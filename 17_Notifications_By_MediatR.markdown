# MediatR в ASP.NET Core

## Зміст
1. [Що таке MediatR?](#що-таке-mediatr)
2. [Основні концепції](#основні-концепції)
3. [Встановлення та налаштування](#встановлення-та-налаштування)
4. [Команд<PERSON> (Commands)](#команди-commands)
5. [Запити (Queries)](#запити-queries)
6. [Нотифікації (Notifications)](#нотифікації-notifications)
7. [Поведінки (Behaviors)](#поведінки-behaviors)
8. [Практичні приклади](#практичні-приклади)
9. [Найкращі практики](#найкращі-практики)
10. [Тестування з MediatR](#тестування-з-mediatr)

## Що таке MediatR?

**MediatR** — це популярна бібліотека для .NET, яка реалізує патерн **Mediator** (Посередник). Цей патерн дозволяє зменшити зв'язність між компонентами системи, забезпечуючи непряму взаємодію через центральний посередник.

### Основні переваги MediatR:

- **Розділення відповідальностей** — кожна операція має свій власний обробник
- **Слабка зв'язність** — компоненти не знають один про одного напряму
- **Легкість тестування** — кожен обробник можна тестувати окремо
- **Чистий код** — контролери стають простішими та зосереджуються лише на HTTP-логіці
- **Повторне використання** — бізнес-логіка може використовуватися в різних місцях

### Коли використовувати MediatR?

✅ **Використовуйте MediatR, коли:**
- Ваша програма має складну бізнес-логіку
- Потрібно розділити HTTP-логіку та бізнес-логіку
- Хочете застосувати CQRS (Command Query Responsibility Segregation)
- Потрібна система нотифікацій між компонентами
- Команда працює над великим проектом

❌ **Не використовуйте MediatR, коли:**
- Програма дуже проста (CRUD операції)
- Команда мала і всі розуміють архітектуру
- Продуктивність критично важлива (MediatR додає невеликий overhead)

## Основні концепції

MediatR працює з трьома основними типами повідомлень:

### 1. Команди (Commands)
Команди змінюють стан системи і не повертають значення (або повертають результат операції).

```csharp
// Команда для створення користувача
public class CreateUserCommand : IRequest<int>
{
    public string Name { get; set; }
    public string Email { get; set; }
}
```

### 2. Запити (Queries)
Запити отримують дані без зміни стану системи.

```csharp
// Запит для отримання користувача
public class GetUserQuery : IRequest<UserDto>
{
    public int Id { get; set; }
}
```

### 3. Нотифікації (Notifications)
Нотифікації дозволяють повідомити кілька обробників про подію.

```csharp
// Нотифікація про створення користувача
public class UserCreatedNotification : INotification
{
    public int UserId { get; set; }
    public string UserName { get; set; }
}
```

## Встановлення та налаштування

### Крок 1: Встановлення пакетів

```bash
# Основний пакет MediatR
dotnet add package MediatR

# Інтеграція з DI контейнером ASP.NET Core
dotnet add package MediatR.Extensions.Microsoft.DependencyInjection
```

### Крок 2: Реєстрація в Program.cs (Minimal API)

```csharp
using MediatR;

var builder = WebApplication.CreateBuilder(args);

// Реєстрація MediatR
builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(Program).Assembly));

// Реєстрація додаткових сервісів
builder.Services.AddScoped<IUserRepository, UserRepository>();
builder.Services.AddScoped<IEmailService, EmailService>();

var app = builder.Build();

// Minimal API endpoints
app.MapPost("/api/users", async (CreateUserCommand command, IMediator mediator) =>
{
    try
    {
        var userId = await mediator.Send(command);
        return Results.Created($"/api/users/{userId}", userId);
    }
    catch (InvalidOperationException ex)
    {
        return Results.BadRequest(ex.Message);
    }
});

app.MapGet("/api/users/{id}", async (int id, IMediator mediator) =>
{
    try
    {
        var user = await mediator.Send(new GetUserQuery { Id = id });
        return Results.Ok(user);
    }
    catch (NotFoundException ex)
    {
        return Results.NotFound(ex.Message);
    }
});

app.MapDelete("/api/users/{id}", async (int id, IMediator mediator) =>
{
    try
    {
        await mediator.Send(new DeleteUserCommand { UserId = id });
        return Results.NoContent();
    }
    catch (NotFoundException ex)
    {
        return Results.NotFound(ex.Message);
    }
});

app.Run();
```

### Крок 3: Структура проекту

Рекомендована структура для організації MediatR компонентів:

```
📁 Features/
  📁 Users/
    📁 Commands/
      📄 CreateUser/
        📄 CreateUserCommand.cs
        📄 CreateUserCommandHandler.cs
        📄 CreateUserCommandValidator.cs
    📁 Queries/
      📄 GetUser/
        📄 GetUserQuery.cs
        📄 GetUserQueryHandler.cs
    📁 Notifications/
      📄 UserCreated/
        📄 UserCreatedNotification.cs
        📄 UserCreatedNotificationHandler.cs
```

## Команди (Commands)

Команди використовуються для операцій, які змінюють стан системи.

### Приклад: Створення користувача

**1. Визначення команди:**

```csharp
using MediatR;

namespace Features.Users.Commands.CreateUser
{
    public class CreateUserCommand : IRequest<int>
    {
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public int Age { get; set; }
    }
}
```

**2. Обробник команди:**

```csharp
using MediatR;

namespace Features.Users.Commands.CreateUser
{
    public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, int>
    {
        private readonly IUserRepository _userRepository;
        private readonly ILogger<CreateUserCommandHandler> _logger;

        public CreateUserCommandHandler(
            IUserRepository userRepository,
            ILogger<CreateUserCommandHandler> logger)
        {
            _userRepository = userRepository;
            _logger = logger;
        }

        public async Task<int> Handle(CreateUserCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Creating user with email: {Email}", request.Email);

            // Перевірка чи користувач вже існує
            var existingUser = await _userRepository.GetByEmailAsync(request.Email);
            if (existingUser != null)
            {
                throw new InvalidOperationException($"User with email {request.Email} already exists");
            }

            // Створення нового користувача
            var user = new User
            {
                Name = request.Name,
                Email = request.Email,
                Age = request.Age,
                CreatedAt = DateTime.UtcNow
            };

            await _userRepository.AddAsync(user);
            await _userRepository.SaveChangesAsync();

            _logger.LogInformation("User created successfully with ID: {UserId}", user.Id);

            return user.Id;
        }
    }
}
```

**3. Використання в Minimal API:**

```csharp
// В Program.cs
app.MapPost("/api/users", async (CreateUserCommand command, IMediator mediator) =>
{
    try
    {
        var userId = await mediator.Send(command);
        return Results.Created($"/api/users/{userId}", userId);
    }
    catch (InvalidOperationException ex)
    {
        return Results.BadRequest(ex.Message);
    }
})
.WithName("CreateUser")
.WithOpenApi();
```

### Команди без повернення значення

Для команд, які не повертають значення, використовуйте `IRequest`:

```csharp
public class DeleteUserCommand : IRequest
{
    public int UserId { get; set; }
}

public class DeleteUserCommandHandler : IRequestHandler<DeleteUserCommand>
{
    private readonly IUserRepository _userRepository;

    public DeleteUserCommandHandler(IUserRepository userRepository)
    {
        _userRepository = userRepository;
    }

    public async Task Handle(DeleteUserCommand request, CancellationToken cancellationToken)
    {
        var user = await _userRepository.GetByIdAsync(request.UserId);
        if (user == null)
        {
            throw new NotFoundException($"User with ID {request.UserId} not found");
        }

        await _userRepository.DeleteAsync(user);
        await _userRepository.SaveChangesAsync();
    }
}
```

## Запити (Queries)

Запити використовуються для отримання даних без зміни стану системи.

### Приклад: Отримання користувача за ID

**1. Визначення запиту:**

```csharp
using MediatR;

namespace Features.Users.Queries.GetUser
{
    public class GetUserQuery : IRequest<UserDto>
    {
        public int Id { get; set; }
    }

    public class UserDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public int Age { get; set; }
        public DateTime CreatedAt { get; set; }
    }
}
```

**2. Обробник запиту:**

```csharp
using MediatR;

namespace Features.Users.Queries.GetUser
{
    public class GetUserQueryHandler : IRequestHandler<GetUserQuery, UserDto>
    {
        private readonly IUserRepository _userRepository;
        private readonly ILogger<GetUserQueryHandler> _logger;

        public GetUserQueryHandler(
            IUserRepository userRepository,
            ILogger<GetUserQueryHandler> logger)
        {
            _userRepository = userRepository;
            _logger = logger;
        }

        public async Task<UserDto> Handle(GetUserQuery request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Getting user with ID: {UserId}", request.Id);

            var user = await _userRepository.GetByIdAsync(request.Id);
            if (user == null)
            {
                throw new NotFoundException($"User with ID {request.Id} not found");
            }

            return new UserDto
            {
                Id = user.Id,
                Name = user.Name,
                Email = user.Email,
                Age = user.Age,
                CreatedAt = user.CreatedAt
            };
        }
    }
}
```

**3. Використання в Minimal API:**

```csharp
app.MapGet("/api/users/{id}", async (int id, IMediator mediator) =>
{
    try
    {
        var user = await mediator.Send(new GetUserQuery { Id = id });
        return Results.Ok(user);
    }
    catch (NotFoundException ex)
    {
        return Results.NotFound(ex.Message);
    }
})
.WithName("GetUser")
.WithOpenApi();
```

### Приклад: Отримання списку користувачів з пагінацією

**1. Запит зі складнішими параметрами:**

```csharp
public class GetUsersQuery : IRequest<PagedResult<UserDto>>
{
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SearchTerm { get; set; }
    public string? SortBy { get; set; } = "Name";
    public bool SortDescending { get; set; } = false;
}

public class PagedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
}
```

**2. Обробник з фільтрацією та сортуванням:**

```csharp
public class GetUsersQueryHandler : IRequestHandler<GetUsersQuery, PagedResult<UserDto>>
{
    private readonly IUserRepository _userRepository;

    public GetUsersQueryHandler(IUserRepository userRepository)
    {
        _userRepository = userRepository;
    }

    public async Task<PagedResult<UserDto>> Handle(GetUsersQuery request, CancellationToken cancellationToken)
    {
        var query = _userRepository.GetQueryable();

        // Фільтрація
        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            query = query.Where(u => u.Name.Contains(request.SearchTerm) ||
                                   u.Email.Contains(request.SearchTerm));
        }

        // Сортування
        query = request.SortBy?.ToLower() switch
        {
            "email" => request.SortDescending ?
                query.OrderByDescending(u => u.Email) :
                query.OrderBy(u => u.Email),
            "age" => request.SortDescending ?
                query.OrderByDescending(u => u.Age) :
                query.OrderBy(u => u.Age),
            "createdat" => request.SortDescending ?
                query.OrderByDescending(u => u.CreatedAt) :
                query.OrderBy(u => u.CreatedAt),
            _ => request.SortDescending ?
                query.OrderByDescending(u => u.Name) :
                query.OrderBy(u => u.Name)
        };

        var totalCount = await query.CountAsync(cancellationToken);

        var users = await query
            .Skip((request.Page - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(u => new UserDto
            {
                Id = u.Id,
                Name = u.Name,
                Email = u.Email,
                Age = u.Age,
                CreatedAt = u.CreatedAt
            })
            .ToListAsync(cancellationToken);

        return new PagedResult<UserDto>
        {
            Items = users,
            TotalCount = totalCount,
            Page = request.Page,
            PageSize = request.PageSize
        };
    }
}
```

## Нотифікації (Notifications)

Нотифікації дозволяють повідомити кілька обробників про подію одночасно. Це корисно для реалізації побічних ефектів.

### Приклад: Нотифікація про створення користувача

**1. Визначення нотифікації:**

```csharp
using MediatR;

namespace Features.Users.Notifications.UserCreated
{
    public class UserCreatedNotification : INotification
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string UserEmail { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
    }
}
```

**2. Кілька обробників нотифікації:**

```csharp
// Обробник для відправки email
public class SendWelcomeEmailHandler : INotificationHandler<UserCreatedNotification>
{
    private readonly IEmailService _emailService;
    private readonly ILogger<SendWelcomeEmailHandler> _logger;

    public SendWelcomeEmailHandler(IEmailService emailService, ILogger<SendWelcomeEmailHandler> logger)
    {
        _emailService = emailService;
        _logger = logger;
    }

    public async Task Handle(UserCreatedNotification notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Sending welcome email to user: {UserEmail}", notification.UserEmail);

        await _emailService.SendWelcomeEmailAsync(
            notification.UserEmail,
            notification.UserName);

        _logger.LogInformation("Welcome email sent successfully to: {UserEmail}", notification.UserEmail);
    }
}

// Обробник для логування
public class LogUserCreationHandler : INotificationHandler<UserCreatedNotification>
{
    private readonly ILogger<LogUserCreationHandler> _logger;

    public LogUserCreationHandler(ILogger<LogUserCreationHandler> logger)
    {
        _logger = logger;
    }

    public async Task Handle(UserCreatedNotification notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("User created: ID={UserId}, Name={UserName}, Email={UserEmail}, CreatedAt={CreatedAt}",
            notification.UserId,
            notification.UserName,
            notification.UserEmail,
            notification.CreatedAt);

        await Task.CompletedTask;
    }
}

// Обробник для оновлення статистики
public class UpdateUserStatisticsHandler : INotificationHandler<UserCreatedNotification>
{
    private readonly IStatisticsService _statisticsService;

    public UpdateUserStatisticsHandler(IStatisticsService statisticsService)
    {
        _statisticsService = statisticsService;
    }

    public async Task Handle(UserCreatedNotification notification, CancellationToken cancellationToken)
    {
        await _statisticsService.IncrementUserCountAsync();
        await _statisticsService.RecordUserRegistrationAsync(notification.CreatedAt);
    }
}
```

**3. Відправка нотифікації з команди:**

```csharp
public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, int>
{
    private readonly IUserRepository _userRepository;
    private readonly IMediator _mediator;
    private readonly ILogger<CreateUserCommandHandler> _logger;

    public CreateUserCommandHandler(
        IUserRepository userRepository,
        IMediator mediator,
        ILogger<CreateUserCommandHandler> logger)
    {
        _userRepository = userRepository;
        _mediator = mediator;
        _logger = logger;
    }

    public async Task<int> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        // Створення користувача
        var user = new User
        {
            Name = request.Name,
            Email = request.Email,
            Age = request.Age,
            CreatedAt = DateTime.UtcNow
        };

        await _userRepository.AddAsync(user);
        await _userRepository.SaveChangesAsync();

        // Відправка нотифікації
        await _mediator.Publish(new UserCreatedNotification
        {
            UserId = user.Id,
            UserName = user.Name,
            UserEmail = user.Email,
            CreatedAt = user.CreatedAt
        }, cancellationToken);

        return user.Id;
    }
}
```

### Обробка помилок в нотифікаціях

Важливо пам'ятати, що якщо один з обробників нотифікації викине виняток, це може вплинути на інші обробники:

```csharp
public class RobustEmailHandler : INotificationHandler<UserCreatedNotification>
{
    private readonly IEmailService _emailService;
    private readonly ILogger<RobustEmailHandler> _logger;

    public RobustEmailHandler(IEmailService emailService, ILogger<RobustEmailHandler> logger)
    {
        _emailService = emailService;
        _logger = logger;
    }

    public async Task Handle(UserCreatedNotification notification, CancellationToken cancellationToken)
    {
        try
        {
            await _emailService.SendWelcomeEmailAsync(
                notification.UserEmail,
                notification.UserName);

            _logger.LogInformation("Welcome email sent to: {UserEmail}", notification.UserEmail);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send welcome email to: {UserEmail}", notification.UserEmail);
            // Не перекидаємо виняток, щоб не зупинити інші обробники
        }
    }
}
```

## Поведінки (Behaviors)

Behaviors дозволяють додати cross-cutting concerns (логування, валідація, кешування тощо) до всіх запитів.

### Встановлення додаткових пакетів

```bash
# Для валідації
dotnet add package FluentValidation
dotnet add package FluentValidation.DependencyInjectionExtensions
```

### Приклад: Behavior для логування

```csharp
using MediatR;
using System.Diagnostics;

public class LoggingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : notnull
{
    private readonly ILogger<LoggingBehavior<TRequest, TResponse>> _logger;

    public LoggingBehavior(ILogger<LoggingBehavior<TRequest, TResponse>> logger)
    {
        _logger = logger;
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;
        var stopwatch = Stopwatch.StartNew();

        _logger.LogInformation("Handling {RequestName}", requestName);

        try
        {
            var response = await next();

            stopwatch.Stop();
            _logger.LogInformation("Handled {RequestName} in {ElapsedMilliseconds}ms",
                requestName, stopwatch.ElapsedMilliseconds);

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error handling {RequestName} after {ElapsedMilliseconds}ms",
                requestName, stopwatch.ElapsedMilliseconds);
            throw;
        }
    }
}
```

### Приклад: Behavior для валідації

```csharp
using FluentValidation;
using MediatR;

public class ValidationBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : notnull
{
    private readonly IEnumerable<IValidator<TRequest>> _validators;

    public ValidationBehavior(IEnumerable<IValidator<TRequest>> validators)
    {
        _validators = validators;
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        if (_validators.Any())
        {
            var context = new ValidationContext<TRequest>(request);

            var validationResults = await Task.WhenAll(
                _validators.Select(v => v.ValidateAsync(context, cancellationToken)));

            var failures = validationResults
                .SelectMany(r => r.Errors)
                .Where(f => f != null)
                .ToList();

            if (failures.Any())
            {
                throw new ValidationException(failures);
            }
        }

        return await next();
    }
}
```

### Приклад валідатора

```csharp
using FluentValidation;

public class CreateUserCommandValidator : AbstractValidator<CreateUserCommand>
{
    public CreateUserCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Name is required")
            .MaximumLength(100).WithMessage("Name must not exceed 100 characters");

        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("Email is required")
            .EmailAddress().WithMessage("Email must be valid")
            .MaximumLength(255).WithMessage("Email must not exceed 255 characters");

        RuleFor(x => x.Age)
            .GreaterThan(0).WithMessage("Age must be greater than 0")
            .LessThan(150).WithMessage("Age must be less than 150");
    }
}
```

### Реєстрація behaviors

```csharp
var builder = WebApplication.CreateBuilder(args);

// Реєстрація MediatR
builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(Program).Assembly));

// Реєстрація behaviors (порядок важливий!)
builder.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(LoggingBehavior<,>));
builder.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));

// Реєстрація валідаторів
builder.Services.AddValidatorsFromAssembly(typeof(Program).Assembly);

var app = builder.Build();
```

## Практичні приклади

### Приклад 1: Система управління завданнями

Розглянемо повний приклад системи управління завданнями (Todo List):

**Модель:**

```csharp
public class TodoItem
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsCompleted { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public int UserId { get; set; }
}
```

**Команди:**

```csharp
// Створення завдання
public class CreateTodoCommand : IRequest<int>
{
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int UserId { get; set; }
}

public class CreateTodoCommandHandler : IRequestHandler<CreateTodoCommand, int>
{
    private readonly ITodoRepository _repository;
    private readonly IMediator _mediator;

    public CreateTodoCommandHandler(ITodoRepository repository, IMediator mediator)
    {
        _repository = repository;
        _mediator = mediator;
    }

    public async Task<int> Handle(CreateTodoCommand request, CancellationToken cancellationToken)
    {
        var todo = new TodoItem
        {
            Title = request.Title,
            Description = request.Description,
            UserId = request.UserId,
            CreatedAt = DateTime.UtcNow,
            IsCompleted = false
        };

        await _repository.AddAsync(todo);
        await _repository.SaveChangesAsync();

        // Відправка нотифікації
        await _mediator.Publish(new TodoCreatedNotification
        {
            TodoId = todo.Id,
            Title = todo.Title,
            UserId = todo.UserId
        }, cancellationToken);

        return todo.Id;
    }
}

// Позначення завдання як виконаного
public class CompleteTodoCommand : IRequest
{
    public int TodoId { get; set; }
    public int UserId { get; set; }
}

public class CompleteTodoCommandHandler : IRequestHandler<CompleteTodoCommand>
{
    private readonly ITodoRepository _repository;
    private readonly IMediator _mediator;

    public CompleteTodoCommandHandler(ITodoRepository repository, IMediator mediator)
    {
        _repository = repository;
        _mediator = mediator;
    }

    public async Task Handle(CompleteTodoCommand request, CancellationToken cancellationToken)
    {
        var todo = await _repository.GetByIdAsync(request.TodoId);
        if (todo == null || todo.UserId != request.UserId)
        {
            throw new NotFoundException("Todo not found");
        }

        if (todo.IsCompleted)
        {
            throw new InvalidOperationException("Todo is already completed");
        }

        todo.IsCompleted = true;
        todo.CompletedAt = DateTime.UtcNow;

        await _repository.UpdateAsync(todo);
        await _repository.SaveChangesAsync();

        await _mediator.Publish(new TodoCompletedNotification
        {
            TodoId = todo.Id,
            Title = todo.Title,
            UserId = todo.UserId,
            CompletedAt = todo.CompletedAt.Value
        }, cancellationToken);
    }
}
```

**Запити:**

```csharp
// Отримання завдань користувача
public class GetUserTodosQuery : IRequest<List<TodoDto>>
{
    public int UserId { get; set; }
    public bool? IsCompleted { get; set; }
}

public class TodoDto
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsCompleted { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
}

public class GetUserTodosQueryHandler : IRequestHandler<GetUserTodosQuery, List<TodoDto>>
{
    private readonly ITodoRepository _repository;

    public GetUserTodosQueryHandler(ITodoRepository repository)
    {
        _repository = repository;
    }

    public async Task<List<TodoDto>> Handle(GetUserTodosQuery request, CancellationToken cancellationToken)
    {
        var todos = await _repository.GetUserTodosAsync(request.UserId, request.IsCompleted);

        return todos.Select(t => new TodoDto
        {
            Id = t.Id,
            Title = t.Title,
            Description = t.Description,
            IsCompleted = t.IsCompleted,
            CreatedAt = t.CreatedAt,
            CompletedAt = t.CompletedAt
        }).ToList();
    }
}
```

**Нотифікації:**

```csharp
public class TodoCreatedNotification : INotification
{
    public int TodoId { get; set; }
    public string Title { get; set; } = string.Empty;
    public int UserId { get; set; }
}

public class TodoCompletedNotification : INotification
{
    public int TodoId { get; set; }
    public string Title { get; set; } = string.Empty;
    public int UserId { get; set; }
    public DateTime CompletedAt { get; set; }
}

// Обробник для оновлення статистики
public class TodoStatisticsHandler :
    INotificationHandler<TodoCreatedNotification>,
    INotificationHandler<TodoCompletedNotification>
{
    private readonly IStatisticsService _statisticsService;

    public TodoStatisticsHandler(IStatisticsService statisticsService)
    {
        _statisticsService = statisticsService;
    }

    public async Task Handle(TodoCreatedNotification notification, CancellationToken cancellationToken)
    {
        await _statisticsService.IncrementTodoCountAsync(notification.UserId);
    }

    public async Task Handle(TodoCompletedNotification notification, CancellationToken cancellationToken)
    {
        await _statisticsService.IncrementCompletedTodoCountAsync(notification.UserId);
    }
}
```

**Minimal API endpoints:**

```csharp
// В Program.cs

// Група endpoints для Todo
var todosGroup = app.MapGroup("/api/todos").WithTags("Todos");

todosGroup.MapPost("/", async (CreateTodoCommand command, IMediator mediator, HttpContext context) =>
{
    // В реальному додатку UserId отримується з токена автентифікації
    command.UserId = GetCurrentUserId(context);

    var todoId = await mediator.Send(command);
    return Results.Created($"/api/todos/user/{command.UserId}", todoId);
})
.WithName("CreateTodo")
.WithOpenApi();

todosGroup.MapGet("/user/{userId}", async (int userId, IMediator mediator, bool? isCompleted = null) =>
{
    var todos = await mediator.Send(new GetUserTodosQuery
    {
        UserId = userId,
        IsCompleted = isCompleted
    });
    return Results.Ok(todos);
})
.WithName("GetUserTodos")
.WithOpenApi();

todosGroup.MapPut("/{id}/complete", async (int id, IMediator mediator, HttpContext context) =>
{
    await mediator.Send(new CompleteTodoCommand
    {
        TodoId = id,
        UserId = GetCurrentUserId(context)
    });
    return Results.NoContent();
})
.WithName("CompleteTodo")
.WithOpenApi();

// Допоміжна функція
static int GetCurrentUserId(HttpContext context)
{
    // В реальному додатку це отримується з JWT токена
    // var userId = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
    return 1;
}
```
```

## Найкращі практики

### 1. Організація коду

**✅ Рекомендується:**

```csharp
// Групуйте пов'язані компоненти разом
📁 Features/
  📁 Users/
    📁 Commands/
      📁 CreateUser/
        📄 CreateUserCommand.cs
        📄 CreateUserCommandHandler.cs
        📄 CreateUserCommandValidator.cs
    📁 Queries/
      📁 GetUser/
        📄 GetUserQuery.cs
        📄 GetUserQueryHandler.cs
```

**❌ Не рекомендується:**

```csharp
// Розділення по типах компонентів
📁 Commands/
  📄 CreateUserCommand.cs
  📄 CreateTodoCommand.cs
📁 Handlers/
  📄 CreateUserCommandHandler.cs
  📄 CreateTodoCommandHandler.cs
```

### 2. Іменування

**✅ Правильно:**

```csharp
public class CreateUserCommand : IRequest<int> { }
public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, int> { }
public class GetUserQuery : IRequest<UserDto> { }
public class GetUserQueryHandler : IRequestHandler<GetUserQuery, UserDto> { }
public class UserCreatedNotification : INotification { }
```

**❌ Неправильно:**

```csharp
public class User : IRequest<int> { } // Незрозуміло, що це команда
public class UserHandler : IRequestHandler<User, int> { } // Незрозуміло, що робить
```

### 3. Розділення відповідальностей

**✅ Правильно:**

```csharp
public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, int>
{
    public async Task<int> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        // Тільки бізнес-логіка створення користувача
        var user = new User { /* ... */ };
        await _repository.AddAsync(user);
        await _repository.SaveChangesAsync();

        // Відправка нотифікації для побічних ефектів
        await _mediator.Publish(new UserCreatedNotification { /* ... */ });

        return user.Id;
    }
}
```

**❌ Неправильно:**

```csharp
public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, int>
{
    public async Task<int> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        var user = new User { /* ... */ };
        await _repository.AddAsync(user);

        // Не робіть це в обробнику команди!
        await _emailService.SendWelcomeEmail(user.Email);
        await _statisticsService.UpdateUserCount();
        await _auditService.LogUserCreation(user.Id);

        return user.Id;
    }
}
```

### 4. Обробка помилок

**✅ Використовуйте специфічні винятки:**

```csharp
public class NotFoundException : Exception
{
    public NotFoundException(string message) : base(message) { }
}

public class ValidationException : Exception
{
    public ValidationException(string message) : base(message) { }
}

public class BusinessRuleException : Exception
{
    public BusinessRuleException(string message) : base(message) { }
}
```

**✅ Глобальний обробник помилок:**

```csharp
public class GlobalExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionMiddleware> _logger;

    public GlobalExceptionMiddleware(RequestDelegate next, ILogger<GlobalExceptionMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unhandled exception occurred");
            await HandleExceptionAsync(context, ex);
        }
    }

    private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.ContentType = "application/json";

        var response = exception switch
        {
            NotFoundException => new { error = exception.Message, statusCode = 404 },
            ValidationException => new { error = exception.Message, statusCode = 400 },
            BusinessRuleException => new { error = exception.Message, statusCode = 422 },
            _ => new { error = "An error occurred", statusCode = 500 }
        };

        context.Response.StatusCode = response.statusCode;
        await context.Response.WriteAsync(JsonSerializer.Serialize(response));
    }
}
```

### 5. Валідація

**✅ Використовуйте FluentValidation:**

```csharp
public class CreateUserCommandValidator : AbstractValidator<CreateUserCommand>
{
    private readonly IUserRepository _userRepository;

    public CreateUserCommandValidator(IUserRepository userRepository)
    {
        _userRepository = userRepository;

        RuleFor(x => x.Email)
            .NotEmpty()
            .EmailAddress()
            .MustAsync(BeUniqueEmail).WithMessage("Email already exists");

        RuleFor(x => x.Name)
            .NotEmpty()
            .MaximumLength(100);
    }

    private async Task<bool> BeUniqueEmail(string email, CancellationToken cancellationToken)
    {
        var existingUser = await _userRepository.GetByEmailAsync(email);
        return existingUser == null;
    }
}
```

### 6. Кешування

**Приклад behavior для кешування:**

```csharp
public class CachingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>, ICacheableRequest
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<CachingBehavior<TRequest, TResponse>> _logger;

    public CachingBehavior(IMemoryCache cache, ILogger<CachingBehavior<TRequest, TResponse>> logger)
    {
        _cache = cache;
        _logger = logger;
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var cacheKey = request.CacheKey;

        if (_cache.TryGetValue(cacheKey, out TResponse cachedResponse))
        {
            _logger.LogInformation("Cache hit for {CacheKey}", cacheKey);
            return cachedResponse;
        }

        _logger.LogInformation("Cache miss for {CacheKey}", cacheKey);
        var response = await next();

        _cache.Set(cacheKey, response, TimeSpan.FromMinutes(request.CacheExpirationMinutes));

        return response;
    }
}

public interface ICacheableRequest
{
    string CacheKey { get; }
    int CacheExpirationMinutes { get; }
}

public class GetUserQuery : IRequest<UserDto>, ICacheableRequest
{
    public int Id { get; set; }
    public string CacheKey => $"user-{Id}";
    public int CacheExpirationMinutes => 30;
}
```

## Тестування з MediatR

### Встановлення пакетів для тестування

```bash
dotnet add package Microsoft.NET.Test.Sdk
dotnet add package xunit
dotnet add package xunit.runner.visualstudio
dotnet add package Moq
dotnet add package FluentAssertions
```

### 1. Тестування обробників команд

```csharp
using Xunit;
using Moq;
using FluentAssertions;

public class CreateUserCommandHandlerTests
{
    private readonly Mock<IUserRepository> _mockRepository;
    private readonly Mock<IMediator> _mockMediator;
    private readonly Mock<ILogger<CreateUserCommandHandler>> _mockLogger;
    private readonly CreateUserCommandHandler _handler;

    public CreateUserCommandHandlerTests()
    {
        _mockRepository = new Mock<IUserRepository>();
        _mockMediator = new Mock<IMediator>();
        _mockLogger = new Mock<ILogger<CreateUserCommandHandler>>();
        _handler = new CreateUserCommandHandler(_mockRepository.Object, _mockMediator.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_ValidCommand_ShouldCreateUserAndReturnId()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Name = "John Doe",
            Email = "<EMAIL>",
            Age = 30
        };

        var expectedUserId = 123;
        _mockRepository.Setup(r => r.GetByEmailAsync(command.Email))
                      .ReturnsAsync((User)null);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<User>()))
                      .Callback<User>(u => u.Id = expectedUserId);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().Be(expectedUserId);

        _mockRepository.Verify(r => r.AddAsync(It.Is<User>(u =>
            u.Name == command.Name &&
            u.Email == command.Email &&
            u.Age == command.Age)), Times.Once);

        _mockRepository.Verify(r => r.SaveChangesAsync(), Times.Once);

        _mockMediator.Verify(m => m.Publish(It.Is<UserCreatedNotification>(n =>
            n.UserId == expectedUserId &&
            n.UserName == command.Name &&
            n.UserEmail == command.Email),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ExistingEmail_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Name = "John Doe",
            Email = "<EMAIL>",
            Age = 30
        };

        var existingUser = new User { Id = 1, Email = command.Email };
        _mockRepository.Setup(r => r.GetByEmailAsync(command.Email))
                      .ReturnsAsync(existingUser);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.Message.Should().Contain("already exists");

        _mockRepository.Verify(r => r.AddAsync(It.IsAny<User>()), Times.Never);
        _mockRepository.Verify(r => r.SaveChangesAsync(), Times.Never);
        _mockMediator.Verify(m => m.Publish(It.IsAny<UserCreatedNotification>(),
            It.IsAny<CancellationToken>()), Times.Never);
    }
}
```

### 2. Тестування обробників запитів

```csharp
public class GetUserQueryHandlerTests
{
    private readonly Mock<IUserRepository> _mockRepository;
    private readonly Mock<ILogger<GetUserQueryHandler>> _mockLogger;
    private readonly GetUserQueryHandler _handler;

    public GetUserQueryHandlerTests()
    {
        _mockRepository = new Mock<IUserRepository>();
        _mockLogger = new Mock<ILogger<GetUserQueryHandler>>();
        _handler = new GetUserQueryHandler(_mockRepository.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_ExistingUser_ShouldReturnUserDto()
    {
        // Arrange
        var userId = 123;
        var user = new User
        {
            Id = userId,
            Name = "John Doe",
            Email = "<EMAIL>",
            Age = 30,
            CreatedAt = DateTime.UtcNow
        };

        _mockRepository.Setup(r => r.GetByIdAsync(userId))
                      .ReturnsAsync(user);

        var query = new GetUserQuery { Id = userId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(user.Id);
        result.Name.Should().Be(user.Name);
        result.Email.Should().Be(user.Email);
        result.Age.Should().Be(user.Age);
        result.CreatedAt.Should().Be(user.CreatedAt);
    }

    [Fact]
    public async Task Handle_NonExistingUser_ShouldThrowNotFoundException()
    {
        // Arrange
        var userId = 999;
        _mockRepository.Setup(r => r.GetByIdAsync(userId))
                      .ReturnsAsync((User)null);

        var query = new GetUserQuery { Id = userId };

        // Act & Assert
        var exception = await Assert.ThrowsAsync<NotFoundException>(
            () => _handler.Handle(query, CancellationToken.None));

        exception.Message.Should().Contain($"User with ID {userId} not found");
    }
}
```

### 3. Тестування обробників нотифікацій

```csharp
public class SendWelcomeEmailHandlerTests
{
    private readonly Mock<IEmailService> _mockEmailService;
    private readonly Mock<ILogger<SendWelcomeEmailHandler>> _mockLogger;
    private readonly SendWelcomeEmailHandler _handler;

    public SendWelcomeEmailHandlerTests()
    {
        _mockEmailService = new Mock<IEmailService>();
        _mockLogger = new Mock<ILogger<SendWelcomeEmailHandler>>();
        _handler = new SendWelcomeEmailHandler(_mockEmailService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_ValidNotification_ShouldSendEmail()
    {
        // Arrange
        var notification = new UserCreatedNotification
        {
            UserId = 123,
            UserName = "John Doe",
            UserEmail = "<EMAIL>",
            CreatedAt = DateTime.UtcNow
        };

        // Act
        await _handler.Handle(notification, CancellationToken.None);

        // Assert
        _mockEmailService.Verify(s => s.SendWelcomeEmailAsync(
            notification.UserEmail,
            notification.UserName), Times.Once);
    }

    [Fact]
    public async Task Handle_EmailServiceThrows_ShouldNotThrow()
    {
        // Arrange
        var notification = new UserCreatedNotification
        {
            UserId = 123,
            UserName = "John Doe",
            UserEmail = "<EMAIL>",
            CreatedAt = DateTime.UtcNow
        };

        _mockEmailService.Setup(s => s.SendWelcomeEmailAsync(It.IsAny<string>(), It.IsAny<string>()))
                        .ThrowsAsync(new Exception("Email service error"));

        // Act & Assert
        // Обробник не повинен кидати виняток, щоб не зупинити інші обробники
        await _handler.Handle(notification, CancellationToken.None);

        _mockEmailService.Verify(s => s.SendWelcomeEmailAsync(
            notification.UserEmail,
            notification.UserName), Times.Once);
    }
}
```

### 4. Інтеграційні тести

```csharp
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using System.Net.Http.Json;

public class UsersControllerIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public UsersControllerIntegrationTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task CreateUser_ValidCommand_ShouldReturnCreatedUser()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Name = "Integration Test User",
            Email = "<EMAIL>",
            Age = 25
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/users", command);

        // Assert
        response.Should().HaveStatusCode(HttpStatusCode.Created);

        var userId = await response.Content.ReadFromJsonAsync<int>();
        userId.Should().BeGreaterThan(0);

        // Перевірка, що користувач дійсно створений
        var getUserResponse = await _client.GetAsync($"/api/users/{userId}");
        getUserResponse.Should().HaveStatusCode(HttpStatusCode.OK);

        var user = await getUserResponse.Content.ReadFromJsonAsync<UserDto>();
        user.Should().NotBeNull();
        user.Name.Should().Be(command.Name);
        user.Email.Should().Be(command.Email);
        user.Age.Should().Be(command.Age);
    }

    [Fact]
    public async Task CreateUser_DuplicateEmail_ShouldReturnBadRequest()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Name = "Test User",
            Email = "<EMAIL>",
            Age = 30
        };

        // Створюємо першого користувача
        await _client.PostAsJsonAsync("/api/users", command);

        // Act - спробуємо створити другого з тим же email
        var response = await _client.PostAsJsonAsync("/api/users", command);

        // Assert
        response.Should().HaveStatusCode(HttpStatusCode.BadRequest);
    }
}
```

### 5. Тестування behaviors

```csharp
public class ValidationBehaviorTests
{
    [Fact]
    public async Task Handle_ValidRequest_ShouldCallNext()
    {
        // Arrange
        var validators = new List<IValidator<CreateUserCommand>>();
        var behavior = new ValidationBehavior<CreateUserCommand, int>(validators);

        var request = new CreateUserCommand
        {
            Name = "John Doe",
            Email = "<EMAIL>",
            Age = 30
        };

        var nextCalled = false;
        Task<int> Next()
        {
            nextCalled = true;
            return Task.FromResult(123);
        }

        // Act
        var result = await behavior.Handle(request, Next, CancellationToken.None);

        // Assert
        nextCalled.Should().BeTrue();
        result.Should().Be(123);
    }

    [Fact]
    public async Task Handle_InvalidRequest_ShouldThrowValidationException()
    {
        // Arrange
        var validator = new Mock<IValidator<CreateUserCommand>>();
        validator.Setup(v => v.ValidateAsync(It.IsAny<ValidationContext<CreateUserCommand>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ValidationResult(new[]
                {
                    new ValidationFailure("Name", "Name is required")
                }));

        var validators = new List<IValidator<CreateUserCommand>> { validator.Object };
        var behavior = new ValidationBehavior<CreateUserCommand, int>(validators);

        var request = new CreateUserCommand();

        Task<int> Next() => Task.FromResult(123);

        // Act & Assert
        await Assert.ThrowsAsync<ValidationException>(
            () => behavior.Handle(request, Next, CancellationToken.None));
    }
}
```

## Поширені помилки та як їх уникнути

### 1. Занадто багато логіки в контролерах

**❌ Неправильно:**

```csharp
[HttpPost]
public async Task<IActionResult> CreateUser([FromBody] CreateUserRequest request)
{
    // Валідація
    if (string.IsNullOrEmpty(request.Name))
        return BadRequest("Name is required");

    if (string.IsNullOrEmpty(request.Email))
        return BadRequest("Email is required");

    // Перевірка унікальності
    var existingUser = await _userRepository.GetByEmailAsync(request.Email);
    if (existingUser != null)
        return BadRequest("User already exists");

    // Створення користувача
    var user = new User { /* ... */ };
    await _userRepository.AddAsync(user);
    await _userRepository.SaveChangesAsync();

    // Відправка email
    await _emailService.SendWelcomeEmail(user.Email, user.Name);

    return Ok(user.Id);
}
```

**✅ Правильно:**

```csharp
[HttpPost]
public async Task<IActionResult> CreateUser([FromBody] CreateUserCommand command)
{
    var userId = await _mediator.Send(command);
    return CreatedAtAction(nameof(GetUser), new { id = userId }, userId);
}
```

### 2. Порушення принципу єдиної відповідальності

**❌ Неправильно:**

```csharp
public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, int>
{
    public async Task<int> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        // Створення користувача
        var user = new User { /* ... */ };
        await _repository.AddAsync(user);

        // Відправка email (не повинно бути тут!)
        await _emailService.SendWelcomeEmail(user.Email);

        // Оновлення статистики (не повинно бути тут!)
        await _statisticsService.UpdateUserCount();

        return user.Id;
    }
}
```

**✅ Правильно:**

```csharp
public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, int>
{
    public async Task<int> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        var user = new User { /* ... */ };
        await _repository.AddAsync(user);
        await _repository.SaveChangesAsync();

        // Відправка нотифікації для побічних ефектів
        await _mediator.Publish(new UserCreatedNotification { /* ... */ });

        return user.Id;
    }
}
```

### 3. Неправильне використання нотифікацій

**❌ Неправильно:**

```csharp
// Використання нотифікації для отримання даних
public class GetUserNotification : INotification
{
    public int UserId { get; set; }
    public UserDto Result { get; set; } // Неправильно!
}
```

**✅ Правильно:**

```csharp
// Використовуйте запити для отримання даних
public class GetUserQuery : IRequest<UserDto>
{
    public int UserId { get; set; }
}

// Нотифікації тільки для повідомлення про події
public class UserViewedNotification : INotification
{
    public int UserId { get; set; }
    public DateTime ViewedAt { get; set; }
}
```

## Багатоканальна система нотифікацій

Створимо потужну систему нотифікацій з підтримкою множинних каналів доставки.

### Архітектура системи

```mermaid
graph TD
    A[Notification Event] --> B[MediatR Publisher]
    B --> C[Notification Handler]
    C --> D[Notification Manager]
    D --> E[Channel Resolver]
    E --> F[Email Channel]
    E --> G[SMS Channel]
    E --> H[Telegram Channel]
    E --> I[Push Channel]
    E --> J[Database Channel]
    E --> K[Slack Channel]

    F --> L[Email Provider]
    G --> M[SMS Provider]
    H --> N[Telegram Bot API]
    I --> O[Push Service]
    J --> P[Database]
    K --> Q[Slack API]
```

### Основні компоненти

#### 1. Базові інтерфейси та абстракції

```csharp
// Базовий інтерфейс для всіх нотифікацій
public interface INotifiable
{
    int Id { get; }
    string Email { get; }
    string? PhoneNumber { get; }
    string? TelegramChatId { get; }
    string? PushToken { get; }
    Dictionary<string, object> NotificationPreferences { get; }
}

// Інтерфейс для каналу доставки
public interface INotificationChannel
{
    string Name { get; }
    Task<NotificationResult> SendAsync(INotifiable recipient, INotificationMessage message, CancellationToken cancellationToken = default);
    bool CanHandle(INotifiable recipient, INotificationMessage message);
}

// Базовий інтерфейс для повідомлення
public interface INotificationMessage
{
    string Subject { get; }
    string Content { get; }
    NotificationPriority Priority { get; }
    Dictionary<string, object> Data { get; }
    List<string> Channels { get; }
}

// Результат відправки нотифікації
public class NotificationResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ExternalId { get; set; }
    public DateTime SentAt { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

// Пріоритет нотифікації
public enum NotificationPriority
{
    Low = 1,
    Normal = 2,
    High = 3,
    Critical = 4
}
```

#### 2. Базова нотифікація

```csharp
public abstract class BaseNotification : INotificationMessage
{
    public abstract string Subject { get; }
    public abstract string Content { get; }
    public virtual NotificationPriority Priority => NotificationPriority.Normal;
    public virtual Dictionary<string, object> Data { get; } = new();
    public virtual List<string> Channels { get; } = new();

    // Методи для налаштування контенту для різних каналів
    public virtual string ToEmail(INotifiable recipient) => Content;
    public virtual string ToSms(INotifiable recipient) => Content;
    public virtual string ToTelegram(INotifiable recipient) => Content;
    public virtual object ToPush(INotifiable recipient) => new { title = Subject, body = Content };
    public virtual object ToSlack(INotifiable recipient) => new { text = $"*{Subject}*\n{Content}" };

    // Визначення каналів для конкретного отримувача
    public virtual List<string> GetChannelsFor(INotifiable recipient)
    {
        if (Channels.Any()) return Channels;

        var channels = new List<string>();

        // Автоматичне визначення доступних каналів
        if (!string.IsNullOrEmpty(recipient.Email)) channels.Add("email");
        if (!string.IsNullOrEmpty(recipient.PhoneNumber)) channels.Add("sms");
        if (!string.IsNullOrEmpty(recipient.TelegramChatId)) channels.Add("telegram");
        if (!string.IsNullOrEmpty(recipient.PushToken)) channels.Add("push");

        return channels;
    }
}
```

#### 3. Менеджер нотифікацій

```csharp
public interface INotificationManager
{
    Task SendAsync<T>(T notification, INotifiable recipient, CancellationToken cancellationToken = default)
        where T : INotificationMessage;
    Task SendAsync<T>(T notification, IEnumerable<INotifiable> recipients, CancellationToken cancellationToken = default)
        where T : INotificationMessage;
    Task SendViaChannelAsync<T>(T notification, INotifiable recipient, string channel, CancellationToken cancellationToken = default)
        where T : INotificationMessage;
}

public class NotificationManager : INotificationManager
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<NotificationManager> _logger;
    private readonly INotificationStorage _storage;
    private readonly Dictionary<string, Type> _channels;

    public NotificationManager(
        IServiceProvider serviceProvider,
        ILogger<NotificationManager> logger,
        INotificationStorage storage)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _storage = storage;
        _channels = new Dictionary<string, Type>();

        RegisterDefaultChannels();
    }

    private void RegisterDefaultChannels()
    {
        _channels["email"] = typeof(EmailNotificationChannel);
        _channels["sms"] = typeof(SmsNotificationChannel);
        _channels["telegram"] = typeof(TelegramNotificationChannel);
        _channels["push"] = typeof(PushNotificationChannel);
        _channels["database"] = typeof(DatabaseNotificationChannel);
        _channels["slack"] = typeof(SlackNotificationChannel);
    }

    public async Task SendAsync<T>(T notification, INotifiable recipient, CancellationToken cancellationToken = default)
        where T : INotificationMessage
    {
        var channels = notification.GetChannelsFor(recipient);
        var tasks = channels.Select(channel => SendViaChannelAsync(notification, recipient, channel, cancellationToken));

        await Task.WhenAll(tasks);
    }

    public async Task SendAsync<T>(T notification, IEnumerable<INotifiable> recipients, CancellationToken cancellationToken = default)
        where T : INotificationMessage
    {
        var tasks = recipients.Select(recipient => SendAsync(notification, recipient, cancellationToken));
        await Task.WhenAll(tasks);
    }

    public async Task SendViaChannelAsync<T>(T notification, INotifiable recipient, string channel, CancellationToken cancellationToken = default)
        where T : INotificationMessage
    {
        try
        {
            if (!_channels.TryGetValue(channel, out var channelType))
            {
                _logger.LogWarning("Unknown notification channel: {Channel}", channel);
                return;
            }

            var channelInstance = (INotificationChannel)_serviceProvider.GetRequiredService(channelType);

            if (!channelInstance.CanHandle(recipient, notification))
            {
                _logger.LogInformation("Channel {Channel} cannot handle notification for recipient {RecipientId}",
                    channel, recipient.Id);
                return;
            }

            var result = await channelInstance.SendAsync(recipient, notification, cancellationToken);

            // Зберігаємо результат в базі даних
            await _storage.StoreNotificationAsync(new NotificationRecord
            {
                RecipientId = recipient.Id,
                Channel = channel,
                Subject = notification.Subject,
                Content = notification.Content,
                Priority = notification.Priority,
                IsSuccess = result.IsSuccess,
                ErrorMessage = result.ErrorMessage,
                ExternalId = result.ExternalId,
                SentAt = result.SentAt,
                Metadata = result.Metadata
            });

            if (result.IsSuccess)
            {
                _logger.LogInformation("Notification sent successfully via {Channel} to recipient {RecipientId}",
                    channel, recipient.Id);
            }
            else
            {
                _logger.LogError("Failed to send notification via {Channel} to recipient {RecipientId}: {Error}",
                    channel, recipient.Id, result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification via {Channel} to recipient {RecipientId}",
                channel, recipient.Id);
        }
    }
}
```

#### 4. Реалізація каналів нотифікацій

**Email канал:**

```csharp
public class EmailNotificationChannel : INotificationChannel
{
    public string Name => "email";

    private readonly IEmailService _emailService;
    private readonly ILogger<EmailNotificationChannel> _logger;

    public EmailNotificationChannel(IEmailService emailService, ILogger<EmailNotificationChannel> logger)
    {
        _emailService = emailService;
        _logger = logger;
    }

    public bool CanHandle(INotifiable recipient, INotificationMessage message)
    {
        return !string.IsNullOrEmpty(recipient.Email);
    }

    public async Task<NotificationResult> SendAsync(INotifiable recipient, INotificationMessage message, CancellationToken cancellationToken = default)
    {
        try
        {
            var content = message is BaseNotification baseNotification
                ? baseNotification.ToEmail(recipient)
                : message.Content;

            var emailMessage = new EmailMessage
            {
                To = recipient.Email,
                Subject = message.Subject,
                Body = content,
                IsHtml = true,
                Priority = MapPriority(message.Priority)
            };

            var messageId = await _emailService.SendAsync(emailMessage, cancellationToken);

            return new NotificationResult
            {
                IsSuccess = true,
                ExternalId = messageId,
                SentAt = DateTime.UtcNow,
                Metadata = new Dictionary<string, object>
                {
                    ["email"] = recipient.Email,
                    ["subject"] = message.Subject
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email notification to {Email}", recipient.Email);

            return new NotificationResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                SentAt = DateTime.UtcNow
            };
        }
    }

    private EmailPriority MapPriority(NotificationPriority priority)
    {
        return priority switch
        {
            NotificationPriority.Critical => EmailPriority.High,
            NotificationPriority.High => EmailPriority.High,
            NotificationPriority.Normal => EmailPriority.Normal,
            NotificationPriority.Low => EmailPriority.Low,
            _ => EmailPriority.Normal
        };
    }
}
```

**SMS канал:**

```csharp
public class SmsNotificationChannel : INotificationChannel
{
    public string Name => "sms";

    private readonly ISmsService _smsService;
    private readonly ILogger<SmsNotificationChannel> _logger;

    public SmsNotificationChannel(ISmsService smsService, ILogger<SmsNotificationChannel> logger)
    {
        _smsService = smsService;
        _logger = logger;
    }

    public bool CanHandle(INotifiable recipient, INotificationMessage message)
    {
        return !string.IsNullOrEmpty(recipient.PhoneNumber);
    }

    public async Task<NotificationResult> SendAsync(INotifiable recipient, INotificationMessage message, CancellationToken cancellationToken = default)
    {
        try
        {
            var content = message is BaseNotification baseNotification
                ? baseNotification.ToSms(recipient)
                : message.Content;

            // Обмежуємо довжину SMS
            if (content.Length > 160)
            {
                content = content.Substring(0, 157) + "...";
            }

            var smsMessage = new SmsMessage
            {
                To = recipient.PhoneNumber,
                Body = content
            };

            var messageId = await _smsService.SendAsync(smsMessage, cancellationToken);

            return new NotificationResult
            {
                IsSuccess = true,
                ExternalId = messageId,
                SentAt = DateTime.UtcNow,
                Metadata = new Dictionary<string, object>
                {
                    ["phone"] = recipient.PhoneNumber,
                    ["length"] = content.Length
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send SMS notification to {Phone}", recipient.PhoneNumber);

            return new NotificationResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                SentAt = DateTime.UtcNow
            };
        }
    }
}
```

**Telegram канал:**

```csharp
public class TelegramNotificationChannel : INotificationChannel
{
    public string Name => "telegram";

    private readonly ITelegramBotService _telegramService;
    private readonly ILogger<TelegramNotificationChannel> _logger;

    public TelegramNotificationChannel(ITelegramBotService telegramService, ILogger<TelegramNotificationChannel> logger)
    {
        _telegramService = telegramService;
        _logger = logger;
    }

    public bool CanHandle(INotifiable recipient, INotificationMessage message)
    {
        return !string.IsNullOrEmpty(recipient.TelegramChatId);
    }

    public async Task<NotificationResult> SendAsync(INotifiable recipient, INotificationMessage message, CancellationToken cancellationToken = default)
    {
        try
        {
            var content = message is BaseNotification baseNotification
                ? baseNotification.ToTelegram(recipient)
                : $"*{message.Subject}*\n\n{message.Content}";

            var telegramMessage = new TelegramMessage
            {
                ChatId = recipient.TelegramChatId,
                Text = content,
                ParseMode = "Markdown"
            };

            var messageId = await _telegramService.SendMessageAsync(telegramMessage, cancellationToken);

            return new NotificationResult
            {
                IsSuccess = true,
                ExternalId = messageId.ToString(),
                SentAt = DateTime.UtcNow,
                Metadata = new Dictionary<string, object>
                {
                    ["chatId"] = recipient.TelegramChatId,
                    ["parseMode"] = "Markdown"
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send Telegram notification to {ChatId}", recipient.TelegramChatId);

            return new NotificationResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                SentAt = DateTime.UtcNow
            };
        }
    }
}
```

**Push нотифікації канал:**

```csharp
public class PushNotificationChannel : INotificationChannel
{
    public string Name => "push";

    private readonly IPushNotificationService _pushService;
    private readonly ILogger<PushNotificationChannel> _logger;

    public PushNotificationChannel(IPushNotificationService pushService, ILogger<PushNotificationChannel> logger)
    {
        _pushService = pushService;
        _logger = logger;
    }

    public bool CanHandle(INotifiable recipient, INotificationMessage message)
    {
        return !string.IsNullOrEmpty(recipient.PushToken);
    }

    public async Task<NotificationResult> SendAsync(INotifiable recipient, INotificationMessage message, CancellationToken cancellationToken = default)
    {
        try
        {
            var pushData = message is BaseNotification baseNotification
                ? baseNotification.ToPush(recipient)
                : new { title = message.Subject, body = message.Content };

            var pushMessage = new PushMessage
            {
                Token = recipient.PushToken,
                Title = message.Subject,
                Body = message.Content,
                Data = message.Data,
                Priority = MapPushPriority(message.Priority)
            };

            var messageId = await _pushService.SendAsync(pushMessage, cancellationToken);

            return new NotificationResult
            {
                IsSuccess = true,
                ExternalId = messageId,
                SentAt = DateTime.UtcNow,
                Metadata = new Dictionary<string, object>
                {
                    ["token"] = recipient.PushToken,
                    ["platform"] = "fcm"
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send push notification to token {Token}",
                recipient.PushToken?.Substring(0, 10) + "...");

            return new NotificationResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                SentAt = DateTime.UtcNow
            };
        }
    }

    private PushPriority MapPushPriority(NotificationPriority priority)
    {
        return priority switch
        {
            NotificationPriority.Critical => PushPriority.High,
            NotificationPriority.High => PushPriority.High,
            _ => PushPriority.Normal
        };
    }
}
```

**Database канал (для зберігання в додатку):**

```csharp
public class DatabaseNotificationChannel : INotificationChannel
{
    public string Name => "database";

    private readonly INotificationRepository _repository;
    private readonly ILogger<DatabaseNotificationChannel> _logger;

    public DatabaseNotificationChannel(INotificationRepository repository, ILogger<DatabaseNotificationChannel> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    public bool CanHandle(INotifiable recipient, INotificationMessage message)
    {
        return true; // Database канал завжди доступний
    }

    public async Task<NotificationResult> SendAsync(INotifiable recipient, INotificationMessage message, CancellationToken cancellationToken = default)
    {
        try
        {
            var notification = new DatabaseNotification
            {
                RecipientId = recipient.Id,
                Subject = message.Subject,
                Content = message.Content,
                Priority = message.Priority,
                Data = message.Data,
                CreatedAt = DateTime.UtcNow,
                IsRead = false
            };

            await _repository.AddAsync(notification);
            await _repository.SaveChangesAsync();

            return new NotificationResult
            {
                IsSuccess = true,
                ExternalId = notification.Id.ToString(),
                SentAt = DateTime.UtcNow,
                Metadata = new Dictionary<string, object>
                {
                    ["recipientId"] = recipient.Id,
                    ["notificationId"] = notification.Id
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save notification to database for recipient {RecipientId}", recipient.Id);

            return new NotificationResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                SentAt = DateTime.UtcNow
            };
        }
    }
}
```

#### 5. Інтеграція з MediatR

**Базовий обробник нотифікацій:**

```csharp
public abstract class NotificationHandler<TNotification> : INotificationHandler<TNotification>
    where TNotification : INotification
{
    protected readonly INotificationManager _notificationManager;
    protected readonly ILogger _logger;

    protected NotificationHandler(INotificationManager notificationManager, ILogger logger)
    {
        _notificationManager = notificationManager;
        _logger = logger;
    }

    public async Task Handle(TNotification notification, CancellationToken cancellationToken)
    {
        try
        {
            var recipients = await GetRecipientsAsync(notification);
            var notificationMessage = await CreateNotificationMessageAsync(notification);

            await _notificationManager.SendAsync(notificationMessage, recipients, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling notification {NotificationType}", typeof(TNotification).Name);
        }
    }

    protected abstract Task<IEnumerable<INotifiable>> GetRecipientsAsync(TNotification notification);
    protected abstract Task<INotificationMessage> CreateNotificationMessageAsync(TNotification notification);
}
```

**Конкретні нотифікації:**

```csharp
// Нотифікація про реєстрацію користувача
public class UserRegisteredNotification : INotification
{
    public int UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public DateTime RegisteredAt { get; set; }
}

// Повідомлення про вітання нового користувача
public class WelcomeNotificationMessage : BaseNotification
{
    private readonly string _userName;
    private readonly DateTime _registeredAt;

    public WelcomeNotificationMessage(string userName, DateTime registeredAt)
    {
        _userName = userName;
        _registeredAt = registeredAt;
    }

    public override string Subject => $"Ласкаво просимо, {_userName}!";
    public override string Content => $"Дякуємо за реєстрацію в нашому сервісі. Ваш акаунт було створено {_registeredAt:dd.MM.yyyy о HH:mm}.";
    public override NotificationPriority Priority => NotificationPriority.High;

    public override List<string> Channels => new() { "email", "database", "push" };

    public override string ToEmail(INotifiable recipient)
    {
        return $@"
            <h2>Ласкаво просимо, {_userName}!</h2>
            <p>Дякуємо за реєстрацію в нашому сервісі.</p>
            <p>Ваш акаунт було створено <strong>{_registeredAt:dd.MM.yyyy о HH:mm}</strong>.</p>
            <p>Тепер ви можете користуватися всіма можливостями нашої платформи.</p>
            <br>
            <p>З найкращими побажаннями,<br>Команда розробки</p>
        ";
    }

    public override string ToSms(INotifiable recipient)
    {
        return $"Вітаємо, {_userName}! Ваш акаунт створено. Дякуємо за реєстрацію!";
    }

    public override string ToTelegram(INotifiable recipient)
    {
        return $"🎉 *Ласкаво просимо, {_userName}!*\n\n" +
               $"Дякуємо за реєстрацію в нашому сервісі.\n" +
               $"Акаунт створено: `{_registeredAt:dd.MM.yyyy о HH:mm}`";
    }

    public override object ToPush(INotifiable recipient)
    {
        return new
        {
            title = Subject,
            body = $"Дякуємо за реєстрацію! Ваш акаунт готовий до використання.",
            icon = "welcome_icon",
            click_action = "OPEN_APP"
        };
    }
}

// Обробник нотифікації про реєстрацію
public class UserRegisteredNotificationHandler : NotificationHandler<UserRegisteredNotification>
{
    private readonly IUserRepository _userRepository;

    public UserRegisteredNotificationHandler(
        INotificationManager notificationManager,
        IUserRepository userRepository,
        ILogger<UserRegisteredNotificationHandler> logger)
        : base(notificationManager, logger)
    {
        _userRepository = userRepository;
    }

    protected override async Task<IEnumerable<INotifiable>> GetRecipientsAsync(UserRegisteredNotification notification)
    {
        var user = await _userRepository.GetByIdAsync(notification.UserId);
        return user != null ? new[] { user } : Array.Empty<INotifiable>();
    }

    protected override async Task<INotificationMessage> CreateNotificationMessageAsync(UserRegisteredNotification notification)
    {
        await Task.CompletedTask;
        return new WelcomeNotificationMessage(notification.UserName, notification.RegisteredAt);
    }
}
```

#### 6. Налаштування системи в Program.cs

```csharp
using MediatR;

var builder = WebApplication.CreateBuilder(args);

// Реєстрація MediatR
builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(Program).Assembly));

// Реєстрація системи нотифікацій
builder.Services.AddNotificationSystem(builder.Configuration);

var app = builder.Build();

// Minimal API endpoints
var usersGroup = app.MapGroup("/api/users").WithTags("Users");

usersGroup.MapPost("/", async (CreateUserCommand command, IMediator mediator) =>
{
    var userId = await mediator.Send(command);
    return Results.Created($"/api/users/{userId}", userId);
})
.WithName("CreateUser")
.WithOpenApi();

// Endpoint для отримання нотифікацій користувача
usersGroup.MapGet("/{userId}/notifications", async (int userId, INotificationRepository repository,
    int page = 1, int pageSize = 20) =>
{
    var notifications = await repository.GetUserNotificationsAsync(userId, page, pageSize);
    return Results.Ok(notifications);
})
.WithName("GetUserNotifications")
.WithOpenApi();

// Endpoint для позначення нотифікації як прочитаної
usersGroup.MapPut("/notifications/{notificationId}/read", async (int notificationId, INotificationRepository repository) =>
{
    await repository.MarkAsReadAsync(notificationId);
    return Results.NoContent();
})
.WithName("MarkNotificationAsRead")
.WithOpenApi();

app.Run();

// Extension method для реєстрації системи нотифікацій
public static class NotificationServiceExtensions
{
    public static IServiceCollection AddNotificationSystem(this IServiceCollection services, IConfiguration configuration)
    {
        // Реєстрація основних сервісів
        services.AddScoped<INotificationManager, NotificationManager>();
        services.AddScoped<INotificationStorage, NotificationStorage>();

        // Реєстрація каналів
        services.AddScoped<INotificationChannel, EmailNotificationChannel>();
        services.AddScoped<INotificationChannel, SmsNotificationChannel>();
        services.AddScoped<INotificationChannel, TelegramNotificationChannel>();
        services.AddScoped<INotificationChannel, PushNotificationChannel>();
        services.AddScoped<INotificationChannel, DatabaseNotificationChannel>();
        services.AddScoped<INotificationChannel, SlackNotificationChannel>();

        // Реєстрація провайдерів
        services.AddScoped<IEmailService, EmailService>();
        services.AddScoped<ISmsService, SmsService>();
        services.AddScoped<ITelegramBotService, TelegramBotService>();
        services.AddScoped<IPushNotificationService, PushNotificationService>();
        services.AddScoped<ISlackService, SlackService>();

        // Налаштування конфігурації
        services.Configure<EmailSettings>(configuration.GetSection("Email"));
        services.Configure<SmsSettings>(configuration.GetSection("Sms"));
        services.Configure<TelegramSettings>(configuration.GetSection("Telegram"));
        services.Configure<PushSettings>(configuration.GetSection("Push"));
        services.Configure<SlackSettings>(configuration.GetSection("Slack"));

        return services;
    }
}
```

#### 7. Розширені можливості

**Шаблони нотифікацій:**

```csharp
public interface INotificationTemplate
{
    string Name { get; }
    Task<string> RenderAsync(object model, string channel);
}

public class RazorNotificationTemplate : INotificationTemplate
{
    public string Name { get; }
    private readonly IRazorViewEngine _viewEngine;
    private readonly ITempDataProvider _tempDataProvider;
    private readonly IServiceProvider _serviceProvider;

    public RazorNotificationTemplate(string name, IRazorViewEngine viewEngine,
        ITempDataProvider tempDataProvider, IServiceProvider serviceProvider)
    {
        Name = name;
        _viewEngine = viewEngine;
        _tempDataProvider = tempDataProvider;
        _serviceProvider = serviceProvider;
    }

    public async Task<string> RenderAsync(object model, string channel)
    {
        var viewName = $"Notifications/{Name}/{channel}";

        var actionContext = new ActionContext(
            new DefaultHttpContext { RequestServices = _serviceProvider },
            new RouteData(),
            new ActionDescriptor()
        );

        var viewResult = _viewEngine.FindView(actionContext, viewName, false);

        if (!viewResult.Success)
        {
            throw new InvalidOperationException($"Template {viewName} not found");
        }

        using var writer = new StringWriter();
        var viewContext = new ViewContext(
            actionContext,
            viewResult.View,
            new ViewDataDictionary(new EmptyModelMetadataProvider(), new ModelStateDictionary())
            {
                Model = model
            },
            new TempDataDictionary(actionContext.HttpContext, _tempDataProvider),
            writer,
            new HtmlHelperOptions()
        );

        await viewResult.View.RenderAsync(viewContext);
        return writer.ToString();
    }
}
```

**Черги нотифікацій:**

```csharp
public interface INotificationQueue
{
    Task EnqueueAsync<T>(T notification, INotifiable recipient, string channel, NotificationPriority priority = NotificationPriority.Normal)
        where T : INotificationMessage;
    Task<QueuedNotification?> DequeueAsync(CancellationToken cancellationToken = default);
}

public class QueuedNotification
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public INotificationMessage Message { get; set; } = null!;
    public INotifiable Recipient { get; set; } = null!;
    public string Channel { get; set; } = string.Empty;
    public NotificationPriority Priority { get; set; }
    public DateTime QueuedAt { get; set; } = DateTime.UtcNow;
    public int RetryCount { get; set; }
    public DateTime? ScheduledFor { get; set; }
}

// Background service для обробки черги
public class NotificationQueueProcessor : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<NotificationQueueProcessor> _logger;

    public NotificationQueueProcessor(IServiceProvider serviceProvider, ILogger<NotificationQueueProcessor> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var queue = scope.ServiceProvider.GetRequiredService<INotificationQueue>();
                var manager = scope.ServiceProvider.GetRequiredService<INotificationManager>();

                var queuedNotification = await queue.DequeueAsync(stoppingToken);

                if (queuedNotification != null)
                {
                    await manager.SendViaChannelAsync(
                        queuedNotification.Message,
                        queuedNotification.Recipient,
                        queuedNotification.Channel,
                        stoppingToken);
                }
                else
                {
                    await Task.Delay(1000, stoppingToken); // Чекаємо 1 секунду якщо черга порожня
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing notification queue");
                await Task.Delay(5000, stoppingToken); // Чекаємо 5 секунд при помилці
            }
        }
    }
}
```

#### 8. Практичні приклади використання

**Приклад 1: Система замовлень**

```csharp
// Нотифікація про нове замовлення
public class OrderCreatedNotification : INotification
{
    public int OrderId { get; set; }
    public int CustomerId { get; set; }
    public decimal TotalAmount { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<OrderItem> Items { get; set; } = new();
}

// Повідомлення про замовлення
public class OrderConfirmationMessage : BaseNotification
{
    private readonly int _orderId;
    private readonly decimal _totalAmount;
    private readonly List<OrderItem> _items;

    public OrderConfirmationMessage(int orderId, decimal totalAmount, List<OrderItem> items)
    {
        _orderId = orderId;
        _totalAmount = totalAmount;
        _items = items;
    }

    public override string Subject => $"Підтвердження замовлення #{_orderId}";
    public override string Content => $"Ваше замовлення на суму {_totalAmount:C} прийнято в обробку.";
    public override NotificationPriority Priority => NotificationPriority.High;
    public override List<string> Channels => new() { "email", "sms", "database" };

    public override string ToEmail(INotifiable recipient)
    {
        var itemsHtml = string.Join("", _items.Select(item =>
            $"<tr><td>{item.Name}</td><td>{item.Quantity}</td><td>{item.Price:C}</td></tr>"));

        return $@"
            <h2>Дякуємо за ваше замовлення!</h2>
            <p>Замовлення <strong>#{_orderId}</strong> успішно прийнято.</p>

            <h3>Деталі замовлення:</h3>
            <table border='1' style='border-collapse: collapse;'>
                <tr><th>Товар</th><th>Кількість</th><th>Ціна</th></tr>
                {itemsHtml}
                <tr><td colspan='2'><strong>Загальна сума:</strong></td><td><strong>{_totalAmount:C}</strong></td></tr>
            </table>

            <p>Ми повідомимо вас про статус замовлення.</p>
        ";
    }

    public override string ToSms(INotifiable recipient)
    {
        return $"Замовлення #{_orderId} на суму {_totalAmount:C} прийнято. Дякуємо!";
    }
}

// Обробник нотифікації про замовлення
public class OrderCreatedNotificationHandler : NotificationHandler<OrderCreatedNotification>
{
    private readonly ICustomerRepository _customerRepository;
    private readonly IOrderRepository _orderRepository;

    public OrderCreatedNotificationHandler(
        INotificationManager notificationManager,
        ICustomerRepository customerRepository,
        IOrderRepository orderRepository,
        ILogger<OrderCreatedNotificationHandler> logger)
        : base(notificationManager, logger)
    {
        _customerRepository = customerRepository;
        _orderRepository = orderRepository;
    }

    protected override async Task<IEnumerable<INotifiable>> GetRecipientsAsync(OrderCreatedNotification notification)
    {
        var customer = await _customerRepository.GetByIdAsync(notification.CustomerId);
        return customer != null ? new[] { customer } : Array.Empty<INotifiable>();
    }

    protected override async Task<INotificationMessage> CreateNotificationMessageAsync(OrderCreatedNotification notification)
    {
        await Task.CompletedTask;
        return new OrderConfirmationMessage(notification.OrderId, notification.TotalAmount, notification.Items);
    }
}
```

**Приклад 2: Система коментарів**

```csharp
// Нотифікація про новий коментар
public class CommentAddedNotification : INotification
{
    public int CommentId { get; set; }
    public int PostId { get; set; }
    public int AuthorId { get; set; }
    public string AuthorName { get; set; } = string.Empty;
    public string CommentText { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

// Повідомлення про коментар
public class NewCommentMessage : BaseNotification
{
    private readonly string _authorName;
    private readonly string _commentText;
    private readonly int _postId;

    public NewCommentMessage(string authorName, string commentText, int postId)
    {
        _authorName = authorName;
        _commentText = commentText;
        _postId = postId;
    }

    public override string Subject => $"Новий коментар від {_authorName}";
    public override string Content => $"До вашого поста додано новий коментар: {_commentText}";
    public override List<string> Channels => new() { "email", "database", "push" };

    public override string ToTelegram(INotifiable recipient)
    {
        return $"💬 *Новий коментар*\n\n" +
               $"Автор: {_authorName}\n" +
               $"Коментар: _{_commentText}_\n\n" +
               $"[Переглянути пост](https://example.com/posts/{_postId})";
    }

    public override object ToPush(INotifiable recipient)
    {
        return new
        {
            title = Subject,
            body = _commentText.Length > 100 ? _commentText.Substring(0, 97) + "..." : _commentText,
            icon = "comment_icon",
            click_action = $"OPEN_POST_{_postId}",
            data = new { postId = _postId, commentId = _commentText }
        };
    }
}
```

#### 9. Найкращі практики для багатоканальних нотифікацій

**✅ Рекомендації:**

1. **Асинхронна обробка:**
```csharp
// Використовуйте черги для важких операцій
public class AsyncNotificationHandler : NotificationHandler<UserRegisteredNotification>
{
    private readonly INotificationQueue _queue;

    public AsyncNotificationHandler(INotificationQueue queue, ILogger<AsyncNotificationHandler> logger)
        : base(null!, logger)
    {
        _queue = queue;
    }

    public override async Task Handle(UserRegisteredNotification notification, CancellationToken cancellationToken)
    {
        var recipients = await GetRecipientsAsync(notification);
        var message = await CreateNotificationMessageAsync(notification);

        foreach (var recipient in recipients)
        {
            foreach (var channel in message.GetChannelsFor(recipient))
            {
                await _queue.EnqueueAsync(message, recipient, channel, message.Priority);
            }
        }
    }
}
```

2. **Retry механізм:**
```csharp
public class RetryableNotificationChannel : INotificationChannel
{
    private readonly INotificationChannel _innerChannel;
    private readonly ILogger _logger;
    private readonly int _maxRetries;

    public RetryableNotificationChannel(INotificationChannel innerChannel, ILogger logger, int maxRetries = 3)
    {
        _innerChannel = innerChannel;
        _logger = logger;
        _maxRetries = maxRetries;
    }

    public string Name => _innerChannel.Name;

    public bool CanHandle(INotifiable recipient, INotificationMessage message)
    {
        return _innerChannel.CanHandle(recipient, message);
    }

    public async Task<NotificationResult> SendAsync(INotifiable recipient, INotificationMessage message, CancellationToken cancellationToken = default)
    {
        for (int attempt = 1; attempt <= _maxRetries; attempt++)
        {
            try
            {
                var result = await _innerChannel.SendAsync(recipient, message, cancellationToken);
                if (result.IsSuccess)
                {
                    return result;
                }

                if (attempt < _maxRetries)
                {
                    var delay = TimeSpan.FromSeconds(Math.Pow(2, attempt)); // Exponential backoff
                    await Task.Delay(delay, cancellationToken);
                }
            }
            catch (Exception ex) when (attempt < _maxRetries)
            {
                _logger.LogWarning(ex, "Attempt {Attempt} failed for {Channel}, retrying...", attempt, Name);
                var delay = TimeSpan.FromSeconds(Math.Pow(2, attempt));
                await Task.Delay(delay, cancellationToken);
            }
        }

        return new NotificationResult
        {
            IsSuccess = false,
            ErrorMessage = $"Failed after {_maxRetries} attempts",
            SentAt = DateTime.UtcNow
        };
    }
}
```

3. **Rate limiting:**
```csharp
public class RateLimitedNotificationChannel : INotificationChannel
{
    private readonly INotificationChannel _innerChannel;
    private readonly SemaphoreSlim _semaphore;
    private readonly Timer _resetTimer;

    public RateLimitedNotificationChannel(INotificationChannel innerChannel, int maxConcurrent = 10)
    {
        _innerChannel = innerChannel;
        _semaphore = new SemaphoreSlim(maxConcurrent, maxConcurrent);
        _resetTimer = new Timer(ReleaseSemaphore, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    public string Name => _innerChannel.Name;

    public bool CanHandle(INotifiable recipient, INotificationMessage message)
    {
        return _innerChannel.CanHandle(recipient, message);
    }

    public async Task<NotificationResult> SendAsync(INotifiable recipient, INotificationMessage message, CancellationToken cancellationToken = default)
    {
        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            return await _innerChannel.SendAsync(recipient, message, cancellationToken);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    private void ReleaseSemaphore(object? state)
    {
        // Періодично скидаємо лічильник для rate limiting
        while (_semaphore.CurrentCount < 10)
        {
            _semaphore.Release();
        }
    }
}
```

#### 10. Тестування багатоканальної системи нотифікацій

**Тестування каналів:**

```csharp
public class EmailNotificationChannelTests
{
    private readonly Mock<IEmailService> _mockEmailService;
    private readonly Mock<ILogger<EmailNotificationChannel>> _mockLogger;
    private readonly EmailNotificationChannel _channel;

    public EmailNotificationChannelTests()
    {
        _mockEmailService = new Mock<IEmailService>();
        _mockLogger = new Mock<ILogger<EmailNotificationChannel>>();
        _channel = new EmailNotificationChannel(_mockEmailService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task SendAsync_ValidRecipient_ShouldSendEmail()
    {
        // Arrange
        var recipient = new TestUser
        {
            Id = 1,
            Email = "<EMAIL>",
            Name = "Test User"
        };

        var message = new TestNotificationMessage
        {
            Subject = "Test Subject",
            Content = "Test Content"
        };

        _mockEmailService.Setup(s => s.SendAsync(It.IsAny<EmailMessage>(), It.IsAny<CancellationToken>()))
                        .ReturnsAsync("message-id-123");

        // Act
        var result = await _channel.SendAsync(recipient, message);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.ExternalId.Should().Be("message-id-123");

        _mockEmailService.Verify(s => s.SendAsync(It.Is<EmailMessage>(em =>
            em.To == recipient.Email &&
            em.Subject == message.Subject),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public void CanHandle_RecipientWithEmail_ShouldReturnTrue()
    {
        // Arrange
        var recipient = new TestUser { Email = "<EMAIL>" };
        var message = new TestNotificationMessage();

        // Act
        var canHandle = _channel.CanHandle(recipient, message);

        // Assert
        canHandle.Should().BeTrue();
    }

    [Fact]
    public void CanHandle_RecipientWithoutEmail_ShouldReturnFalse()
    {
        // Arrange
        var recipient = new TestUser { Email = "" };
        var message = new TestNotificationMessage();

        // Act
        var canHandle = _channel.CanHandle(recipient, message);

        // Assert
        canHandle.Should().BeFalse();
    }
}
```

**Тестування менеджера нотифікацій:**

```csharp
public class NotificationManagerTests
{
    private readonly Mock<IServiceProvider> _mockServiceProvider;
    private readonly Mock<INotificationStorage> _mockStorage;
    private readonly Mock<ILogger<NotificationManager>> _mockLogger;
    private readonly NotificationManager _manager;

    public NotificationManagerTests()
    {
        _mockServiceProvider = new Mock<IServiceProvider>();
        _mockStorage = new Mock<INotificationStorage>();
        _mockLogger = new Mock<ILogger<NotificationManager>>();
        _manager = new NotificationManager(_mockServiceProvider.Object, _mockLogger.Object, _mockStorage.Object);
    }

    [Fact]
    public async Task SendAsync_MultipleChannels_ShouldSendToAllChannels()
    {
        // Arrange
        var recipient = new TestUser
        {
            Id = 1,
            Email = "<EMAIL>",
            PhoneNumber = "+**********"
        };

        var message = new TestNotificationMessage
        {
            Channels = new List<string> { "email", "sms" }
        };

        var mockEmailChannel = new Mock<INotificationChannel>();
        var mockSmsChannel = new Mock<INotificationChannel>();

        mockEmailChannel.Setup(c => c.CanHandle(recipient, message)).Returns(true);
        mockEmailChannel.Setup(c => c.SendAsync(recipient, message, It.IsAny<CancellationToken>()))
                       .ReturnsAsync(new NotificationResult { IsSuccess = true });

        mockSmsChannel.Setup(c => c.CanHandle(recipient, message)).Returns(true);
        mockSmsChannel.Setup(c => c.SendAsync(recipient, message, It.IsAny<CancellationToken>()))
                      .ReturnsAsync(new NotificationResult { IsSuccess = true });

        _mockServiceProvider.Setup(sp => sp.GetRequiredService(typeof(EmailNotificationChannel)))
                           .Returns(mockEmailChannel.Object);
        _mockServiceProvider.Setup(sp => sp.GetRequiredService(typeof(SmsNotificationChannel)))
                           .Returns(mockSmsChannel.Object);

        // Act
        await _manager.SendAsync(message, recipient);

        // Assert
        mockEmailChannel.Verify(c => c.SendAsync(recipient, message, It.IsAny<CancellationToken>()), Times.Once);
        mockSmsChannel.Verify(c => c.SendAsync(recipient, message, It.IsAny<CancellationToken>()), Times.Once);
    }
}
```

**Інтеграційні тести:**

```csharp
public class NotificationSystemIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public NotificationSystemIntegrationTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task CreateUser_ShouldTriggerWelcomeNotification()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Name = "Integration Test User",
            Email = "<EMAIL>",
            Age = 25
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/users", command);

        // Assert
        response.Should().HaveStatusCode(HttpStatusCode.Created);

        var userId = await response.Content.ReadFromJsonAsync<int>();

        // Перевіряємо, що нотифікація збережена в базі
        var notificationsResponse = await _client.GetAsync($"/api/users/{userId}/notifications");
        notificationsResponse.Should().HaveStatusCode(HttpStatusCode.OK);

        var notifications = await notificationsResponse.Content.ReadFromJsonAsync<List<DatabaseNotification>>();
        notifications.Should().NotBeEmpty();
        notifications.Should().Contain(n => n.Subject.Contains("Ласкаво просимо"));
    }
}
```

#### 11. Моніторинг та метрики

```csharp
public class NotificationMetrics
{
    private readonly IMetricsCollector _metrics;

    public NotificationMetrics(IMetricsCollector metrics)
    {
        _metrics = metrics;
    }

    public void RecordNotificationSent(string channel, bool success, TimeSpan duration)
    {
        _metrics.Counter("notifications_sent_total")
               .WithTag("channel", channel)
               .WithTag("success", success.ToString())
               .Increment();

        _metrics.Histogram("notification_send_duration_ms")
               .WithTag("channel", channel)
               .Record(duration.TotalMilliseconds);
    }

    public void RecordNotificationQueued(string channel, NotificationPriority priority)
    {
        _metrics.Counter("notifications_queued_total")
               .WithTag("channel", channel)
               .WithTag("priority", priority.ToString())
               .Increment();
    }
}

// Декоратор для збору метрик
public class MetricsNotificationChannel : INotificationChannel
{
    private readonly INotificationChannel _innerChannel;
    private readonly NotificationMetrics _metrics;

    public MetricsNotificationChannel(INotificationChannel innerChannel, NotificationMetrics metrics)
    {
        _innerChannel = innerChannel;
        _metrics = metrics;
    }

    public string Name => _innerChannel.Name;

    public bool CanHandle(INotifiable recipient, INotificationMessage message)
    {
        return _innerChannel.CanHandle(recipient, message);
    }

    public async Task<NotificationResult> SendAsync(INotifiable recipient, INotificationMessage message, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            var result = await _innerChannel.SendAsync(recipient, message, cancellationToken);

            stopwatch.Stop();
            _metrics.RecordNotificationSent(Name, result.IsSuccess, stopwatch.Elapsed);

            return result;
        }
        catch (Exception)
        {
            stopwatch.Stop();
            _metrics.RecordNotificationSent(Name, false, stopwatch.Elapsed);
            throw;
        }
    }
}
```

## Висновки щодо багатоканальної системи нотифікацій

### ✅ Переваги системи:

1. **Масштабованість** - легко додавати нові канали
2. **Гнучкість** - різний контент для різних каналів
3. **Надійність** - retry механізми та обробка помилок
4. **Продуктивність** - асинхронна обробка та черги
5. **Моніторинг** - метрики та логування
6. **Тестованість** - кожен компонент можна тестувати окремо

### 🎯 Ключові особливості:

- **Єдиний інтерфейс** для всіх типів нотифікацій
- **Автоматичне визначення каналів** на основі даних користувача
- **Персоналізація контенту** для кожного каналу
- **Черги та background processing** для важких операцій
- **Rate limiting** для захисту від перевантаження
- **Retry механізми** для підвищення надійності

### 📊 Рекомендації по використанню:

1. **Для критичних повідомлень** використовуйте кілька каналів
2. **Налаштуйте черги** для обробки великих обсягів
3. **Моніторьте метрики** для оптимізації продуктивності
4. **Тестуйте кожен канал** окремо та в комплексі
5. **Використовуйте шаблони** для складного контенту

Ця система забезпечує потужну та гнучку платформу для нотифікацій, яка може рости разом з вашим додатком та легко адаптуватися до нових вимог.

## Висновки

MediatR є потужним інструментом для створення чистої архітектури в ASP.NET Core додатках. Основні переваги:

### ✅ Переваги MediatR:

1. **Розділення відповідальностей** - кожна операція має свій обробник
2. **Слабка зв'язність** - компоненти не залежать один від одного напряму
3. **Легкість тестування** - кожен компонент можна тестувати окремо
4. **CQRS підтримка** - природне розділення команд і запитів
5. **Розширюваність** - легко додавати нові функції через behaviors
6. **Чистий код** - контролери стають простішими

### 🎯 Коли використовувати:

- Складні бізнес-процеси
- Великі команди розробників
- Потреба в CQRS
- Система нотифікацій
- Багато cross-cutting concerns

### ⚠️ Потенційні недоліки:

- Додаткова складність для простих додатків
- Невеликий performance overhead
- Більше коду для простих операцій
- Крива навчання для команди

### 📚 Рекомендації для подальшого вивчення:

1. Вивчіть патерн CQRS детальніше
2. Дослідіть Event Sourcing
3. Розгляньте використання MediatR з Domain Events
4. Вивчіть інші behaviors (кешування, аудит, безпека)
5. Практикуйтеся з різними сценаріями використання

MediatR допомагає створювати масштабовані, підтримувані та тестовані додатки, але важливо розуміти, коли його використання виправдане, а коли може бути надмірним.

---

**Корисні ресурси:**

- [Офіційна документація MediatR](https://github.com/jbogard/MediatR)
- [Clean Architecture з MediatR](https://github.com/jasontaylordev/CleanArchitecture)
- [CQRS та Event Sourcing](https://docs.microsoft.com/en-us/azure/architecture/patterns/cqrs)
- [FluentValidation документація](https://docs.fluentvalidation.net/)
