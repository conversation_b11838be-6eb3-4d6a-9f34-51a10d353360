# Вступ до ASP.NET

## Що таке ASP.NET?

ASP.NET — це потужний фреймворк від Microsoft для створення вебдодатків, вебсайтів і вебсервісів. Він дозволяє розробникам будувати динамічні, масштабовані та безпечні програми, які працюють на сервері. ASP.NET підтримує різні типи додатків, від простих вебсайтів до складних RESTful API, і є частиною екосистеми .NET.

Основні особливості ASP.NET:
- **Кросплатформеність** (з .NET Core та .NET 5+): працює на Windows, macOS і Linux.
- **Продуктивність**: один із найшвидших фреймворків для веброзробки.
- **Гнучкість**: підтримує різні моделі розробки (MVC, Razor Pages, Minimal API тощо).
- **Інтеграція**: легко інтегрується з хмарними сервісами, базами даних і сучасними фронтенд-фреймворками.

![Продуктивність](https://habrastorage.org/r/w1560/getpro/habr/upload_files/e28/184/e33/e28184e33accffd5f296dc1ab25fe893.png)

## Історія ASP.NET

ASP.NET з’явився у 2002 році як частина платформи .NET Framework 1.0. Спочатку це був інструмент для створення вебдодатків на основі серверних сторінок (ASP — Active Server Pages). З часом фреймворк еволюціонував:

- **2002–2012**: ASP.NET Web Forms домінував, пропонуючи модель розробки, подібну до настільних додатків.
- **2012**: Випуск ASP.NET MVC, який привніс чітке розділення логіки (Model-View-Controller).
- **2016**: З’явився ASP.NET Core, кросплатформений і відкритий фреймворк, який об’єднав MVC, Web API та нові підходи, такі як Razor Pages.
- **2020 і далі**: З .NET 5 та .NET 6/7/8 фреймворк став ще швидшим, додавши Minimal API для створення легких вебсервісів.

![Історія розвитку .NET](https://d585tldpucybw.cloudfront.net/sfimages/default-source/blogs/2023/2023-02/aspnetcore-timeline.png?sfvrsn=64702170_3)


## Архітектура ASP.NET

ASP.NET побудований на модульній архітектурі, яка забезпечує гнучкість і масштабованість. Основні компоненти:

- **.NET Runtime**: Середа виконання, яка компілює та виконує код.
- **Kestrel**: Легкий і швидкий вебсервер, який обробляє HTTP-запити. Може працювати самостійно або з реверс-проксі (наприклад, IIS чи Nginx).
- **Middleware**: Ланцюжок обробки запитів, який дозволяє налаштувати потік даних (автентифікація, логування, маршрутизація тощо).
- **Dependency Injection**: Вбудована система для управління залежностями, що спрощує тестування та розробку.

![Архітектура ASP.NET Core](https://images.hanselman.com/blog/Windows-Live-Writer/Making-Web-APIs-with-ASP.NET-MVC-4-Beta_BEA5/image_04e87be2-544a-4de4-bcdd-e97d206c45bb.png)
![Архітектура ASP.NET Core](https://learn.microsoft.com/en-us/dotnet/architecture/modern-web-apps-azure/media/image5-9.png)


## Моделі розробки

ASP.NET підтримує кілька моделей розробки, які підходять для різних типів проєктів:

1. **Web Forms** (застаріла): Модель, орієнтована на швидку розробку з використанням компонентів, подібних до настільних додатків.
2. **MVC (Model-View-Controller)**: Чітке розділення логіки (модель), відображення (представлення) та управління (контролер). Ідеально для складних вебдодатків.
3. **Razor Pages**: Спрощена модель для створення сторінок із мінімальною кількістю коду. Підходить для невеликих проєктів.
4. **Web API**: Для створення RESTful API, які забезпечують обмін даними між клієнтом і сервером.
5. **Minimal API** (з .NET 6): Легкий спосіб створення API з мінімальним кодом, без необхідності контролерів чи складної структури.

![Порівняння моделей розробки](https://www.c-sharpcorner.com/article/asp-net-core-razor-pages-vs-mvc/Images/ASP.NET%20Core%20Razor%20Pages%20vs%20MVC.jpg)
*Зображення: Порівняння MVC та Razor Pages (Джерело: C# Corner)*

## Висновок

ASP.NET — це універсальний і потужний фреймворк, який підходить як для новачків, так і для досвідчених розробників. Його історія показує постійний розвиток, а модульна архітектура та різноманітні моделі розробки дозволяють створювати сучасні вебдодатки будь-якої складності. У наступних темах ми розглянемо Minimal API як один із найпростіших способів почати роботу з ASP.NET.