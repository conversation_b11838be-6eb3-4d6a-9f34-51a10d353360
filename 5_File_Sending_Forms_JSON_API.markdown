# Відправка файлів, форм, JSON та створення REST API в ASP.NET Core

У цьому матеріалі ми розглянемо, як відправляти файли, обробляти HTML-форми, працювати з JSON-даними та створити простий REST API в ASP.NET Core, використовуючи Minimal API. Ми детально розберемо методи відправки файлів, обробки форм, серіалізації/десеріалізації JSON, а також створення RESTful API з підтримкою CRUD-операцій.

## Відправка файлів

ASP.NET Core дозволяє відправляти файли клієнту за допомогою методу `SendFileAsync`, який підтримує як абсолютні, так і відносні шляхи до файлів, а також об’єкти `IFileInfo`.

### Відправка файлу за шляхом
Метод `SendFileAsync` може приймати шлях до файлу у вигляді рядка. Наприклад, для відправки зображення:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Run(async (context) => await context.Response.SendFileAsync("D:\\forest.jpg"));

app.Run();
```

**Результат відправки зображення**:
![alt text](image.png)
*Зображення: Зображення forest.jpg відображається в браузері*

За замовчуванням браузер намагається відкрити файл. Наприклад, зображення відображаються безпосередньо, а HTML-сторінки рендеряться як веб-сторінки.

### Відправка файлу з відносним шляхом
Для використання відносних шляхів додайте файл (наприклад, `forest.jpg`) до проєкту і встановіть у його властивостях опцію **Copy to Output Directory** на **Copy if newer** або **Copy always**. Код для відправки:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Run(async (context) => await context.Response.SendFileAsync("forest.jpg"));

app.Run();
```

### Відправка HTML-сторінки
Створіть папку `html` у проєкті та додайте файл `index.html` із таким вмістом:

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>ITStep</title>
</head>
<body>
    <h2>Hello ASP.NET Core!</h2>
</body>
</html>
```

Код для відправки HTML-сторінки:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Run(async (context) =>
{
    context.Response.ContentType = "text/html; charset=utf-8";
    await context.Response.SendFileAsync("html/index.html");
});

app.Run();
```

**Результат відправки HTML-сторінки**:
![alt text](./image_1.png)
*Зображення: Відображення HTML-сторінки index.html*

### Умовна відправка файлів
Додайте до папки `html` ще два файли: `about.html` і `contact.html`. Код для умовної відправки файлів залежно від шляху запиту:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Run(async (context) =>
{
    var path = context.Request.Path;
    var fullPath = $"html/{path}";
    var response = context.Response;

    response.ContentType = "text/html; charset=utf-8";
    if (File.Exists(fullPath))
    {
        await response.SendFileAsync(fullPath);
    }
    else
    {
        response.StatusCode = 404;
        await response.WriteAsync("<h2>Not Found</h2>");
    }
});

app.Run();
```

**Результат умовної відправки**:
![alt text](image_2.png)
![alt text](image_3.png)
*Зображення: Відображення HTML-сторінки або повідомлення 404*

### Завантаження файлу
Щоб змусити браузер завантажити файл замість його відкриття, встановіть заголовок `Content-Disposition` на `attachment`:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Run(async (context) =>
{
    context.Response.Headers.ContentDisposition = "attachment; filename=my_forest.jpg";
    await context.Response.SendFileAsync("forest.jpg");
});

app.Run();
```

**Результат завантаження файлу**:
![alt text](image-1.png)
*Зображення: Завантаження файлу my_forest.jpg*

### Використання IFileInfo
Для більшої гнучкості використовуйте `IFileInfo` з `PhysicalFileProvider`:

```csharp
using Microsoft.Extensions.FileProviders;

var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Run(async (context) =>
{
    var fileProvider = new PhysicalFileProvider(Directory.GetCurrentDirectory());
    var fileInfo = fileProvider.GetFileInfo("forest.jpg");

    context.Response.Headers.ContentDisposition = "attachment; filename=my_forest2.jpg";
    await context.Response.SendFileAsync(fileInfo);
});

app.Run();
```

**Результат відправки через IFileInfo**:
![alt text](image-2.png)
*Зображення: Завантаження файлу my_forest2.jpg через IFileInfo*

## Обробка HTML-форм

HTML-форми часто використовуються для відправки даних на сервер через POST-запити. Дані форми доступні через властивість `Request.Form` об’єкта `HttpRequest`.

### Проста форма
Створіть файл `html/index.html`:

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>ITStep</title>
</head>
<body>
    <h2>Форма користувача</h2>
    <form method="post" action="postuser">
        <p>Ім'я: <input name="name" /></p>
        <p>Вік: <input name="age" type="number" /></p>
        <input type="submit" value="Надіслати" />
    </form>
</body>
</html>
```

Код для обробки форми:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Run(async (context) =>
{
    context.Response.ContentType = "text/html; charset=utf-8";

    if (context.Request.Path == "/postuser")
    {
        var form = context.Request.Form;
        string name = form["name"];
        string age = form["age"];
        await context.Response.WriteAsync($"<div><p>Ім'я: {name}</p><p>Вік: {age}</p></div>");
    }
    else
    {
        await context.Response.SendFileAsync("html/index.html");
    }
});

app.Run();
```

**Результат обробки форми**:
![alt text](image-3.png)
![alt text](image-4.png)
*Зображення: Відображення даних форми (ім’я та вік)*

### Обробка масивів у формах
Додайте до `index.html` поля для введення кількох значень:

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>ITStep</title>
</head>
<body>
    <h2>Форма користувача</h2>
    <form method="post" action="postuser">
        <p>Ім'я: <br /><input name="name" /></p>
        <p>Вік: <br /><input name="age" type="number" /></p>
        <p>Мови програмування:<br />
            <input name="languages" /><br />
            <input name="languages" /><br />
            <input name="languages" /><br />
        </p>
        <input type="submit" value="Надіслати" />
    </form>
</body>
</html>
```

Код для обробки масиву:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Run(async (context) =>
{
    context.Response.ContentType = "text/html; charset=utf-8";

    if (context.Request.Path == "/postuser")
    {
        var form = context.Request.Form;
        string name = form["name"];
        string age = form["age"];
        string[] languages = form["languages"];
        string langList = string.Join(" ", languages);
        await context.Response.WriteAsync($"<div><p>Ім'я: {name}</p><p>Вік: {age}</p><p>Мови: {langList}</p></div>");
    }
    else
    {
        await context.Response.SendFileAsync("html/index.html");
    }
});

app.Run();
```

**Результат обробки масиву**:
![alt text](image-5.png)
![alt text](image-6.png)
*Зображення: Відображення даних форми з масивом мов*

### Обробка вибору з елементом select
Додайте до форми елемент `<select>` із множинним вибором:

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>ITStep</title>
</head>
<body>
    <h2>Форма користувача</h2>
    <form method="post" action="postuser">
        <p>Ім'я: <br /><input name="name" /></p>
        <p>Вік: <br /><input name="age" type="number" /></p>
        <p>Мови програмування:<br />
            <select multiple name="languages">
                <option>C#</option>
                <option>JavaScript</option>
                <option>Kotlin</option>
                <option>Java</option>
            </select>
        </p>
        <input type="submit" value="Надіслати" />
    </form>
</body>
</html>
```

**Результат обробки select**:
![alt text](image-8.png)
*Зображення: Відображення вибраних мов із елемента select*

## Переадресація

Метод `Redirect` об’єкта `HttpResponse` дозволяє виконувати переадресацію на інший URL:

- `Redirect(string location)`: Тимчасова переадресація (код 302).
- `Redirect(string location, bool permanent)`: Постійна (код 301) або тимчасова (код 302) переадресація.

Приклад переадресації:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Run(async (context) =>
{
    if (context.Request.Path == "/old")
    {
        context.Response.Redirect("/new");
    }
    else if (context.Request.Path == "/new")
    {
        await context.Response.WriteAsync("Нова сторінка");
    }
    else
    {
        await context.Response.WriteAsync("Головна сторінка");
    }
});

app.Run();
```

**Результат переадресації**:
![alt text](image-7.png)
![alt text](image-9.png)
![alt text](image-10.png)
*Зображення: Переадресація з /old на /new*

Переадресація може бути й на зовнішні ресурси:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Run(async (context) =>
{
    if (context.Request.Path == "/old")
    {
        context.Response.Redirect("https://www.google.com/search?q=itstep");
    }
    else
    {
        await context.Response.WriteAsync("Головна сторінка");
    }
});

app.Run();
```

## Робота з JSON

JSON — популярний формат для обміну даними. ASP.NET Core надає методи для серіалізації (`WriteAsJsonAsync`) та десеріалізації (`ReadFromJsonAsync`) JSON.

### Відправка JSON
Метод `WriteAsJsonAsync` серіалізує об’єкт у JSON і автоматично встановлює `Content-Type` на `application/json; charset=utf-8`:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Run(async (context) =>
{
    Person tom = new("Tom", 22);
    await context.Response.WriteAsJsonAsync(tom);
});

app.Run();

public record Person(string Name, int Age);
```

**Результат відправки JSON**:
![alt text](image-11.png)
*Зображення: Відображення JSON-об’єкта Person*

Альтернатива з ручною серіалізацією:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Run(async (context) =>
{
    context.Response.Headers.ContentType = "application/json; charset=utf-8";
    await context.Response.WriteAsync("{\"name\":\"Tom\",\"age\":37}");
});

app.Run();
```

### Отримання JSON
Метод `ReadFromJsonAsync` десеріалізує JSON-дані з тіла запиту:

Створіть файл `html/index.html` для відправки JSON:

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>ITStep</title>
</head>
<body>
    <h2>Форма користувача</h2>
    <div id="message"></div>
    <div>
        <p>Ім'я: <br /><input name="userName" id="userName" /></p>
        <p>Вік: <br /><input name="userAge" id="userAge" type="number" /></p>
        <button id="sendBtn">Надіслати</button>
    </div>
    <script>
        document.getElementById("sendBtn").addEventListener("click", send);
        async function send() {
            const response = await fetch("/api/user", {
                method: "POST",
                headers: { "Accept": "application/json", "Content-Type": "application/json" },
                body: JSON.stringify({
                    name: document.getElementById("userName").value,
                    age: document.getElementById("userAge").value
                })
            });
            const message = await response.json();
            document.getElementById("message").innerText = message.text;
        }
    </script>
</body>
</html>
```

Код для обробки JSON:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Run(async (context) =>
{
    var response = context.Response;
    var request = context.Request;
    if (request.Path == "/api/user")
    {
        var message = "Некоректні дані";
        try
        {
            var person = await request.ReadFromJsonAsync<Person>();
            if (person != null)
                message = $"Ім'я: {person.Name} Вік: {person.Age}";
        }
        catch { }
        await response.WriteAsJsonAsync(new { text = message });
    }
    else
    {
        response.ContentType = "text/html; charset=utf-8";
        await response.SendFileAsync("html/index.html");
    }
});

app.Run();

public record Person(string Name, int Age);
```

**Результат обробки JSON**:
![alt text](image-12.png)
![alt text](image-13.png)
*Зображення: Відображення JSON-відповіді з даними користувача*

### Налаштування серіалізації
Для уникнення помилок десеріалізації використовуйте `JsonSerializerOptions` і власний конвертер:

```csharp
using System.Text.Json;
using System.Text.Json.Serialization;

var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Run(async (context) =>
{
    var response = context.Response;
    var request = context.Request;
    if (request.Path == "/api/user")
    {
        var responseText = "Некоректні дані";
        if (request.HasJsonContentType())
        {
            var jsonOptions = new JsonSerializerOptions();
            jsonOptions.Converters.Add(new PersonConverter());
            var person = await request.ReadFromJsonAsync<Person>(jsonOptions);
            if (person != null)
                responseText = $"Ім'я: {person.Name} Вік: {person.Age}";
        }
        await response.WriteAsJsonAsync(new { text = responseText });
    }
    else
    {
        response.ContentType = "text/html; charset=utf-8";
        await response.SendFileAsync("html/index.html");
    }
});

app.Run();

public record Person(string Name, int Age);

public class PersonConverter : JsonConverter<Person>
{
    public override Person Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        var personName = "Undefined";
        var personAge = 0;
        while (reader.Read())
        {
            if (reader.TokenType == JsonTokenType.PropertyName)
            {
                var propertyName = reader.GetString();
                reader.Read();
                switch (propertyName?.ToLower())
                {
                    case "age" when reader.TokenType == JsonTokenType.Number:
                        personAge = reader.GetInt32();
                        break;
                    case "age" when reader.TokenType == JsonTokenType.String:
                        string? stringValue = reader.GetString();
                        if (int.TryParse(stringValue, out int value))
                        {
                            personAge = value;
                        }
                        break;
                    case "name":
                        string? name = reader.GetString();
                        if (name != null)
                            personName = name;
                        break;
                }
            }
        }
        return new Person(personName, personAge);
    }

    public override void Write(Utf8JsonWriter writer, Person person, JsonSerializerOptions options)
    {
        writer.WriteStartObject();
        writer.WriteString("name", person.Name);
        writer.WriteNumber("age", person.Age);
        writer.WriteEndObject();
    }
}
```

**Результат із конвертером JSON**:
![alt text](image-14.png)
*Зображення: Обробка JSON із нестандартними іменами властивостей*

Цей конвертер дозволяє обробляти JSON із невідповідними іменами властивостей або типами даних, уникаючи виключень.

## Створення REST API

REST API — це архітектура для створення вебсервісів, яка використовує HTTP-методи (GET, POST, PUT, DELETE) для виконання операцій CRUD (Create, Read, Update, Delete).

### Серверна частина
Створимо простий REST API для роботи з користувачами. Визначимо модель `Person` і список даних:

```csharp
using System.Text.RegularExpressions;

var users = new List<Person>
{
    new() { Id = Guid.NewGuid().ToString(), Name = "Tom", Age = 37 },
    new() { Id = Guid.NewGuid().ToString(), Name = "Bob", Age = 41 },
    new() { Id = Guid.NewGuid().ToString(), Name = "Sam", Age = 24 }
};

var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Run(async (context) =>
{
    var response = context.Response;
    var request = context.Request;
    var path = request.Path;
    string expressionForGuid = @"^/api/users/\w{8}-\w{4}-\w{4}-\w{4}-\w{12}$";

    if (path == "/api/users" && request.Method == "GET")
    {
        await GetAllPeople(response);
    }
    else if (Regex.IsMatch(path, expressionForGuid) && request.Method == "GET")
    {
        string? id = path.Value?.Split("/")[3];
        await GetPerson(id, response);
    }
    else if (path == "/api/users" && request.Method == "POST")
    {
        await CreatePerson(response, request);
    }
    else if (path == "/api/users" && request.Method == "PUT")
    {
        await UpdatePerson(response, request);
    }
    else if (Regex.IsMatch(path, expressionForGuid) && request.Method == "DELETE")
    {
        string? id = path.Value?.Split("/")[3];
        await DeletePerson(id, response);
    }
    else
    {
        response.ContentType = "text/html; charset=utf-8";
        await response.SendFileAsync("html/index.html");
    }
});

app.Run();

async Task GetAllPeople(HttpResponse response)
{
    await response.WriteAsJsonAsync(users);
}

async Task GetPerson(string? id, HttpResponse response)
{
    Person? user = users.FirstOrDefault(u => u.Id == id);
    if (user != null)
        await response.WriteAsJsonAsync(user);
    else
    {
        response.StatusCode = 404;
        await response.WriteAsJsonAsync(new { message = "Користувач не знайдений" });
    }
}

async Task CreatePerson(HttpResponse response, HttpRequest request)
{
    try
    {
        var user = await request.ReadFromJsonAsync<Person>();
        if (user != null)
        {
            user.Id = Guid.NewGuid().ToString();
            users.Add(user);
            await response.WriteAsJsonAsync(user);
        }
        else
        {
            throw new Exception("Некоректні дані");
        }
    }
    catch
    {
        response.StatusCode = 400;
        await response.WriteAsJsonAsync(new { message = "Некоректні дані" });
    }
}

async Task UpdatePerson(HttpResponse response, HttpRequest request)
{
    try
    {
        Person? userData = await request.ReadFromJsonAsync<Person>();
        if (userData != null)
        {
            var user = users.FirstOrDefault(u => u.Id == userData.Id);
            if (user != null)
            {
                user.Age = userData.Age;
                user.Name = userData.Name;
                await response.WriteAsJsonAsync(user);
            }
            else
            {
                response.StatusCode = 404;
                await response.WriteAsJsonAsync(new { message = "Користувач не знайдений" });
            }
        }
        else
        {
            throw new Exception("Некоректні дані");
        }
    }
    catch
    {
        response.StatusCode = 400;
        await response.WriteAsJsonAsync(new { message = "Некоректні дані" });
    }
}

async Task DeletePerson(string? id, HttpResponse response)
{
    Person? user = users.FirstOrDefault(u => u.Id == id);
    if (user != null)
    {
        users.Remove(user);
        await response.WriteAsJsonAsync(user);
    }
    else
    {
        response.StatusCode = 404;
        await response.WriteAsJsonAsync(new { message = "Користувач не знайдений" });
    }
}

public class Person
{
    public string Id { get; set; } = "";
    public string Name { get; set; } = "";
    public int Age { get; set; }
}
```

### Клієнтська частина
Створіть файл `html/index.html` для взаємодії з API:

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>ITStep</title>
    <style>
        td { padding: 5px; }
        button { margin: 5px; }
    </style>
</head>
<body>
    <h2>Список користувачів</h2>
    <div>
        <input type="hidden" id="userId" />
        <p>Ім'я:<br /><input id="userName" /></p>
        <p>Вік:<br /><input id="userAge" type="number" /></p>
        <p>
            <button id="saveBtn">Зберегти</button>
            <button id="resetBtn">Скинути</button>
        </p>
    </div>
    <table>
        <thead><tr><th>Ім'я</th><th>Вік</th><th></th></tr></thead>
        <tbody></tbody>
    </table>
    <script>
        async function getUsers() {
            const response = await fetch("/api/users", {
                method: "GET",
                headers: { "Accept": "application/json" }
            });
            if (response.ok === true) {
                const users = await response.json();
                const rows = document.querySelector("tbody");
                users.forEach(user => rows.append(row(user)));
            }
        }

        async function getUser(id) {
            const response = await fetch(`/api/users/${id}`, {
                method: "GET",
                headers: { "Accept": "application/json" }
            });
            if (response.ok === true) {
                const user = await response.json();
                document.getElementById("userId").value = user.id;
                document.getElementById("userName").value = user.name;
                document.getElementById("userAge").value = user.age;
            }
            else {
                const error = await response.json();
                console.log(error.message);
            }
        }

        async function createUser(userName, userAge) {
            const response = await fetch("/api/users", {
                method: "POST",
                headers: { "Accept": "application/json", "Content-Type": "application/json" },
                body: JSON.stringify({
                    name: userName,
                    age: parseInt(userAge, 10)
                })
            });
            if (response.ok === true) {
                const user = await response.json();
                document.querySelector("tbody").append(row(user));
            }
            else {
                const error = await response.json();
                console.log(error.message);
            }
        }

        async function editUser(userId, userName, userAge) {
            const response = await fetch("/api/users", {
                method: "PUT",
                headers: { "Accept": "application/json", "Content-Type": "application/json" },
                body: JSON.stringify({
                    id: userId,
                    name: userName,
                    age: parseInt(userAge, 10)
                })
            });
            if (response.ok === true) {
                const user = await response.json();
                document.querySelector(`tr[data-rowid='${user.id}']`).replaceWith(row(user));
            }
            else {
                const error = await response.json();
                console.log(error.message);
            }
        }

        async function deleteUser(id) {
            const response = await fetch(`/api/users/${id}`, {
                method: "DELETE",
                headers: { "Accept": "application/json" }
            });
            if (response.ok === true) {
                const user = await response.json();
                document.querySelector(`tr[data-rowid='${user.id}']`).remove();
            }
            else {
                const error = await response.json();
                console.log(error.message);
            }
        }

        function reset() {
            document.getElementById("userId").value = "";
            document.getElementById("userName").value = "";
            document.getElementById("userAge").value = "";
        }

        function row(user) {
            const tr = document.createElement("tr");
            tr.setAttribute("data-rowid", user.id);

            const nameTd = document.createElement("td");
            nameTd.append(user.name);
            tr.append(nameTd);

            const ageTd = document.createElement("td");
            ageTd.append(user.age);
            tr.append(ageTd);

            const linksTd = document.createElement("td");

            const editLink = document.createElement("button");
            editLink.append("Редагувати");
            editLink.addEventListener("click", async () => await getUser(user.id));
            linksTd.append(editLink);

            const removeLink = document.createElement("button");
            removeLink.append("Видалити");
            removeLink.addEventListener("click", async () => await deleteUser(user.id));
            linksTd.append(removeLink);

            tr.appendChild(linksTd);
            return tr;
        }

        document.getElementById("resetBtn").addEventListener("click", () => reset());

        document.getElementById("saveBtn").addEventListener("click", async () => {
            const id = document.getElementById("userId").value;
            const name = document.getElementById("userName").value;
            const age = document.getElementById("userAge").value;
            if (id === "")
                await createUser(name, age);
            else
                await editUser(id, name, age);
            reset();
        });

        getUsers();
    </script>
</body>
</html>
```

**Результат роботи REST API**:
![alt text](image-15.png)
*Зображення: Інтерфейс із таблицею користувачів і формою для CRUD-операцій*

### Пояснення роботи API
- **GET /api/users**: Повертає список усіх користувачів у форматі JSON.
- **GET /api/users/{id}**: Повертає одного користувача за ID або помилку 404.
- **POST /api/users**: Створює нового користувача, додаючи унікальний ID.
- **PUT /api/users**: Оновлює дані існуючого користувача.
- **DELETE /api/users/{id}**: Видаляє користувача за ID.

Клієнтська частина використовує JavaScript і `fetch` для взаємодії з API, дозволяючи додавати, редагувати, видаляти та переглядати користувачів.

## Висновок

У цьому матеріалі ми розглянули ключові аспекти роботи з файлами, формами, JSON і створення REST API в ASP.NET Core. Minimal API дозволяє швидко створювати функціональні вебсервіси з мінімальним кодом. Використання `SendFileAsync`, `Request.Form`, `WriteAsJsonAsync` і `ReadFromJsonAsync` забезпечує гнучку обробку даних. У наступних темах можна розглянути використання вбудованих middleware для статичних файлів, маршрутизації або інтеграцію з базами даних.

```json
### Get all users
GET http://localhost:5000/api/users

### Get a user by ID
GET http://localhost:5000/api/users/{{userId}}

### Create a new user
POST http://localhost:5000/api/users
Content-Type: application/json

{
  "name": "John Doe",
  "age": 30
}

### Update a user
PUT http://localhost:5000/api/users
Content-Type: application/json

{
  "id": "{{userId}}",
  "name": "Jane Doe",
  "age": 35
}

### Delete a user
DELETE http://localhost:5000/api/users/{{userId}}

```

# Curl Commands for Program.cs API

To run these commands, ensure your ASP.NET Core application is running, typically on `http://localhost:5000`.

## Get all users

```bash
curl -X GET http://localhost:5000/api/users
```

## Get a user by ID

First, you need to get an existing user ID. You can do this by running the "Get all users" command above. Replace `YOUR_USER_ID` with an actual ID.

```bash
curl -X GET http://localhost:5000/api/users/YOUR_USER_ID
```

## Create a new user

```bash
curl -X POST -H "Content-Type: application/json" -d '{ "name": "John Doe", "age": 30 }' http://localhost:5000/api/users
```

## Update a user

Replace `YOUR_USER_ID` with the ID of the user you want to update.

```bash
curl -X PUT -H "Content-Type: application/json" -d '{ "id": "YOUR_USER_ID", "name": "Jane Doe", "age": 35 }' http://localhost:5000/api/users
```

## Delete a user

Replace `YOUR_USER_ID` with the ID of the user you want to delete.

```bash
curl -X DELETE http://localhost:5000/api/users/YOUR_USER_ID
