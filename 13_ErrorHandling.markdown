# Обробка помилок у ASP.NET Core

Обробка помилок є критично важливою частиною розробки веб-додатків, оскільки вона забезпечує коректне реагування на виключення та HTTP-помилки, покращуючи користувацький досвід і полегшуючи діагностику проблем. У ASP.NET Core для цього використовуються middleware, такі як `UseDeveloperExceptionPage`, `UseExceptionHandler` і `UseStatusCodePages`, а також спеціалізовані підходи для мінімального API. У цьому матеріалі ми розглянемо обробку виключень, HTTP-помилок, а також найкращі практики для мінімального API.

## Обробка виключень

Виключення в ASP.NET Core виникають через помилки в коді, наприклад, ділення на нуль або відсутність даних. Такі помилки корисні для розробників під час тестування, але їх не слід показувати користувачам у продакшені.

### `UseDeveloperExceptionPage`

Middleware `DeveloperExceptionPageMiddleware` призначений для відображення детальної інформації про виключення в режимі розробки.

#### Приклад із `UseDeveloperExceptionPage`
Код із симуляцією виключення:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.UseDeveloperExceptionPage();

app.MapGet("/", async (context) =>
{
    int a = 5;
    int b = 0;
    int c = a / b;
    await context.Response.WriteAsync($"c = {c}");
});

app.Run();
```

**Результат використання `UseDeveloperExceptionPage`**:
*Зображення: Веб-браузер відображає сторінку з деталями виключення `DivideByZeroException`, включаючи стек викликів*

**Пояснення**:
- `UseDeveloperExceptionPage` автоматично додається в режимі розробки (`Development`).
- У продакшені (`Production`) цей middleware не активний, щоб уникнути витоку чутливої інформації.

#### Поведінка в продакшені
Код у продакшен-режимі:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Environment.EnvironmentName = Environments.Production;

app.MapGet("/", async (context) =>
{
    int a = 5;
    int b = 0;
    int c = a / b;
    await context.Response.WriteAsync($"c = {c}");
});

app.Run();
```

**Результат у продакшені**:
*Зображення: Веб-браузер відображає помилку HTTP 500 без деталей виключення*

**Пояснення**:
- У продакшені користувач бачить лише код 500, що вказує на внутрішню помилку сервера.
- Це захищає від витоку інформації, але не інформативно для користувача.

### `UseExceptionHandler`

Middleware `ExceptionHandlerMiddleware` дозволяє налаштувати обробку виключень у продакшені, перенаправляючи запити на спеціальний маршрут або обробник.
`UseExceptionHandler` — це middleware, який додається до конвеєра обробки запитів у ASP.NET Core. Він перехоплює будь-які неперехоплені винятки, що виникають у процесі виконання запиту (у контролерах, фільтрах, інших middleware тощо), і дозволяє розробнику визначити, як обробляти ці помилки. Цей підхід забезпечує централізовану обробку помилок, що зменшує дублювання коду та спрощує підтримку додатку.

Контекст помилки: Middleware надає доступ до об’єкта `IExceptionHandlerFeature`, який містить інформацію про виняток, що дозволяє отримати деталі, такі як тип винятку, повідомлення або стек викликів.

Коли використовувати UseExceptionHandler?
- Для API: Повернення стандартизованих відповідей (наприклад, у форматі ProblemDetails) для клієнтів.
- Для веб-додатків: Перенаправлення на користувацькі сторінки помилок (наприклад, сторінка 500).
- Для логування: Централізоване логування всіх помилок для подальшого аналізу.
- У продакшені: Для приховування деталей винятків від користувачів і відображення дружніх повідомлень про помилки.

#### Приклад із перенаправленням
Код із перенаправленням на маршрут `/error`:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Environment.EnvironmentName = Environments.Production;

if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/error");
}

app.Map("/error", (context) =>
{
    context.Response.StatusCode = StatusCodes.Status500InternalServerError;
    return "Помилка 500: Виникла проблема на сервері";
});

app.MapGet("/", async (context) =>
{
    int a = 5;
    int b = 0;
    int c = a / b;
    await context.Response.WriteAsync($"c = {c}");
});

app.Run();
```

**Результат із `UseExceptionHandler`**:
*Зображення: Веб-браузер відображає "Помилка 500: Виникла проблема на сервері"*

**Пояснення**:
- `UseExceptionHandler("/error")` перенаправляє на маршрут `/error` при виникненні виключення.
- Недолік: прямий запит до `/error` повертає той самий результат.

#### Приклад із делегатом
Код із обробкою виключення в делегаті:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Environment.EnvironmentName = Environments.Production;

if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler(errorApp =>
    {
        errorApp.Run(async context =>
        {
            context.Response.StatusCode = StatusCodes.Status500InternalServerError;
            await context.Response.WriteAsync("Помилка 500: Виникла проблема на сервері");
        });
    });
}

app.MapGet("/", async (context) =>
{
    int a = 5;
    int b = 0;
    int c = a / b;
    await context.Response.WriteAsync($"c = {c}");
});

app.Run();
```

**Результат із делегатом**:
*Зображення: Веб-браузер відображає аналогічний результат, але прямий доступ до `/error` не працює*

**Пояснення**:
- Делегат у `UseExceptionHandler` ізолює обробку помилок, уникаючи прямого доступу до маршруту.

```csharp
app.UseExceptionHandler(errorApp =>
{
    errorApp.Run(async context =>
    {
        var errorFeature = context.Features.Get<IExceptionHandlerFeature>();
        if (errorFeature != null)
        {
            var problemDetails = new ProblemDetails
            {
                Status = 500,
                Title = "Внутрішня помилка сервера",
                Detail = errorFeature.Error.Message, // У продакшені краще приховати деталі
                Instance = context.Request.Path
            };

            context.Response.StatusCode = 500;
            context.Response.ContentType = "application/problem+json";
            await context.Response.WriteAsJsonAsync(problemDetails);
        }
    });
});
```

```csharp
if (errorFeature.Error is DivideByZeroException)
{
    context.Response.StatusCode = 400;
    await context.Response.WriteAsync("Помилка: ділення на нуль.");
}
else
{
    context.Response.StatusCode = 500;
    await context.Response.WriteAsync("Невідома помилка.");
}
```

```csharp
app.UseExceptionHandler(errorApp =>
{
    errorApp.Run(async context =>
    {
        var errorFeature = context.Features.Get<IExceptionHandlerFeature>();
        if (errorFeature != null)
        {
            var exception = errorFeature.Error;
            // Логування помилки
            var logger = context.RequestServices.GetService<ILogger<Program>>();
            logger?.LogError(exception, "Виникла помилка: {Message}", exception.Message);

            // Налаштування відповіді
            context.Response.StatusCode = 500;
            await context.Response.WriteAsync("Виникла помилка. Спробуйте ще раз пізніше.");
        }
    });
});
```

## Обробка HTTP-помилок

HTTP-помилки, такі як 404 (Не знайдено) або 403 (Заборонено), за замовчуванням не надають користувачу детальної інформації. Для їх обробки використовується middleware `StatusCodePagesMiddleware`.

### `UseStatusCodePages`

Базовий приклад обробки HTTP-помилок:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.UseStatusCodePages();

app.MapGet("/hello", () => "Привіт, ITStep!");

app.Run();
```

**Результат використання `UseStatusCodePages`**:
*Зображення: Веб-браузер відображає базову сторінку з повідомленням про помилку, наприклад, "Status Code: 404; Not Found" для запиту до `/invalid`*

**Пояснення**:
- `UseStatusCodePages` додає базову обробку HTTP-помилок.
- Middleware слід розміщувати на початку конвеєра.

### Налаштування повідомлення

Налаштування власного повідомлення для HTTP-помилок:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.UseStatusCodePages("text/plain", "Помилка: Ресурс не знайдено. Код статусу: {0}");

app.MapGet("/hello", () => "Привіт, ITStep!");

app.Run();
```

**Результат із власним повідомленням**:
*Зображення: Веб-браузер відображає "Помилка: Ресурс не знайдено. Код статусу: 404" для запиту до `/invalid`*

### Обробка через `StatusCodeContext`

Детальна обробка HTTP-помилок із використанням `StatusCodeContext`:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.UseStatusCodePages(async context =>
{
    var response = context.HttpContext.Response;
    var path = context.HttpContext.Request.Path;

    response.ContentType = "text/plain; charset=UTF-8";
    if (response.StatusCode == StatusCodes.Status403Forbidden)
    {
        await response.WriteAsync($"Шлях: {path}. Доступ заборонено");
    }
    else if (response.StatusCode == StatusCodes.Status404NotFound)
    {
        await response.WriteAsync($"Ресурс {path} не знайдено");
    }
});

app.MapGet("/hello", () => "Привіт, ITStep!");

app.Run();
```

**Результат із `StatusCodeContext`**:
*Зображення: Веб-браузер відображає "Ресурс /invalid не знайдено" для запиту до `/invalid`*

### `UseStatusCodePagesWithRedirects` і `UseStatusCodePagesWithReExecute`

Ці методи дозволяють перенаправляти запити або повторно виконувати маршрути для обробки HTTP-помилок.

#### Приклад із `UseStatusCodePagesWithRedirects`
Код із перенаправленням:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.UseStatusCodePagesWithRedirects("/error/{0}");

app.MapGet("/hello", () => "Привіт, ITStep!");
app.MapGet("/error/{statusCode}", (int statusCode) => $"Помилка. Код статусу: {statusCode}");

app.Run();
```

**Результат із `UseStatusCodePagesWithRedirects`**:
*Зображення: Веб-браузер відображає "Помилка. Код статусу: 404" для запиту до `/invalid`, статус код 302*

**Пояснення**:
- Перенаправлення повертає код 302, що може бути небажаним для SEO.

#### Приклад із `UseStatusCodePagesWithReExecute`
Код із повторним виконанням:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.UseStatusCodePagesWithReExecute("/error", "?code={0}");

app.MapGet("/hello", () => "Привіт, ITStep!");
app.MapGet("/error", (string code) => $"Помилка. Код статусу: {code}");

app.Run();
```

**Результат із `UseStatusCodePagesWithReExecute`**:
*Зображення: Веб-браузер відображає "Помилка. Код статусу: 404" для запиту до `/invalid`, зберігаючи статус 404*

**Пояснення**:
- `UseStatusCodePagesWithReExecute` зберігає оригінальний статусний код, що краще для SEO.

## Найкращі практики обробки помилок у мінімальному API

Для мінімального API ASP.NET Core важливо дотримуватися структурованого підходу до обробки помилок, щоб забезпечити надійність, безпеку та гарний користувацький досвід. Ось кілька найкращих практик:

### 1. Використовуйте `UseDeveloperExceptionPage` лише в розробці
- **Чому**: У режимі розробки `UseDeveloperExceptionPage` надає детальну інформацію про виключення, але в продакшені це може призвести до витоку чутливих даних.
- **Як**: Умовно додавайте middleware за допомогою `app.Environment.IsDevelopment()`.

#### Приклад
```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}
else
{
    app.UseExceptionHandler(errorApp =>
    {
        errorApp.Run(async context =>
        {
            context.Response.StatusCode = StatusCodes.Status500InternalServerError;
            await context.Response.WriteAsync("Виникла помилка на сервері. Спробуйте пізніше.");
        });
    });
}

app.MapGet("/", async (context) =>
{
    throw new InvalidOperationException("Тестова помилка");
});

app.Run();
```

**Результат**: У розробці відображається детальна сторінка помилки, у продакшені — дружнє повідомлення.

### 2. Використовуйте структуровані відповіді для помилок
- **Чому**: Стандартизовані відповіді (наприклад, у форматі JSON) полегшують обробку помилок у клієнтських додатках.
- **Як**: Використовуйте `ProblemDetails` (RFC 7807) для уніфікованих відповідей.

#### Приклад із `ProblemDetails`
```csharp
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;

var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler(errorApp =>
    {
        errorApp.Run(async context =>
        {
            var exception = context.Features.Get<IExceptionHandlerFeature>()?.Error;
            context.Response.StatusCode = StatusCodes.Status500InternalServerError;
            context.Response.ContentType = "application/problem+json";
            var problem = new ProblemDetails
            {
                Status = StatusCodes.Status500InternalServerError,
                Title = "Внутрішня помилка сервера",
                Detail = exception?.Message
            };
            await context.Response.WriteAsJsonAsync(problem);
        });
    });
}

app.MapGet("/", async (context) =>
{
    throw new InvalidOperationException("Тестова помилка");
});

app.Run();
```

**Результат**: У продакшені повертається JSON із деталями помилки у форматі `ProblemDetails`.

### 3. Логуйте помилки
- **Чому**: Логування допомагає діагностувати проблеми без витоку інформації до клієнта.
- **Як**: Використовуйте `ILogger` у middleware обробки помилок.

#### Приклад із логуванням
```csharp
using Microsoft.AspNetCore.Diagnostics;

var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler(errorApp =>
    {
        errorApp.Run(async context =>
        {
            var logger = context.RequestServices.GetRequiredService<ILogger<Program>>();
            var exception = context.Features.Get<IExceptionHandlerFeature>()?.Error;
            logger.LogError(exception, "Помилка при обробці запиту {Path}", context.Request.Path);
            context.Response.StatusCode = StatusCodes.Status500InternalServerError;
            await context.Response.WriteAsync("Виникла помилка на сервері.");
        });
    });
}

app.MapGet("/", async (context) =>
{
    throw new InvalidOperationException("Тестова помилка");
});

app.Run();
```

**Результат**: Помилка логуються в консоль або інший провайдер логування, користувач бачить дружнє повідомлення.

### 4. Обробляйте HTTP-помилки з `UseStatusCodePagesWithReExecute`
- **Чому**: Зберігання оригінального статусного коду (наприклад, 404) краще для SEO та коректної обробки.
- **Як**: Використовуйте `UseStatusCodePagesWithReExecute` замість `UseStatusCodePagesWithRedirects`.

#### Приклад
```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.UseStatusCodePagesWithReExecute("/error", "?code={0}");

app.MapGet("/hello", () => "Привіт, ITStep!");
app.MapGet("/error", (string code) =>
{
    return Results.Problem(
        title: "Помилка запиту",
        detail: $"Код статусу: {code}",
        statusCode: int.Parse(code)
    );
});

app.Run();
```

**Результат**: Запит до `/invalid` повертає JSON із кодом 404 і деталями помилки.

### 5. Використовуйте `Results.Problem` у мінімальному API
- **Чому**: `Results.Problem` забезпечує стандартизовану обробку помилок у мінімальному API.
- **Як**: Повертайте `Results.Problem` у кінцевих точках.

#### Приклад із `Results.Problem`
```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/data/{id}", (int id, ILogger<Program> logger) =>
{
    if (id <= 0)
    {
        logger.LogWarning("Невалідний ідентифікатор: {Id}", id);
        return Results.Problem(
            statusCode: StatusCodes.Status400BadRequest,
            title: "Невалідний запит",
            detail: "Ідентифікатор має бути більшим за 0"
        );
    }
    return Results.Ok($"Дані для id: {id}");
});

app.Run();
```

**Результат**: Запит до `/data/0` повертає JSON із кодом 400 і деталями помилки.

### 6. Централізована обробка виключень
- **Чому**: Централізований middleware зменшує дублювання коду.
- **Як**: Створіть middleware для обробки всіх виключень.

#### Приклад централізованого middleware
```csharp
using Microsoft.AspNetCore.Diagnostics;

public class ErrorHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ErrorHandlingMiddleware> _logger;

    public ErrorHandlingMiddleware(RequestDelegate next, ILogger<ErrorHandlingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Помилка при обробці запиту {Path}", context.Request.Path);
            context.Response.StatusCode = StatusCodes.Status500InternalServerError;
            context.Response.ContentType = "application/problem+json";
            var problem = new ProblemDetails
            {
                Status = StatusCodes.Status500InternalServerError,
                Title = "Внутрішня помилка сервера",
                Detail = "Виникла несподівана помилка. Спробуйте пізніше."
            };
            await context.Response.WriteAsJsonAsync(problem);
        }
    }
}
```

Код програми:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

if (!app.Environment.IsDevelopment())
{
    app.UseMiddleware<ErrorHandlingMiddleware>();
}
else
{
    app.UseDeveloperExceptionPage();
}

app.MapGet("/", async (context) =>
{
    throw new InvalidOperationException("Тестова помилка");
});

app.Run();
```

**Результат**: У продакшені повертається JSON із кодом 500, помилка логується.

### 7. Тестування обробки помилок
- **Чому**: Тестування забезпечує коректність обробки помилок.
- **Як**: Використовуйте юніт-тести для перевірки middleware і кінцевих точок.

#### Приклад тесту
```csharp
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging.Abstractions;
using Xunit;

public class ErrorHandlingTests
{
    [Fact]
    public async Task Middleware_HandlesException_ReturnsProblemDetails()
    {
        var context = new DefaultHttpContext();
        context.Response.Body = new MemoryStream();
        var middleware = new ErrorHandlingMiddleware(
            next: _ => throw new InvalidOperationException("Тестова помилка"),
            logger: new NullLogger<ErrorHandlingMiddleware>()
        );

        await middleware.InvokeAsync(context);

        Assert.Equal(StatusCodes.Status500InternalServerError, context.Response.StatusCode);
        Assert.Equal("application/problem+json", context.Response.ContentType);
    }
}
```

**Результат**: Тест перевіряє, що middleware повертає коректний код і JSON.

## Висновок

Обробка помилок у ASP.NET Core включає управління виключеннями (`UseDeveloperExceptionPage`, `UseExceptionHandler`) та HTTP-помилками (`UseStatusCodePages`, `UseStatusCodePagesWithReExecute`). У мінімальному API найкращі практики включають умовне використання `UseDeveloperExceptionPage`, структуровані відповіді через `ProblemDetails`, логування помилок, використання `Results.Problem` і централізовану обробку через middleware. Ці підходи забезпечують безпеку, інформативність і легкість підтримки. У наступних темах можна розглянути інтеграцію з зовнішніми системами логування або обробку помилок у MVC-додатках.