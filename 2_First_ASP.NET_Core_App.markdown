# Перший додаток на ASP.NET Core: .NET CLI та Visual Studio

## Частина 1: Створення додатка за допомогою .NET CLI

.NET CLI (командний рядок .NET) дозволяє створювати, налаштовувати та запускати проєкти без графічного інтерфейсу. Цей підхід ідеально підходить для кросплатформної розробки та автоматизації.

### Крок 1: Встановлення .NET SDK
1. Переконайтеся, що .NET SDK встановлено. Завантажте останню версію (наприклад, .NET 8) з [офіційного сайту](https://dotnet.microsoft.com/download).
2. Перевірте встановлення, виконавши команду:
   ```
   dotnet --version
   ```
   Очікуваний результат: виведення версії, наприклад, `8.0.100`.

### Крок 2: Створення проєкту
1. Відкрийте термінал (Command Prompt, PowerShell або будь-який інший).
2. Створіть нову папку для проєкту:
   ```
   mkdir MyFirstApp
   cd MyFirstApp
   ```
3. Створіть новий проєкт Minimal API:
   ```
   dotnet new web -n MyFirstApp
   ```
   Ця команда створює базовий проєкт ASP.NET Core з Minimal API.

### Крок 3: Огляд структури проєкту
Після виконання команди ви отримаєте таку структуру:
- `Program.cs`: Головний файл, де налаштовується вебдодаток.
- `MyFirstApp.csproj`: Файл проєкту з залежностями та конфігурацією.
- `appsettings.json`: Налаштування додатка.

### Крок 4: Запуск додатка
1. Виконайте команду для запуску:
   ```
   dotnet run
   ```
2. Відкрийте браузер і перейдіть за адресою `https://localhost:5001` (або порт, указаний у виводі терміналу). Ви побачите текст "Hello, World!".

![Запуск додатка через .NET CLI](https://metanit.com/sharp/aspnet6/pics/1.29.png)

### Крок 5: Редагування коду
Відкрийте `Program.cs` у текстовому редакторі (наприклад, VS Code). Код виглядатиме приблизно так:

```Csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/", () => "Hello, World!");

app.Run();
```

## Частина 2: Створення додатка за допомогою Visual Studio

## Крок 1: Встановлення Visual Studio
1. Завантажте Visual Studio 2022 (Community, Professional або Enterprise) з [офіційного сайту](https://visualstudio.microsoft.com/).
2. Під час встановлення виберіть робоче навантаження **ASP.NET and web development**.
3. Переконайтеся, що встановлено .NET SDK (наприклад, .NET 8). Перевірити можна, відкривши термінал і виконавши:
   ```
   dotnet --version
   ```
   Очікуваний результат: виведення версії, наприклад, `9.0`.

## Крок 2: Створення проєкту
1. Відкрийте Visual Studio і виберіть **Create a new project**.
2. У пошуковому полі введіть `ASP.NET Core` і виберіть шаблон **ASP.NET Core Web App (Minimal API)**.
3. Задайте ім’я проєкту (наприклад, `MyFirstApp`) і виберіть місце для збереження.
4. У налаштуваннях залиште стандартні параметри (.NET 8, без додаткових опцій).

![Створення проєкту в Visual Studio](https://metanit.com/sharp/aspnet6/pics/1.10.png)

## Крок 3: Огляд структури проєкту
Після створення проєкту Visual Studio згенерує наступні основні файли:
- `Program.cs`: Головний файл, де налаштовується вебдодаток.
- `MyFirstApp.csproj`: Файл проєкту з залежностями та конфігурацією.
- `appsettings.json`: Налаштування додатка.

## Крок 4: Запуск додатка
1. Натисніть **F5** або кнопку **Start** у Visual Studio.
2. Браузер автоматично відкриє сторінку `https://localhost:<порт>`, де ви побачите текст "Hello, World!".

## Що таке Minimal API?

Це так зване Minimal API — спрощена мінімізована модель для запуску вебдодатка в ASP.NET.

Додаток в ASP.NET Core представляє об’єкт `Microsoft.AspNetCore.Builder.WebApplication`. Цей об’єкт налаштовує всю конфігурацію додатка, його маршрути, використовувані залежності тощо. Для створення об’єкта `WebApplication` необхідний спеціальний клас-будівельник — `WebApplicationBuilder`. У файлі `Program.cs` спочатку створюється цей об’єкт за допомогою статичного методу `WebApplication.CreateBuilder`:

```Csharp
var builder = WebApplication.CreateBuilder(args);
```

В якості параметра в метод передаються аргументи, які передаються додатку під час запуску.

Отримавши об’єкт `WebApplicationBuilder`, у нього викликається метод `Build()`, який створює об’єкт `WebApplication`:

```Csharp
var app = builder.Build();
```

За допомогою об’єкта `WebApplication` можна налаштувати всю інфраструктуру додатка — його конфігурацію, маршрути тощо. У файлі `Program.cs` за замовчуванням для додатка визначається один маршрут:

```Csharp
app.MapGet("/", () => "Hello World!");
```

Метод `MapGet()` як перший параметр приймає шлях, за яким можна звернутися до додатка. У даному випадку це шлях `"/"`, тобто корінь вебдодатка — ім’я домену та порт, після яких може йти слеш, наприклад, `https://localhost:7256/`.

Як другий параметр у метод `MapGet()` передається обробник запиту за цим маршрутом у вигляді функції. Тут це лямбда-вираз, який повертає рядок `"Hello World!"`. Саме тому при зверненні до додатка ми побачимо цей рядок у браузері.

Нарешті, для запуску додатка викликається метод `Run()` класу `WebApplication`:

```Csharp
app.Run();
```

В результаті запуститься консольний додаток, і ми зможемо звертатися до нього з різних браузерів.

## Висновок

Visual Studio спрощує створення ASP.NET Core додатків завдяки зручному графічному інтерфейсу та інструментам для дебагінгу. Minimal API дозволяє швидко створювати легкі вебдодатки з мінімальною кількістю коду. У наступних темах можна розглянути додавання нових ендпоінтів або підключення до бази даних.
