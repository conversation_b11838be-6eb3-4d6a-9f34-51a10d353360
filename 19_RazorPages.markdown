# Razor Pages в ASP.NET Core

## Зміст

1. [Глава 1. Введення в Razor Pages](#глава-1-введення-в-razor-pages)
   - [Що таке Razor Pages](#що-таке-razor-pages)
   - [Переваги <PERSON>zor Pages](#переваги-razor-pages)
   - [Структура Razor Pages](#структура-razor-pages)
   - [Перший проект з .NET CLI](#перший-проект-з-net-cli)
   - [Перший проект у Visual Studio](#перший-проект-у-visual-studio)
   - [Додавання Razor Pages до порожнього проекту](#додавання-razor-pages-до-порожнього-проекту)

2. [Глава 2. <PERSON><PERSON>н<PERSON><PERSON> Razor Pages](#глава-2-основи-razor-pages)
   - [Визначення сторінок Razor](#визначення-сторінок-razor)
   - [Синтаксис Razor](#синтаксис-razor)
   - [Модель сторінки Razor](#модель-сторінки-razor)
   - [Обробка запитів. Контекст сторінки Razor](#обробка-запитів-контекст-сторінки-razor)
   - [Передача даних на сторінку Razor в GET-запиті](#передача-даних-на-сторінку-razor-в-get-запиті)
   - [POST-запити та відправка форм](#post-запити-та-відправка-форм)
   - [Прив'язка властивостей сторінок та моделей Razor до параметрів запиту](#привязка-властивостей-сторінок-та-моделей-razor-до-параметрів-запиту)
   - [Параметри маршрутів](#параметри-маршрутів)
   - [Обробники сторінки](#обробники-сторінки)
   - [Повернення результату](#повернення-результату)
   - [Відправка файлів](#відправка-файлів)
   - [Відправка статусних кодів](#відправка-статусних-кодів)
   - [Переадресація](#переадресація)
   - [Передача залежностей на сторінку](#передача-залежностей-на-сторінку)
   - [ViewBag та ViewData](#viewbag-та-viewdata)

3. [Глава 3. Визначення користувацького інтерфейсу](#глава-3-визначення-користувацького-інтерфейсу)
   - [Мастер-сторінки Layout](#мастер-сторінки-layout)
   - [Файл _ViewImports.cshtml](#файл-_viewimportscshtml)
   - [Введення в tag-хелпери](#введення-в-tag-хелпери)
   - [Створення посилань](#створення-посилань)
   - [Робота з формами. Tag-хелпери форм](#робота-з-формами-tag-хелпери-форм)

---

## Глава 1. Введення в Razor Pages

### Що таке Razor Pages

Razor Pages - це спрощена модель програмування для створення веб-додатків в ASP.NET Core, яка дозволяє створювати сторінки з мінімальною кількістю коду. Це альтернатива традиційному підходу MVC (Model-View-Controller), яка особливо підходить для сценаріїв, орієнтованих на сторінки.

**Основні характеристики Razor Pages:**
- Кожна сторінка має свій власний файл `.cshtml` та відповідний файл коду `.cshtml.cs`
- Логіка сторінки інкапсульована в класі PageModel
- Простіша структура для додатків, орієнтованих на сторінки
- Підтримка всіх можливостей ASP.NET Core (маршрутизація, валідація, авторизація тощо)

### Переваги Razor Pages

1. **Простота організації коду** - кожна сторінка має свою логіку в окремому файлі
2. **Менше шаблонного коду** - не потрібно створювати контролери для простих сторінок
3. **Краща продуктивність** - оптимізована маршрутизація
4. **Легкість тестування** - PageModel легко тестувати
5. **Гнучкість** - можна поєднувати з MVC в одному проекті

### Структура Razor Pages

Типовий проект Razor Pages має наступну структуру:

```
MyRazorPagesApp/
├── Pages/
│   ├── Shared/
│   │   ├── _Layout.cshtml
│   │   └── _ViewStart.cshtml
│   ├── Index.cshtml
│   ├── Index.cshtml.cs
│   ├── Privacy.cshtml
│   └── Privacy.cshtml.cs
├── wwwroot/
├── appsettings.json
└── Program.cs
```

**Основні компоненти:**
- **Pages/** - папка з усіма сторінками Razor
- **Shared/** - спільні компоненти (макети, часткові представлення)
- **wwwroot/** - статичні файли (CSS, JS, зображення)
- **Program.cs** - точка входу додатку

---

## Перший проект з .NET CLI

### Встановлення .NET SDK

Перед початком роботи переконайтеся, що у вас встановлено .NET SDK. Перевірити версію можна командою:

```bash
dotnet --version
```

### Створення нового проекту

1. **Створення проекту Razor Pages:**

```bash
dotnet new webapp -n MyFirstRazorApp
```

Ця команда створить новий проект з шаблоном веб-додатку Razor Pages.
![](./image-38.png)

2. **Перехід до папки проекту:**

```bash
cd MyFirstRazorApp
```

3. **Структура створеного проекту:**

```bash
ls -la
```
![](./image-39.png)

### Запуск проекту

1. **Запуск додатку:**

```bash
dotnet run
```

[](image-40.png)

2. **Відкриття в браузері:**

Відкрийте браузер та перейдіть за адресою `https://localhost:5001` або `http://localhost:5000`

### Огляд створених файлів

**Program.cs** - основний файл конфігурації:

```csharp
var builder = WebApplication.CreateBuilder(args);

// Додавання сервісів Razor Pages
builder.Services.AddRazorPages();

var app = builder.Build();

// Конфігурація HTTP pipeline
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.MapStaticAssets();
app.UseRouting();
app.UseAuthorization();

// Мапування Razor Pages
app.MapRazorPages()
   .WithStaticAssets();

app.Run();
```

**Pages/Index.cshtml** - головна сторінка:

```html
@page
@model IndexModel
@{
    ViewData["Title"] = "Головна сторінка";
}

<div class="text-center">
    <h1 class="display-4">Ласкаво просимо</h1>
    <p>Вивчайте <a href="https://docs.microsoft.com/aspnet/core">ASP.NET Core</a>.</p>
</div>
```

**Pages/Index.cshtml.cs** - код сторінки:

```csharp
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace MyFirstRazorApp.Pages
{
    public class IndexModel : PageModel
    {
        private readonly ILogger<IndexModel> _logger;

        public IndexModel(ILogger<IndexModel> logger)
        {
            _logger = logger;
        }

        public void OnGet()
        {
            // Логіка для GET запиту
        }
    }
}
```

---

## Перший проект у Visual Studio

### Створення проекту

1. **Запуск Visual Studio**

Відкрийте Visual Studio та виберіть "Create a new project"

2. **Вибір шаблону проекту**

- Виберіть "ASP.NET Core Web App"
- Переконайтеся, що вибрано C# як мову програмування

[](image-41.png)

3. **Налаштування проекту**

- **Project name:** MyRazorPagesApp
- **Location:** виберіть бажану папку
- **Solution name:** MyRazorPagesApp

[](image-42.png)

4. **Додаткові налаштування**

- **Framework:** .NET 8.0 (або найновіша версія)
- **Authentication type:** None (для початку)
- Залиште інші опції за замовчуванням

[](image-43.png)

### Огляд створеного проекту

Після створення проекту ви побачите наступну структуру в Solution Explorer:

[](image-44.png)

**Основні файли та папки:**
- **Connected Services** - підключені сервіси
- **Dependencies** - залежності проекту
- **Properties** - властивості проекту
- **wwwroot** - статичні файли
- **Pages** - сторінки Razor
- **appsettings.json** - конфігурація додатку
- **Program.cs** - точка входу

### Запуск проекту в Visual Studio

1. **Запуск без налагодження:**

Натисніть `Ctrl + F5` або виберіть "Debug" → "Start Without Debugging"

2. **Запуск з налагодженням:**

Натисніть `F5` або виберіть "Debug" → "Start Debugging"

### Редагування сторінки

1. **Відкриття файлу Index.cshtml:**

Двічі клікніть на файл `Pages/Index.cshtml` в Solution Explorer


2. **Внесення змін:**

Змініть заголовок сторінки:

```html
@page
@model IndexModel
@{
    ViewData["Title"] = "Моя перша Razor сторінка";
}

<div class="text-center">
    <h1 class="display-4">Привіт, світ!</h1>
    <p>Це моя перша сторінка Razor Pages.</p>
    <p>Поточний час: @DateTime.Now.ToString("dd.MM.yyyy HH:mm:ss")</p>
</div>
```

3. **Збереження та перегляд змін:**

Збережіть файл (`Ctrl + S`) та оновіть сторінку в браузері

[](image-45.png)

---

## Додавання Razor Pages до порожнього проекту

### Створення порожнього проекту

1. **Через .NET CLI:**

```bash
dotnet new web -n EmptyToRazorApp
cd EmptyToRazorApp
```

2. **Через Visual Studio:**

Виберіть шаблон "ASP.NET Core Empty" при створенні проекту

### Початковий стан порожнього проекту

**Program.cs** у порожньому проекті:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/", () => "Hello World!");

app.Run();
```

### Додавання підтримки Razor Pages

1. **Оновлення Program.cs:**

```csharp
var builder = WebApplication.CreateBuilder(args);

// Додавання сервісів Razor Pages
builder.Services.AddRazorPages();

var app = builder.Build();

// Мапування Razor Pages
app.MapRazorPages();

app.Run();
```

### Створення структури папок

1. **Створення папки Pages:**

```bash
mkdir Pages
```

2. **Через Visual Studio:**

Правий клік на проект → Add → New Folder

### Створення базового макету

1. **Pages/Shared/_Layout.cshtml:**

```html
<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Мій додаток</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-light bg-white border-bottom box-shadow mb-3">
            <div class="container">
                <a class="navbar-brand" href="/">Мій додаток</a>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link text-dark" href="/">Головна</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-dark" href="/Privacy">Конфіденційність</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted">
        <div class="container">
            &copy; 2024 - Мій додаток
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
```

2. **Pages/Shared/_ViewStart.cshtml:**

```html
@{
    Layout = "_Layout";
}
```

### Створення першої сторінки

1. **Pages/Index.cshtml:**

```html
@page
@model IndexModel
@{
    ViewData["Title"] = "Головна сторінка";
}

<div class="text-center">
    <h1 class="display-4">Ласкаво просимо!</h1>
    <p>Цей проект було створено з порожнього шаблону та доповнено Razor Pages.</p>
    <p>Поточна дата: <strong>@Model.CurrentDate</strong></p>
</div>
```

2. **Pages/Index.cshtml.cs:**

```csharp
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace EmptyToRazorApp.Pages
{
    public class IndexModel : PageModel
    {
        public string CurrentDate { get; set; } = string.Empty;

        public void OnGet()
        {
            CurrentDate = DateTime.Now.ToString("dd MMMM yyyy");
        }
    }
}
```

### Створення додаткової сторінки

1. **Pages/Privacy.cshtml:**

```html
@page
@model PrivacyModel
@{
    ViewData["Title"] = "Політика конфіденційності";
}

<h1>@ViewData["Title"]</h1>

<p>Використовуйте цю сторінку для деталізації політики конфіденційності вашого сайту.</p>

<div class="alert alert-info">
    <h4>Інформація про сторінку:</h4>
    <ul>
        <li>Час створення: @Model.PageCreationTime</li>
        <li>Кількість відвідувань: @Model.VisitCount</li>
    </ul>
</div>
```

2. **Pages/Privacy.cshtml.cs:**

```csharp
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace EmptyToRazorApp.Pages
{
    public class PrivacyModel : PageModel
    {
        public string PageCreationTime { get; set; } = string.Empty;
        public int VisitCount { get; set; }

        public void OnGet()
        {
            PageCreationTime = DateTime.Now.ToString("HH:mm:ss dd.MM.yyyy");

            // Простий лічильник відвідувань (в реальному додатку використовуйте базу даних)
            if (HttpContext.Session.GetInt32("VisitCount") == null)
            {
                HttpContext.Session.SetInt32("VisitCount", 1);
            }
            else
            {
                var count = HttpContext.Session.GetInt32("VisitCount") ?? 0;
                HttpContext.Session.SetInt32("VisitCount", count + 1);
            }

            VisitCount = HttpContext.Session.GetInt32("VisitCount") ?? 0;
        }
    }
}
```

### Додавання підтримки сесій

Для роботи лічильника відвідувань оновіть **Program.cs:**

```csharp
var builder = WebApplication.CreateBuilder(args);

// Додавання сервісів
builder.Services.AddRazorPages();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(30);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

var app = builder.Build();

// Налаштування middleware
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();
app.UseSession(); // Додавання підтримки сесій
app.UseAuthorization();

app.MapRazorPages();

app.Run();
```

### Тестування додатку

1. **Запуск додатку:**

```bash
dotnet run
```

2. **Перевірка навігації:**

Перейдіть на сторінку "Конфіденційність" та перевірте роботу лічильника

## Глава 2. Основи Razor Pages

### Зміст глави

1. [Визначення сторінок Razor](#визначення-сторінок-razor)
2. [Синтаксис Razor](#синтаксис-razor)
3. [Модель сторінки Razor](#модель-сторінки-razor)
4. [Обробка запитів. Контекст сторінки Razor](#обробка-запитів-контекст-сторінки-razor)
5. [Передача даних на сторінку Razor в GET-запиті](#передача-даних-на-сторінку-razor-в-get-запиті)
6. [POST-запити та відправка форм](#post-запити-та-відправка-форм)
7. [Прив'язка властивостей сторінок та моделей Razor до параметрів запиту](#привязка-властивостей-сторінок-та-моделей-razor-до-параметрів-запиту)
8. [Параметри маршрутів](#параметри-маршрутів)
9. [Обробники сторінки](#обробники-сторінки)
10. [Повернення результату](#повернення-результату)
11. [Відправка файлів](#відправка-файлів)
12. [Відправка статусних кодів](#відправка-статусних-кодів)
13. [Переадресація](#переадресація)
14. [Передача залежностей на сторінку](#передача-залежностей-на-сторінку)
15. [ViewBag та ViewData](#viewbag-та-viewdata)

---

### Визначення сторінок Razor

Сторінка Razor - це файл з розширенням `.cshtml`, який містить HTML-розмітку та код C#. Кожна сторінка Razor складається з двох основних частин:

1. **Файл представлення (.cshtml)** - містить HTML та Razor-синтаксис
2. **Файл коду (.cshtml.cs)** - містить логіку сторінки (PageModel)

#### Структура сторінки Razor

**Приклад базової сторінки (Pages/About.cshtml):**

```html
@page
@model AboutModel
@{
    ViewData["Title"] = "Про нас";
}

<h1>@ViewData["Title"]</h1>
<p>Ласкаво просимо на нашу сторінку!</p>
<p>Сьогодні: @Model.CurrentDate</p>
```

**Відповідний файл коду (Pages/About.cshtml.cs):**

```csharp
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace MyApp.Pages
{
    public class AboutModel : PageModel
    {
        public string CurrentDate { get; set; } = string.Empty;

        public void OnGet()
        {
            CurrentDate = DateTime.Now.ToString("dd.MM.yyyy");
        }
    }
}
```

#### Ключові елементи

- **@page** - директива, яка позначає файл як сторінку Razor
- **@model** - вказує тип моделі сторінки
- **PageModel** - базовий клас для моделей сторінок

---

### Синтаксис Razor

Razor - це синтаксис розмітки, який дозволяє вбудовувати код C# в HTML. Основні елементи синтаксису:

#### Основні конструкції

**1. Вираження Razor (@):**

```html
<p>Поточний час: @DateTime.Now</p>
<p>Ім'я користувача: @Model.UserName</p>
<p>Результат обчислення: @(2 + 3)</p>
```

**2. Блоки коду (@{ }):**

```html
@{
    var message = "Привіт, світ!";
    var number = 42;
    var isVisible = true;
}

<p>@message</p>
<p>Число: @number</p>
```

**3. Умовні конструкції:**

```html
@if (Model.IsLoggedIn)
{
    <p>Ласкаво просимо, @Model.UserName!</p>
}
else
{
    <p>Будь ласка, увійдіть в систему.</p>
}
```

**4. Цикли:**

```html
@if (Model.Items != null && Model.Items.Any())
{
    <ul>
        @foreach (var item in Model.Items)
        {
            <li>@item.Name - @item.Price грн</li>
        }
    </ul>
}
else
{
    <p>Товари відсутні.</p>
}
```

**5. Коментарі Razor:**

```html
@* Це коментар Razor, який не буде відображений в HTML *@
<!-- Це HTML коментар, який буде в кінцевому HTML -->
```

#### Екранування символів

```html
<!-- Для відображення символу @ використовуйте @@ -->
<p>Електронна пошта: user@@example.com</p>

<!-- Для складних виразів використовуйте дужки -->
<p>Результат: @(Model.Price * Model.Quantity) грн</p>
```

---

### Модель сторінки Razor

PageModel - це клас, який містить логіку та дані для сторінки Razor. Він наслідується від базового класу `PageModel`.

#### Базова структура PageModel

```csharp
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace MyApp.Pages
{
    public class ProductsModel : PageModel
    {
        // Властивості для зберігання даних
        public List<Product> Products { get; set; } = new();
        public string SearchTerm { get; set; } = string.Empty;
        public int TotalCount { get; set; }

        // Метод для обробки GET запитів
        public void OnGet()
        {
            LoadProducts();
        }

        // Метод для обробки POST запитів
        public IActionResult OnPost()
        {
            if (!ModelState.IsValid)
            {
                LoadProducts();
                return Page();
            }

            // Логіка обробки форми
            return RedirectToPage();
        }

        // Приватні методи
        private void LoadProducts()
        {
            // Логіка завантаження продуктів
            Products = GetProductsFromDatabase();
            TotalCount = Products.Count;
        }

        private List<Product> GetProductsFromDatabase()
        {
            // Симуляція отримання даних з бази
            return new List<Product>
            {
                new Product { Id = 1, Name = "Ноутбук", Price = 25000 },
                new Product { Id = 2, Name = "Миша", Price = 500 },
                new Product { Id = 3, Name = "Клавіатура", Price = 1200 }
            };
        }
    }

    // Модель продукту
    public class Product
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public decimal Price { get; set; }
    }
}
```

#### Життєвий цикл PageModel

1. **Конструктор** - ініціалізація залежностей
2. **OnGet/OnPost** - обробка HTTP запитів
3. **Рендеринг сторінки** - генерація HTML

---

### Обробка запитів. Контекст сторінки Razor

PageModel надає доступ до контексту HTTP запиту через різні властивості та методи.

#### Основні властивості контексту

```csharp
public class ContactModel : PageModel
{
    public string RequestInfo { get; set; } = string.Empty;
    public string UserAgent { get; set; } = string.Empty;
    public string ClientIP { get; set; } = string.Empty;

    public void OnGet()
    {
        // Інформація про запит
        RequestInfo = $"Метод: {Request.Method}, Шлях: {Request.Path}";

        // User Agent браузера
        UserAgent = Request.Headers["User-Agent"].ToString();

        // IP адреса клієнта
        ClientIP = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Невідомо";

        // Параметри запиту
        var queryParams = Request.Query;
        foreach (var param in queryParams)
        {
            // Обробка параметрів
        }
    }
}
```

#### Робота з сесіями

```csharp
public class SessionExampleModel : PageModel
{
    public string SessionData { get; set; } = string.Empty;
    public int VisitCount { get; set; }

    public void OnGet()
    {
        // Читання з сесії
        SessionData = HttpContext.Session.GetString("UserData") ?? "Дані відсутні";

        // Лічильник відвідувань
        VisitCount = HttpContext.Session.GetInt32("VisitCount") ?? 0;
        VisitCount++;
        HttpContext.Session.SetInt32("VisitCount", VisitCount);
    }

    public IActionResult OnPostSaveData(string userData)
    {
        // Збереження в сесію
        HttpContext.Session.SetString("UserData", userData);
        return RedirectToPage();
    }
}
```

#### Робота з cookies

```csharp
public class CookieExampleModel : PageModel
{
    public string CookieValue { get; set; } = string.Empty;

    public void OnGet()
    {
        // Читання cookie
        CookieValue = Request.Cookies["UserPreference"] ?? "За замовчуванням";
    }

    public IActionResult OnPostSetCookie(string value)
    {
        // Встановлення cookie
        var cookieOptions = new CookieOptions
        {
            Expires = DateTime.Now.AddDays(30),
            HttpOnly = true,
            Secure = true
        };

        Response.Cookies.Append("UserPreference", value, cookieOptions);
        return RedirectToPage();
    }
}
```

---

### Передача даних на сторінку Razor в GET-запиті

GET-запити дозволяють передавати дані через URL у вигляді параметрів маршруту або query string. Це забезпечує можливість створення закладок, поділу посилань та SEO-оптимізації.

**Способи передачі даних:**
1. **Параметри маршруту** - `/products/{id}` - частина URL шляху
2. **Query string** - `/search?query=value&page=1` - параметри після знаку `?`
3. **Комбінований підхід** - поєднання обох методів

**Переваги GET запитів:**
- URL містить всю необхідну інформацію
- Можливість створення закладок
- Кешування браузером
- SEO-дружні URL
- Простота тестування та налагодження

#### 1. Параметри маршруту

**Визначення маршруту з параметрами:**

```html
@page "/products/{id:int}"
@model ProductDetailsModel

<h1>Деталі продукту #@Model.ProductId</h1>
<div class="card">
    <div class="card-body">
        <h5 class="card-title">@Model.Product.Name</h5>
        <p class="card-text">Ціна: @Model.Product.Price грн</p>
        <p class="card-text">Опис: @Model.Product.Description</p>
    </div>
</div>
```

**Відповідний PageModel:**

```csharp
public class ProductDetailsModel : PageModel
{
    public int ProductId { get; set; }
    public Product Product { get; set; } = new();

    public IActionResult OnGet(int id)
    {
        ProductId = id;
        Product = GetProductById(id);

        if (Product == null)
        {
            return NotFound();
        }

        return Page();
    }

    private Product? GetProductById(int id)
    {
        // Симуляція отримання продукту з бази даних
        var products = new List<Product>
        {
            new Product { Id = 1, Name = "Ноутбук", Price = 25000, Description = "Потужний ноутбук для роботи" },
            new Product { Id = 2, Name = "Миша", Price = 500, Description = "Бездротова миша" }
        };

        return products.FirstOrDefault(p => p.Id == id);
    }
}
```

#### 2. Query String параметри

**Сторінка з обробкою query параметрів:**

```html
@page "/search"
@model SearchModel

<h1>Пошук продуктів</h1>

<form method="get">
    <div class="row mb-3">
        <div class="col-md-8">
            <input type="text" name="query" value="@Model.Query" class="form-control" placeholder="Введіть назву продукту...">
        </div>
        <div class="col-md-2">
            <select name="category" class="form-control">
                <option value="">Всі категорії</option>
                <option value="electronics" selected="@(Model.Category == "electronics")">Електроніка</option>
                <option value="books" selected="@(Model.Category == "books")">Книги</option>
            </select>
        </div>
        <div class="col-md-2">
            <button type="submit" class="btn btn-primary">Пошук</button>
        </div>
    </div>
</form>

@if (!string.IsNullOrEmpty(Model.Query))
{
    <h3>Результати пошуку для "@Model.Query"</h3>

    @if (Model.Results.Any())
    {
        <div class="row">
            @foreach (var product in Model.Results)
            {
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">@product.Name</h5>
                            <p class="card-text">@product.Price грн</p>
                            <a href="/products/@product.Id" class="btn btn-primary">Деталі</a>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <p>За вашим запитом нічого не знайдено.</p>
    }
}
```

**PageModel для пошуку:**

```csharp
public class SearchModel : PageModel
{
    public string Query { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public List<Product> Results { get; set; } = new();

    public void OnGet(string? query, string? category)
    {
        Query = query ?? string.Empty;
        Category = category ?? string.Empty;

        if (!string.IsNullOrEmpty(Query))
        {
            Results = SearchProducts(Query, Category);
        }
    }

    private List<Product> SearchProducts(string query, string category)
    {
        var allProducts = new List<Product>
        {
            new Product { Id = 1, Name = "Ноутбук Dell", Price = 25000, Category = "electronics" },
            new Product { Id = 2, Name = "Миша Logitech", Price = 500, Category = "electronics" },
            new Product { Id = 3, Name = "Книга C#", Price = 800, Category = "books" },
            new Product { Id = 4, Name = "Клавіатура", Price = 1200, Category = "electronics" }
        };

        var filtered = allProducts.Where(p =>
            p.Name.Contains(query, StringComparison.OrdinalIgnoreCase));

        if (!string.IsNullOrEmpty(category))
        {
            filtered = filtered.Where(p => p.Category == category);
        }

        return filtered.ToList();
    }
}
```

#### 3. Комбінування параметрів маршруту та query string

```html
@page "/category/{categoryName}"
@model CategoryModel

<h1>Категорія: @Model.CategoryName</h1>

<div class="row mb-3">
    <div class="col-md-6">
        <label>Сортування:</label>
        <select onchange="location.href='/category/@Model.CategoryName?sort=' + this.value + '&page=@Model.CurrentPage'">
            <option value="name" selected="@(Model.SortBy == "name")">За назвою</option>
            <option value="price" selected="@(Model.SortBy == "price")">За ціною</option>
        </select>
    </div>
</div>

<!-- Відображення продуктів -->
<div class="row">
    @foreach (var product in Model.Products)
    {
        <div class="col-md-4 mb-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">@product.Name</h5>
                    <p class="card-text">@product.Price грн</p>
                </div>
            </div>
        </div>
    }
</div>

<!-- Пагінація -->
@if (Model.TotalPages > 1)
{
    <nav>
        <ul class="pagination">
            @for (int i = 1; i <= Model.TotalPages; i++)
            {
                <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                    <a class="page-link" href="/category/@Model.CategoryName?page=@i&sort=@Model.SortBy">@i</a>
                </li>
            }
        </ul>
    </nav>
}
```

**PageModel з комбінованими параметрами:**

```csharp
public class CategoryModel : PageModel
{
    public string CategoryName { get; set; } = string.Empty;
    public List<Product> Products { get; set; } = new();
    public string SortBy { get; set; } = "name";
    public int CurrentPage { get; set; } = 1;
    public int TotalPages { get; set; }
    private const int PageSize = 6;

    public IActionResult OnGet(string categoryName, string? sort, int? page)
    {
        CategoryName = categoryName;
        SortBy = sort ?? "name";
        CurrentPage = page ?? 1;

        var allProducts = GetProductsByCategory(categoryName);

        if (!allProducts.Any())
        {
            return NotFound();
        }

        // Сортування
        allProducts = SortBy switch
        {
            "price" => allProducts.OrderBy(p => p.Price).ToList(),
            _ => allProducts.OrderBy(p => p.Name).ToList()
        };

        // Пагінація
        TotalPages = (int)Math.Ceiling(allProducts.Count / (double)PageSize);
        Products = allProducts
            .Skip((CurrentPage - 1) * PageSize)
            .Take(PageSize)
            .ToList();

        return Page();
    }

    private List<Product> GetProductsByCategory(string category)
    {
        var allProducts = new List<Product>
        {
            new Product { Id = 1, Name = "Ноутбук Dell", Price = 25000, Category = "electronics" },
            new Product { Id = 2, Name = "Миша Logitech", Price = 500, Category = "electronics" },
            new Product { Id = 3, Name = "Клавіатура", Price = 1200, Category = "electronics" },
            new Product { Id = 4, Name = "Книга C#", Price = 800, Category = "books" },
            new Product { Id = 5, Name = "Книга JavaScript", Price = 750, Category = "books" }
        };

        return allProducts.Where(p => p.Category == category).ToList();
    }
}
```

---

### POST-запити та відправка форм

POST-запити використовуються для відправки даних на сервер через HTML форми. На відміну від GET, POST передає дані в тілі запиту, що дозволяє передавати великі обсяги інформації та файли.

**Особливості POST запитів:**
- Дані передаються в тілі HTTP запиту
- Немає обмежень на розмір даних
- Підтримка завантаження файлів (`enctype="multipart/form-data"`)
- Не кешуються браузером
- Безпечніші для передачі чутливих даних

**Обробка форм:**
- Метод `OnPost()` обробляє POST запити
- Автоматична прив'язка даних форми до моделі
- Валідація через атрибути та `ModelState`
- Захист від CSRF атак (автоматично в Razor Pages)

#### Базова форма з POST-запитом

**Сторінка контактів (Pages/Contact.cshtml):**

```html
@page
@model ContactModel
@{
    ViewData["Title"] = "Зв'язатися з нами";
}

<h1>@ViewData["Title"]</h1>

@if (Model.IsSubmitted)
{
    <div class="alert alert-success">
        <h4>Дякуємо за ваше повідомлення!</h4>
        <p>Ми зв'яжемося з вами найближчим часом.</p>
    </div>
}
else
{
    <form method="post">
        <div class="mb-3">
            <label asp-for="ContactForm.Name" class="form-label">Ім'я:</label>
            <input asp-for="ContactForm.Name" class="form-control" />
            <span asp-validation-for="ContactForm.Name" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="ContactForm.Email" class="form-label">Email:</label>
            <input asp-for="ContactForm.Email" type="email" class="form-control" />
            <span asp-validation-for="ContactForm.Email" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="ContactForm.Subject" class="form-label">Тема:</label>
            <input asp-for="ContactForm.Subject" class="form-control" />
            <span asp-validation-for="ContactForm.Subject" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="ContactForm.Message" class="form-label">Повідомлення:</label>
            <textarea asp-for="ContactForm.Message" class="form-control" rows="5"></textarea>
            <span asp-validation-for="ContactForm.Message" class="text-danger"></span>
        </div>

        <button type="submit" class="btn btn-primary">Відправити</button>
    </form>
}

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
```

**PageModel для контактної форми:**

```csharp
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;

namespace MyApp.Pages
{
    public class ContactModel : PageModel
    {
        [BindProperty]
        public ContactFormModel ContactForm { get; set; } = new();

        public bool IsSubmitted { get; set; }

        public void OnGet()
        {
            IsSubmitted = false;
        }

        public IActionResult OnPost()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            // Обробка форми (відправка email, збереження в базу даних тощо)
            ProcessContactForm(ContactForm);

            IsSubmitted = true;

            // Очищення форми після успішної відправки
            ContactForm = new ContactFormModel();

            return Page();
        }

        private void ProcessContactForm(ContactFormModel form)
        {
            // Тут може бути логіка відправки email або збереження в базу даних
            // Для прикладу просто логуємо
            Console.WriteLine($"Отримано повідомлення від {form.Name} ({form.Email}): {form.Subject}");
        }
    }

    public class ContactFormModel
    {
        [Required(ErrorMessage = "Ім'я є обов'язковим")]
        [StringLength(100, ErrorMessage = "Ім'я не може бути довшим за 100 символів")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email є обов'язковим")]
        [EmailAddress(ErrorMessage = "Невірний формат email")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "Тема є обов'язковою")]
        [StringLength(200, ErrorMessage = "Тема не може бути довшою за 200 символів")]
        public string Subject { get; set; } = string.Empty;

        [Required(ErrorMessage = "Повідомлення є обов'язковим")]
        [StringLength(1000, ErrorMessage = "Повідомлення не може бути довшим за 1000 символів")]
        public string Message { get; set; } = string.Empty;
    }
}
```

#### Форма з файлами

**Сторінка завантаження файлів:**

```html
@page
@model UploadModel

<h1>Завантаження файлів</h1>

@if (Model.UploadResult != null)
{
    <div class="alert @(Model.UploadResult.Success ? "alert-success" : "alert-danger")">
        @Model.UploadResult.Message
    </div>
}

<form method="post" enctype="multipart/form-data">
    <div class="mb-3">
        <label asp-for="UploadedFile" class="form-label">Виберіть файл:</label>
        <input asp-for="UploadedFile" type="file" class="form-control" accept=".jpg,.jpeg,.png,.pdf" />
        <span asp-validation-for="UploadedFile" class="text-danger"></span>
        <div class="form-text">Дозволені формати: JPG, PNG, PDF. Максимальний розмір: 5 МБ</div>
    </div>

    <div class="mb-3">
        <label asp-for="Description" class="form-label">Опис файлу:</label>
        <input asp-for="Description" class="form-control" />
    </div>

    <button type="submit" class="btn btn-primary">Завантажити</button>
</form>
```

**PageModel для завантаження файлів:**

```csharp
public class UploadModel : PageModel
{
    [BindProperty]
    [Required(ErrorMessage = "Виберіть файл для завантаження")]
    public IFormFile UploadedFile { get; set; } = null!;

    [BindProperty]
    public string Description { get; set; } = string.Empty;

    public UploadResultModel? UploadResult { get; set; }

    public void OnGet()
    {
        UploadResult = null;
    }

    public async Task<IActionResult> OnPostAsync()
    {
        if (!ModelState.IsValid)
        {
            return Page();
        }

        // Перевірка розміру файлу (5 МБ)
        if (UploadedFile.Length > 5 * 1024 * 1024)
        {
            UploadResult = new UploadResultModel
            {
                Success = false,
                Message = "Файл занадто великий. Максимальний розмір: 5 МБ"
            };
            return Page();
        }

        // Перевірка типу файлу
        var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".pdf" };
        var fileExtension = Path.GetExtension(UploadedFile.FileName).ToLowerInvariant();

        if (!allowedExtensions.Contains(fileExtension))
        {
            UploadResult = new UploadResultModel
            {
                Success = false,
                Message = "Недозволений тип файлу"
            };
            return Page();
        }

        try
        {
            // Збереження файлу
            var uploadsFolder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads");
            Directory.CreateDirectory(uploadsFolder);

            var uniqueFileName = Guid.NewGuid().ToString() + fileExtension;
            var filePath = Path.Combine(uploadsFolder, uniqueFileName);

            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await UploadedFile.CopyToAsync(stream);
            }

            UploadResult = new UploadResultModel
            {
                Success = true,
                Message = $"Файл '{UploadedFile.FileName}' успішно завантажено"
            };

            // Очищення форми
            Description = string.Empty;
        }
        catch (Exception ex)
        {
            UploadResult = new UploadResultModel
            {
                Success = false,
                Message = $"Помилка при завантаженні файлу: {ex.Message}"
            };
        }

        return Page();
    }

    public class UploadResultModel
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
```

---

### Прив'язка властивостей сторінок та моделей Razor до параметрів запиту

Атрибут `[BindProperty]` забезпечує автоматичну прив'язку (model binding) даних з HTTP запитів до властивостей PageModel. Це спрощує роботу з формами та параметрами.

**Особливості прив'язки:**
- **За замовчуванням** - тільки для POST запитів
- `[BindProperty(SupportsGet = true)]` - для GET та POST
- `[BindProperty(Name = "custom")]` - кастомна назва параметра
- Автоматичне перетворення типів (string → int, DateTime тощо)

**Джерела даних:**
- Form data (POST форми)
- Query string параметри
- Route параметри
- JSON body (з атрибутом `[FromBody]`)

**Валідація:**
Прив'язані властивості автоматично валідуються за атрибутами валідації (`[Required]`, `[Range]` тощо). Результати валідації доступні через `ModelState.IsValid`.

#### Основи прив'язки властивостей

**Базовий приклад прив'язки:**

```csharp
public class UserProfileModel : PageModel
{
    // Прив'язка тільки для POST запитів (за замовчуванням)
    [BindProperty]
    public UserModel User { get; set; } = new();

    // Прив'язка для GET та POST запитів
    [BindProperty(SupportsGet = true)]
    public string SearchTerm { get; set; } = string.Empty;

    // Прив'язка з іншою назвою параметра
    [BindProperty(Name = "user_id")]
    public int UserId { get; set; }

    public void OnGet()
    {
        // SearchTerm буде автоматично заповнений з query string
        if (!string.IsNullOrEmpty(SearchTerm))
        {
            // Логіка пошуку
        }
    }

    public IActionResult OnPost()
    {
        if (!ModelState.IsValid)
        {
            return Page();
        }

        // User буде автоматично заповнений з даних форми
        SaveUser(User);
        return RedirectToPage();
    }
}

public class UserModel
{
    [Required]
    public string Name { get; set; } = string.Empty;

    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    public int Age { get; set; }
}
```

#### Прив'язка колекцій

**Робота зі списками та масивами:**

```html
@page
@model ShoppingCartModel

<h1>Кошик покупок</h1>

<form method="post">
    @for (int i = 0; i < Model.CartItems.Count; i++)
    {
        <div class="row mb-3">
            <div class="col-md-4">
                <input asp-for="CartItems[i].ProductName" readonly class="form-control" />
                <input asp-for="CartItems[i].ProductId" type="hidden" />
            </div>
            <div class="col-md-2">
                <input asp-for="CartItems[i].Quantity" type="number" min="1" class="form-control" />
            </div>
            <div class="col-md-2">
                <input asp-for="CartItems[i].Price" readonly class="form-control" />
            </div>
            <div class="col-md-2">
                <span class="form-control-plaintext">@(Model.CartItems[i].Quantity * Model.CartItems[i].Price) грн</span>
            </div>
            <div class="col-md-2">
                <button type="submit" asp-page-handler="RemoveItem" asp-route-index="@i" class="btn btn-danger">Видалити</button>
            </div>
        </div>
    }

    <div class="row">
        <div class="col-md-8">
            <strong>Загальна сума: @Model.TotalAmount грн</strong>
        </div>
        <div class="col-md-4">
            <button type="submit" asp-page-handler="UpdateCart" class="btn btn-primary">Оновити кошик</button>
            <button type="submit" asp-page-handler="Checkout" class="btn btn-success">Оформити замовлення</button>
        </div>
    </div>
</form>
```

**PageModel для кошика:**

```csharp
public class ShoppingCartModel : PageModel
{
    [BindProperty]
    public List<CartItemModel> CartItems { get; set; } = new();

    public decimal TotalAmount => CartItems.Sum(item => item.Quantity * item.Price);

    public void OnGet()
    {
        LoadCartItems();
    }

    public IActionResult OnPostUpdateCart()
    {
        if (!ModelState.IsValid)
        {
            return Page();
        }

        // Оновлення кількості товарів
        UpdateCartInSession();
        return RedirectToPage();
    }

    public IActionResult OnPostRemoveItem(int index)
    {
        LoadCartItems();

        if (index >= 0 && index < CartItems.Count)
        {
            CartItems.RemoveAt(index);
            UpdateCartInSession();
        }

        return RedirectToPage();
    }

    public IActionResult OnPostCheckout()
    {
        if (!ModelState.IsValid || !CartItems.Any())
        {
            return Page();
        }

        // Логіка оформлення замовлення
        ProcessOrder();

        // Очищення кошика
        HttpContext.Session.Remove("CartItems");

        return RedirectToPage("/OrderConfirmation");
    }

    private void LoadCartItems()
    {
        // Завантаження з сесії або бази даних
        CartItems = new List<CartItemModel>
        {
            new CartItemModel { ProductId = 1, ProductName = "Ноутбук", Price = 25000, Quantity = 1 },
            new CartItemModel { ProductId = 2, ProductName = "Миша", Price = 500, Quantity = 2 }
        };
    }

    private void UpdateCartInSession()
    {
        // Збереження в сесію
        var json = System.Text.Json.JsonSerializer.Serialize(CartItems);
        HttpContext.Session.SetString("CartItems", json);
    }

    private void ProcessOrder()
    {
        // Логіка обробки замовлення
    }
}

public class CartItemModel
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public decimal Price { get; set; }

    [Range(1, 100, ErrorMessage = "Кількість повинна бути від 1 до 100")]
    public int Quantity { get; set; }
}
```

#### Складна прив'язка з вкладеними об'єктами

**Форма реєстрації з адресою:**

```html
@page
@model RegisterModel

<h1>Реєстрація</h1>

<form method="post">
    <div class="row">
        <div class="col-md-6">
            <h3>Особисті дані</h3>

            <div class="mb-3">
                <label asp-for="Registration.PersonalInfo.FirstName" class="form-label">Ім'я:</label>
                <input asp-for="Registration.PersonalInfo.FirstName" class="form-control" />
                <span asp-validation-for="Registration.PersonalInfo.FirstName" class="text-danger"></span>
            </div>

            <div class="mb-3">
                <label asp-for="Registration.PersonalInfo.LastName" class="form-label">Прізвище:</label>
                <input asp-for="Registration.PersonalInfo.LastName" class="form-control" />
                <span asp-validation-for="Registration.PersonalInfo.LastName" class="text-danger"></span>
            </div>

            <div class="mb-3">
                <label asp-for="Registration.PersonalInfo.Email" class="form-label">Email:</label>
                <input asp-for="Registration.PersonalInfo.Email" type="email" class="form-control" />
                <span asp-validation-for="Registration.PersonalInfo.Email" class="text-danger"></span>
            </div>

            <div class="mb-3">
                <label asp-for="Registration.PersonalInfo.Phone" class="form-label">Телефон:</label>
                <input asp-for="Registration.PersonalInfo.Phone" class="form-control" />
                <span asp-validation-for="Registration.PersonalInfo.Phone" class="text-danger"></span>
            </div>
        </div>

        <div class="col-md-6">
            <h3>Адреса</h3>

            <div class="mb-3">
                <label asp-for="Registration.Address.Street" class="form-label">Вулиця:</label>
                <input asp-for="Registration.Address.Street" class="form-control" />
                <span asp-validation-for="Registration.Address.Street" class="text-danger"></span>
            </div>

            <div class="mb-3">
                <label asp-for="Registration.Address.City" class="form-label">Місто:</label>
                <input asp-for="Registration.Address.City" class="form-control" />
                <span asp-validation-for="Registration.Address.City" class="text-danger"></span>
            </div>

            <div class="mb-3">
                <label asp-for="Registration.Address.PostalCode" class="form-label">Поштовий індекс:</label>
                <input asp-for="Registration.Address.PostalCode" class="form-control" />
                <span asp-validation-for="Registration.Address.PostalCode" class="text-danger"></span>
            </div>

            <div class="mb-3">
                <label asp-for="Registration.Address.Country" class="form-label">Країна:</label>
                <select asp-for="Registration.Address.Country" class="form-control">
                    <option value="">Виберіть країну</option>
                    <option value="UA">Україна</option>
                    <option value="PL">Польща</option>
                    <option value="DE">Німеччина</option>
                </select>
                <span asp-validation-for="Registration.Address.Country" class="text-danger"></span>
            </div>
        </div>
    </div>

    <div class="mb-3">
        <div class="form-check">
            <input asp-for="Registration.AcceptTerms" class="form-check-input" />
            <label asp-for="Registration.AcceptTerms" class="form-check-label">
                Я погоджуюся з умовами використання
            </label>
            <span asp-validation-for="Registration.AcceptTerms" class="text-danger"></span>
        </div>
    </div>

    <button type="submit" class="btn btn-primary">Зареєструватися</button>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
```

**Моделі для складної прив'язки:**

```csharp
public class RegisterModel : PageModel
{
    [BindProperty]
    public RegistrationModel Registration { get; set; } = new();

    public void OnGet()
    {
        // Ініціалізація за замовчуванням
        Registration.Address.Country = "UA";
    }

    public IActionResult OnPost()
    {
        if (!ModelState.IsValid)
        {
            return Page();
        }

        // Обробка реєстрації
        var userId = CreateUser(Registration);

        if (userId > 0)
        {
            return RedirectToPage("/Welcome", new { userId });
        }

        ModelState.AddModelError("", "Помилка при реєстрації");
        return Page();
    }

    private int CreateUser(RegistrationModel registration)
    {
        // Логіка створення користувача
        return new Random().Next(1000, 9999);
    }
}

public class RegistrationModel
{
    public PersonalInfoModel PersonalInfo { get; set; } = new();
    public AddressModel Address { get; set; } = new();

    [Required(ErrorMessage = "Необхідно прийняти умови використання")]
    public bool AcceptTerms { get; set; }
}

public class PersonalInfoModel
{
    [Required(ErrorMessage = "Ім'я є обов'язковим")]
    public string FirstName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Прізвище є обов'язковим")]
    public string LastName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Email є обов'язковим")]
    [EmailAddress(ErrorMessage = "Невірний формат email")]
    public string Email { get; set; } = string.Empty;

    [Phone(ErrorMessage = "Невірний формат телефону")]
    public string Phone { get; set; } = string.Empty;
}

public class AddressModel
{
    [Required(ErrorMessage = "Вулиця є обов'язковою")]
    public string Street { get; set; } = string.Empty;

    [Required(ErrorMessage = "Місто є обов'язковим")]
    public string City { get; set; } = string.Empty;

    [Required(ErrorMessage = "Поштовий індекс є обов'язковим")]
    [RegularExpression(@"^\d{5}$", ErrorMessage = "Поштовий індекс повинен містити 5 цифр")]
    public string PostalCode { get; set; } = string.Empty;

    [Required(ErrorMessage = "Країна є обов'язковою")]
    public string Country { get; set; } = string.Empty;
}
```

---

### Параметри маршрутів

Параметри маршрутів дозволяють передавати дані через URL. Вони визначаються в директиві `@page` у фігурних дужках `{параметр}`. ASP.NET Core автоматично передає значення параметрів у відповідні методи PageModel.

**Основні типи параметрів:**
- **Обов'язкові** - `{id}` - повинні бути присутні в URL
- **Опціональні** - `{id?}` - можуть бути відсутні
- **З обмеженнями** - `{id:int}` - валідуються за типом
- **Зі значеннями за замовчуванням** - `{page:int=1}` - мають початкове значення

**Обмеження параметрів:**
- `:int` - ціле число
- `:min(1)` - мінімальне значення
- `:max(100)` - максимальне значення
- `:regex(pattern)` - регулярний вираз
- `:required` - обов'язковий параметр

#### Базові параметри маршруту

**Простий параметр:**

```html
@page "/user/{id}"
@model UserDetailsModel

<h1>Профіль користувача #@Model.UserId</h1>
<div class="card">
    <div class="card-body">
        <h5 class="card-title">@Model.User.Name</h5>
        <p class="card-text">Email: @Model.User.Email</p>
        <p class="card-text">Дата реєстрації: @Model.User.RegistrationDate.ToString("dd.MM.yyyy")</p>
    </div>
</div>
```

**PageModel з параметром:**

```csharp
public class UserDetailsModel : PageModel
{
    public int UserId { get; set; }
    public UserInfo User { get; set; } = new();

    public IActionResult OnGet(int id)
    {
        UserId = id;
        User = GetUserById(id);

        if (User == null)
        {
            return NotFound();
        }

        return Page();
    }

    private UserInfo? GetUserById(int id)
    {
        // Симуляція отримання користувача з бази даних
        var users = new List<UserInfo>
        {
            new UserInfo { Id = 1, Name = "Іван Петренко", Email = "<EMAIL>", RegistrationDate = DateTime.Now.AddDays(-30) },
            new UserInfo { Id = 2, Name = "Марія Іваненко", Email = "<EMAIL>", RegistrationDate = DateTime.Now.AddDays(-15) }
        };

        return users.FirstOrDefault(u => u.Id == id);
    }
}

public class UserInfo
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public DateTime RegistrationDate { get; set; }
}
```

#### Множинні параметри

**Маршрут з кількома параметрами:**

```html
@page "/blog/{year:int}/{month:int}/{slug}"
@model BlogPostModel

<article>
    <header>
        <h1>@Model.Post.Title</h1>
        <p class="text-muted">
            Опубліковано: @Model.Post.PublishDate.ToString("dd MMMM yyyy")
            | Категорія: @Model.Post.Category
        </p>
    </header>

    <div class="content">
        @Html.Raw(Model.Post.Content)
    </div>

    <footer class="mt-4">
        <a href="/blog/@Model.Year/@Model.Month" class="btn btn-secondary">
            ← Повернутися до архіву
        </a>
    </footer>
</article>
```

**PageModel для блогу:**

```csharp
public class BlogPostModel : PageModel
{
    public int Year { get; set; }
    public int Month { get; set; }
    public string Slug { get; set; } = string.Empty;
    public BlogPost Post { get; set; } = new();

    public IActionResult OnGet(int year, int month, string slug)
    {
        Year = year;
        Month = month;
        Slug = slug;

        Post = GetBlogPost(year, month, slug);

        if (Post == null)
        {
            return NotFound();
        }

        return Page();
    }

    private BlogPost? GetBlogPost(int year, int month, string slug)
    {
        // Симуляція пошуку поста
        var posts = new List<BlogPost>
        {
            new BlogPost
            {
                Title = "Введення в ASP.NET Core",
                Slug = "introduction-aspnet-core",
                Content = "<p>ASP.NET Core - це сучасний фреймворк...</p>",
                PublishDate = new DateTime(2024, 1, 15),
                Category = "Програмування"
            }
        };

        return posts.FirstOrDefault(p =>
            p.PublishDate.Year == year &&
            p.PublishDate.Month == month &&
            p.Slug == slug);
    }
}

public class BlogPost
{
    public string Title { get; set; } = string.Empty;
    public string Slug { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public DateTime PublishDate { get; set; }
    public string Category { get; set; } = string.Empty;
}
```

#### Обмеження параметрів

**Різні типи обмежень:**

```html
@page "/products/{category}/{id:int:min(1)}"
@page "/api/data/{format:regex(^(json|xml)$)}"
@page "/files/{filename:required}"
@page "/archive/{date:datetime}"
```

**Приклад з обмеженнями:**

```csharp
public class ProductCategoryModel : PageModel
{
    public string Category { get; set; } = string.Empty;
    public int ProductId { get; set; }
    public Product Product { get; set; } = new();

    public IActionResult OnGet(string category, int id)
    {
        // id автоматично валідується як int >= 1
        Category = category;
        ProductId = id;

        Product = GetProduct(category, id);

        if (Product == null)
        {
            return NotFound($"Продукт з ID {id} в категорії '{category}' не знайдено");
        }

        return Page();
    }

    private Product? GetProduct(string category, int id)
    {
        // Логіка пошуку продукту
        return new Product { Id = id, Name = $"Продукт {id}", Category = category };
    }
}
```

#### Опціональні параметри

**Параметри зі значеннями за замовчуванням:**

```html
@page "/catalog/{category?}"
@page "/catalog/{category}/{page:int=1}"
@model CatalogModel

<h1>Каталог @(!string.IsNullOrEmpty(Model.Category) ? $"- {Model.Category}" : "")</h1>

<div class="row">
    <div class="col-md-3">
        <h4>Категорії</h4>
        <ul class="list-group">
            <li class="list-group-item @(string.IsNullOrEmpty(Model.Category) ? "active" : "")">
                <a href="/catalog">Всі товари</a>
            </li>
            @foreach (var cat in Model.Categories)
            {
                <li class="list-group-item @(Model.Category == cat ? "active" : "")">
                    <a href="/catalog/@cat">@cat</a>
                </li>
            }
        </ul>
    </div>

    <div class="col-md-9">
        <div class="row">
            @foreach (var product in Model.Products)
            {
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">@product.Name</h5>
                            <p class="card-text">@product.Price грн</p>
                        </div>
                    </div>
                </div>
            }
        </div>

        <!-- Пагінація -->
        @if (Model.TotalPages > 1)
        {
            <nav>
                <ul class="pagination">
                    @for (int i = 1; i <= Model.TotalPages; i++)
                    {
                        <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                            <a class="page-link" href="/catalog/@Model.Category/@i">@i</a>
                        </li>
                    }
                </ul>
            </nav>
        }
    </div>
</div>
```

**PageModel з опціональними параметрами:**

```csharp
public class CatalogModel : PageModel
{
    public string Category { get; set; } = string.Empty;
    public int CurrentPage { get; set; } = 1;
    public List<Product> Products { get; set; } = new();
    public List<string> Categories { get; set; } = new();
    public int TotalPages { get; set; }

    private const int PageSize = 9;

    public void OnGet(string? category, int page = 1)
    {
        Category = category ?? string.Empty;
        CurrentPage = page;

        LoadCategories();
        LoadProducts();
    }

    private void LoadCategories()
    {
        Categories = new List<string> { "Електроніка", "Книги", "Одяг", "Спорт" };
    }

    private void LoadProducts()
    {
        var allProducts = GetAllProducts();

        if (!string.IsNullOrEmpty(Category))
        {
            allProducts = allProducts.Where(p => p.Category == Category).ToList();
        }

        TotalPages = (int)Math.Ceiling(allProducts.Count / (double)PageSize);
        Products = allProducts
            .Skip((CurrentPage - 1) * PageSize)
            .Take(PageSize)
            .ToList();
    }

    private List<Product> GetAllProducts()
    {
        return new List<Product>
        {
            new Product { Id = 1, Name = "Ноутбук", Price = 25000, Category = "Електроніка" },
            new Product { Id = 2, Name = "Книга C#", Price = 800, Category = "Книги" },
            new Product { Id = 3, Name = "Футболка", Price = 500, Category = "Одяг" }
            // ... більше продуктів
        };
    }
}
```

*[Місце для скріншота: Каталог з категоріями та пагінацією]*

---

### Обробники сторінки

Обробники сторінки (Page Handlers) дозволяють обробляти різні дії на одній сторінці без створення окремих сторінок для кожної операції.

**Іменування обробників:**
- `OnGet` - базовий GET обробник
- `OnPost` - базовий POST обробник
- `OnPostCreate` - іменований POST обробник "Create"
- `OnGetDelete` - іменований GET обробник "Delete"

**Виклик обробників:**
- `asp-page-handler="Create"` - вказує конкретний обробник
- URL: `/page?handler=Create` - через query параметр
- Автоматичне мапування методів за іменем

Це дозволяє створювати багатофункціональні сторінки з різними операціями (створення, редагування, видалення) в одному місці.

#### Іменовані обробники

**Сторінка з кількома обробниками:**

```html
@page
@model ProductManagementModel

<h1>Управління продуктами</h1>

@if (!string.IsNullOrEmpty(Model.Message))
{
    <div class="alert @(Model.IsSuccess ? "alert-success" : "alert-danger")">
        @Model.Message
    </div>
}

<div class="row">
    <div class="col-md-6">
        <h3>Додати новий продукт</h3>
        <form method="post" asp-page-handler="Create">
            <div class="mb-3">
                <label asp-for="NewProduct.Name" class="form-label">Назва:</label>
                <input asp-for="NewProduct.Name" class="form-control" />
                <span asp-validation-for="NewProduct.Name" class="text-danger"></span>
            </div>

            <div class="mb-3">
                <label asp-for="NewProduct.Price" class="form-label">Ціна:</label>
                <input asp-for="NewProduct.Price" type="number" step="0.01" class="form-control" />
                <span asp-validation-for="NewProduct.Price" class="text-danger"></span>
            </div>

            <div class="mb-3">
                <label asp-for="NewProduct.Category" class="form-label">Категорія:</label>
                <select asp-for="NewProduct.Category" class="form-control">
                    <option value="">Виберіть категорію</option>
                    <option value="Електроніка">Електроніка</option>
                    <option value="Книги">Книги</option>
                    <option value="Одяг">Одяг</option>
                </select>
                <span asp-validation-for="NewProduct.Category" class="text-danger"></span>
            </div>

            <button type="submit" class="btn btn-primary">Створити</button>
        </form>
    </div>

    <div class="col-md-6">
        <h3>Список продуктів</h3>
        @if (Model.Products.Any())
        {
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Назва</th>
                            <th>Ціна</th>
                            <th>Категорія</th>
                            <th>Дії</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var product in Model.Products)
                        {
                            <tr>
                                <td>@product.Name</td>
                                <td>@product.Price грн</td>
                                <td>@product.Category</td>
                                <td>
                                    <form method="post" asp-page-handler="Delete" asp-route-id="@product.Id" style="display: inline;">
                                        <button type="submit" class="btn btn-sm btn-danger"
                                                onclick="return confirm('Ви впевнені?')">Видалити</button>
                                    </form>
                                    <a href="/products/edit/@product.Id" class="btn btn-sm btn-warning">Редагувати</a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <p>Продукти відсутні.</p>
        }
    </div>
</div>

<!-- Масове видалення -->
<div class="mt-4">
    <h3>Масові операції</h3>
    <form method="post" asp-page-handler="DeleteAll">
        <button type="submit" class="btn btn-danger"
                onclick="return confirm('Видалити всі продукти?')">
            Видалити всі продукти
        </button>
    </form>

    <form method="post" asp-page-handler="ExportData" class="d-inline ms-2">
        <button type="submit" class="btn btn-info">Експортувати дані</button>
    </form>
</div>
```

**PageModel з іменованими обробниками:**

```csharp
public class ProductManagementModel : PageModel
{
    [BindProperty]
    public ProductCreateModel NewProduct { get; set; } = new();

    public List<Product> Products { get; set; } = new();
    public string Message { get; set; } = string.Empty;
    public bool IsSuccess { get; set; }

    public void OnGet()
    {
        LoadProducts();
    }

    // Обробник для створення продукту
    public IActionResult OnPostCreate()
    {
        if (!ModelState.IsValid)
        {
            LoadProducts();
            return Page();
        }

        try
        {
            var product = new Product
            {
                Id = GetNextId(),
                Name = NewProduct.Name,
                Price = NewProduct.Price,
                Category = NewProduct.Category
            };

            SaveProduct(product);

            Message = $"Продукт '{product.Name}' успішно створено!";
            IsSuccess = true;

            // Очищення форми
            NewProduct = new ProductCreateModel();
        }
        catch (Exception ex)
        {
            Message = $"Помилка при створенні продукту: {ex.Message}";
            IsSuccess = false;
        }

        LoadProducts();
        return Page();
    }

    // Обробник для видалення продукту
    public IActionResult OnPostDelete(int id)
    {
        try
        {
            var product = GetProductById(id);
            if (product != null)
            {
                DeleteProduct(id);
                Message = $"Продукт '{product.Name}' видалено!";
                IsSuccess = true;
            }
            else
            {
                Message = "Продукт не знайдено!";
                IsSuccess = false;
            }
        }
        catch (Exception ex)
        {
            Message = $"Помилка при видаленні: {ex.Message}";
            IsSuccess = false;
        }

        LoadProducts();
        return Page();
    }

    // Обробник для видалення всіх продуктів
    public IActionResult OnPostDeleteAll()
    {
        try
        {
            DeleteAllProducts();
            Message = "Всі продукти видалено!";
            IsSuccess = true;
        }
        catch (Exception ex)
        {
            Message = $"Помилка при видаленні: {ex.Message}";
            IsSuccess = false;
        }

        LoadProducts();
        return Page();
    }

    // Обробник для експорту даних
    public IActionResult OnPostExportData()
    {
        try
        {
            var csvData = ExportProductsToCsv();
            var bytes = System.Text.Encoding.UTF8.GetBytes(csvData);

            return File(bytes, "text/csv", "products.csv");
        }
        catch (Exception ex)
        {
            Message = $"Помилка при експорті: {ex.Message}";
            IsSuccess = false;
            LoadProducts();
            return Page();
        }
    }

    // Приватні методи
    private void LoadProducts()
    {
        Products = GetAllProducts();
    }

    private List<Product> GetAllProducts()
    {
        // Симуляція отримання з бази даних
        return new List<Product>
        {
            new Product { Id = 1, Name = "Ноутбук", Price = 25000, Category = "Електроніка" },
            new Product { Id = 2, Name = "Книга", Price = 500, Category = "Книги" }
        };
    }

    private Product? GetProductById(int id)
    {
        return GetAllProducts().FirstOrDefault(p => p.Id == id);
    }

    private void SaveProduct(Product product)
    {
        // Логіка збереження в базу даних
    }

    private void DeleteProduct(int id)
    {
        // Логіка видалення з бази даних
    }

    private void DeleteAllProducts()
    {
        // Логіка видалення всіх продуктів
    }

    private int GetNextId()
    {
        return GetAllProducts().Count > 0 ? GetAllProducts().Max(p => p.Id) + 1 : 1;
    }

    private string ExportProductsToCsv()
    {
        var csv = new StringBuilder();
        csv.AppendLine("Id,Name,Price,Category");

        foreach (var product in Products)
        {
            csv.AppendLine($"{product.Id},{product.Name},{product.Price},{product.Category}");
        }

        return csv.ToString();
    }
}

public class ProductCreateModel
{
    [Required(ErrorMessage = "Назва є обов'язковою")]
    [StringLength(100, ErrorMessage = "Назва не може бути довшою за 100 символів")]
    public string Name { get; set; } = string.Empty;

    [Required(ErrorMessage = "Ціна є обов'язковою")]
    [Range(0.01, 1000000, ErrorMessage = "Ціна повинна бути від 0.01 до 1,000,000")]
    public decimal Price { get; set; }

    [Required(ErrorMessage = "Категорія є обов'язковою")]
    public string Category { get; set; } = string.Empty;
}
```

*[Місце для скріншота: Сторінка управління продуктами з різними обробниками]*

---

### Повернення результату

PageModel може повертати різні типи результатів (IActionResult) для контролю поведінки сторінки та відповіді клієнту.

**Основні типи результатів:**
- `Page()` - відображення поточної сторінки
- `RedirectToPage()` - переадресація на іншу сторінку
- `NotFound()` - HTTP 404 статус
- `BadRequest()` - HTTP 400 статус
- `Forbid()` - HTTP 403 статус
- `File()` - відправка файлу
- `Json()` - JSON відповідь
- `StatusCode(code)` - кастомний HTTP статус

**Умовне повернення:**
Методи можуть повертати різні результати залежно від умов (авторизація, валідація, наявність даних). Це забезпечує гнучку обробку запитів та правильні HTTP відповіді.

#### Основні типи результатів

**Приклад з різними типами результатів:**

```csharp
public class ResultExamplesModel : PageModel
{
    public string UserRole { get; set; } = string.Empty;
    public bool IsAuthenticated { get; set; }

    public IActionResult OnGet()
    {
        // Перевірка аутентифікації
        IsAuthenticated = User.Identity?.IsAuthenticated ?? false;

        if (!IsAuthenticated)
        {
            // Переадресація на сторінку входу
            return RedirectToPage("/Account/Login", new { returnUrl = Request.Path });
        }

        UserRole = GetUserRole();

        // Перевірка ролі користувача
        if (UserRole == "Banned")
        {
            // Повернення статусу 403 Forbidden
            return Forbid();
        }

        // Успішне відображення сторінки
        return Page();
    }

    public IActionResult OnPostUpdateProfile(string name, string email)
    {
        if (string.IsNullOrEmpty(name) || string.IsNullOrEmpty(email))
        {
            // Повернення поточної сторінки з помилкою
            ModelState.AddModelError("", "Всі поля є обов'язковими");
            return Page();
        }

        try
        {
            UpdateUserProfile(name, email);

            // Переадресація з повідомленням про успіх
            TempData["SuccessMessage"] = "Профіль успішно оновлено!";
            return RedirectToPage();
        }
        catch (Exception ex)
        {
            // Повернення сторінки з помилкою
            ModelState.AddModelError("", $"Помилка: {ex.Message}");
            return Page();
        }
    }

    public IActionResult OnPostDownloadReport()
    {
        try
        {
            var reportData = GenerateReport();
            var fileName = $"report_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";

            // Повернення файлу
            return File(reportData, "application/pdf", fileName);
        }
        catch (Exception)
        {
            // Повернення помилки 500
            return StatusCode(500, "Помилка генерації звіту");
        }
    }

    public IActionResult OnPostDeleteAccount()
    {
        try
        {
            DeleteUserAccount();

            // Вихід з системи та переадресація
            return SignOut();
        }
        catch (Exception)
        {
            return BadRequest("Не вдалося видалити акаунт");
        }
    }

    // Приватні методи
    private string GetUserRole()
    {
        // Логіка отримання ролі користувача
        return "User";
    }

    private void UpdateUserProfile(string name, string email)
    {
        // Логіка оновлення профілю
    }

    private byte[] GenerateReport()
    {
        // Логіка генерації звіту
        return new byte[0];
    }

    private void DeleteUserAccount()
    {
        // Логіка видалення акаунту
    }
}
```

#### Робота з JSON результатами

**API-подібні обробники:**

```csharp
public class ApiDataModel : PageModel
{
    public void OnGet()
    {
        // Звичайна сторінка
    }

    public IActionResult OnGetProductsJson(string? category)
    {
        try
        {
            var products = GetProducts(category);

            // Повернення JSON
            return new JsonResult(new
            {
                success = true,
                data = products,
                total = products.Count
            });
        }
        catch (Exception ex)
        {
            return new JsonResult(new
            {
                success = false,
                error = ex.Message
            })
            {
                StatusCode = 500
            };
        }
    }

    public IActionResult OnPostCreateProductJson([FromBody] ProductCreateModel model)
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new
            {
                success = false,
                errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
            })
            {
                StatusCode = 400
            };
        }

        try
        {
            var product = CreateProduct(model);

            return new JsonResult(new
            {
                success = true,
                data = product,
                message = "Продукт створено успішно"
            });
        }
        catch (Exception ex)
        {
            return new JsonResult(new
            {
                success = false,
                error = ex.Message
            })
            {
                StatusCode = 500
            };
        }
    }

    private List<Product> GetProducts(string? category)
    {
        var products = new List<Product>
        {
            new Product { Id = 1, Name = "Ноутбук", Price = 25000, Category = "Електроніка" },
            new Product { Id = 2, Name = "Книга", Price = 500, Category = "Книги" }
        };

        if (!string.IsNullOrEmpty(category))
        {
            products = products.Where(p => p.Category == category).ToList();
        }

        return products;
    }

    private Product CreateProduct(ProductCreateModel model)
    {
        // Логіка створення продукту
        return new Product
        {
            Id = new Random().Next(1000, 9999),
            Name = model.Name,
            Price = model.Price,
            Category = model.Category
        };
    }
}
```

---

### Відправка файлів

Razor Pages надає зручні методи для відправки файлів користувачам через метод `File()`.

**Способи відправки файлів:**
- `File(byte[], contentType, fileName)` - з масиву байтів
- `File(stream, contentType, fileName)` - зі стріму
- `File(filePath, contentType, fileName)` - з файлової системи

**Типи контенту (Content-Type):**
- `application/pdf` - PDF файли
- `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet` - Excel
- `text/csv` - CSV файли
- `application/octet-stream` - бінарні файли

**Режими відправки:**
- **Завантаження** - файл завантажується на комп'ютер користувача
- **Перегляд** - файл відкривається в браузері (якщо підтримується)

Це дозволяє створювати системи завантаження документів, генерації звітів та обміну файлами.

#### Відправка статичних файлів

**Сторінка завантаження файлів:**

```html
@page
@model DownloadModel

<h1>Завантаження файлів</h1>

<div class="row">
    <div class="col-md-6">
        <h3>Документи</h3>
        <div class="list-group">
            @foreach (var file in Model.AvailableFiles)
            {
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">@file.Name</h6>
                        <small class="text-muted">@file.Size | @file.Type</small>
                    </div>
                    <div>
                        <a href="?handler=DownloadFile&fileName=@file.FileName"
                           class="btn btn-sm btn-primary">Завантажити</a>
                        <a href="?handler=PreviewFile&fileName=@file.FileName"
                           class="btn btn-sm btn-secondary" target="_blank">Переглянути</a>
                    </div>
                </div>
            }
        </div>
    </div>

    <div class="col-md-6">
        <h3>Генерація звітів</h3>
        <form method="post">
            <div class="mb-3">
                <label class="form-label">Тип звіту:</label>
                <select name="reportType" class="form-control">
                    <option value="pdf">PDF звіт</option>
                    <option value="excel">Excel файл</option>
                    <option value="csv">CSV дані</option>
                </select>
            </div>

            <div class="mb-3">
                <label class="form-label">Період:</label>
                <select name="period" class="form-control">
                    <option value="week">Останній тиждень</option>
                    <option value="month">Останній місяць</option>
                    <option value="year">Останній рік</option>
                </select>
            </div>

            <button type="submit" asp-page-handler="GenerateReport" class="btn btn-success">
                Згенерувати звіт
            </button>
        </form>
    </div>
</div>

@if (!string.IsNullOrEmpty(Model.Message))
{
    <div class="alert alert-info mt-3">
        @Model.Message
    </div>
}
```

**PageModel для роботи з файлами:**

```csharp
public class DownloadModel : PageModel
{
    public List<FileInfo> AvailableFiles { get; set; } = new();
    public string Message { get; set; } = string.Empty;

    public void OnGet()
    {
        LoadAvailableFiles();
    }

    public IActionResult OnGetDownloadFile(string fileName)
    {
        try
        {
            var filePath = GetFilePath(fileName);

            if (!System.IO.File.Exists(filePath))
            {
                return NotFound("Файл не знайдено");
            }

            var fileBytes = System.IO.File.ReadAllBytes(filePath);
            var contentType = GetContentType(fileName);

            // Відправка файлу з примусовим завантаженням
            return File(fileBytes, contentType, fileName);
        }
        catch (Exception ex)
        {
            Message = $"Помилка завантаження файлу: {ex.Message}";
            LoadAvailableFiles();
            return Page();
        }
    }

    public IActionResult OnGetPreviewFile(string fileName)
    {
        try
        {
            var filePath = GetFilePath(fileName);

            if (!System.IO.File.Exists(filePath))
            {
                return NotFound("Файл не знайдено");
            }

            var fileBytes = System.IO.File.ReadAllBytes(filePath);
            var contentType = GetContentType(fileName);

            // Відправка файлу для перегляду в браузері
            return File(fileBytes, contentType);
        }
        catch (Exception)
        {
            return NotFound("Не вдалося відкрити файл");
        }
    }

    public IActionResult OnPostGenerateReport(string reportType, string period)
    {
        try
        {
            var reportData = GenerateReportData(reportType, period);
            var fileName = $"report_{period}_{DateTime.Now:yyyyMMdd}.{GetFileExtension(reportType)}";
            var contentType = GetContentTypeByReportType(reportType);

            return File(reportData, contentType, fileName);
        }
        catch (Exception ex)
        {
            Message = $"Помилка генерації звіту: {ex.Message}";
            LoadAvailableFiles();
            return Page();
        }
    }

    private void LoadAvailableFiles()
    {
        AvailableFiles = new List<FileInfo>
        {
            new FileInfo
            {
                Name = "Інструкція користувача",
                FileName = "user_guide.pdf",
                Size = "2.5 MB",
                Type = "PDF"
            },
            new FileInfo
            {
                Name = "Прайс-лист",
                FileName = "price_list.xlsx",
                Size = "1.2 MB",
                Type = "Excel"
            },
            new FileInfo
            {
                Name = "Технічна документація",
                FileName = "tech_docs.docx",
                Size = "3.8 MB",
                Type = "Word"
            }
        };
    }

    private string GetFilePath(string fileName)
    {
        var uploadsPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "downloads");
        return Path.Combine(uploadsPath, fileName);
    }

    private string GetContentType(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return extension switch
        {
            ".pdf" => "application/pdf",
            ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".txt" => "text/plain",
            ".csv" => "text/csv",
            _ => "application/octet-stream"
        };
    }

    private byte[] GenerateReportData(string reportType, string period)
    {
        // Симуляція генерації звіту
        return reportType switch
        {
            "pdf" => GeneratePdfReport(period),
            "excel" => GenerateExcelReport(period),
            "csv" => GenerateCsvReport(period),
            _ => throw new ArgumentException("Невідомий тип звіту")
        };
    }

    private byte[] GeneratePdfReport(string period)
    {
        // Логіка генерації PDF
        var content = $"PDF звіт за {period}";
        return System.Text.Encoding.UTF8.GetBytes(content);
    }

    private byte[] GenerateExcelReport(string period)
    {
        // Логіка генерації Excel
        var content = $"Excel звіт за {period}";
        return System.Text.Encoding.UTF8.GetBytes(content);
    }

    private byte[] GenerateCsvReport(string period)
    {
        var csv = new StringBuilder();
        csv.AppendLine("Дата,Продукт,Кількість,Сума");
        csv.AppendLine($"{DateTime.Now:yyyy-MM-dd},Тестовий продукт,10,1000");

        return System.Text.Encoding.UTF8.GetBytes(csv.ToString());
    }

    private string GetFileExtension(string reportType)
    {
        return reportType switch
        {
            "pdf" => "pdf",
            "excel" => "xlsx",
            "csv" => "csv",
            _ => "txt"
        };
    }

    private string GetContentTypeByReportType(string reportType)
    {
        return reportType switch
        {
            "pdf" => "application/pdf",
            "excel" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "csv" => "text/csv",
            _ => "text/plain"
        };
    }

    public class FileInfo
    {
        public string Name { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string Size { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
    }
}
```

*[Місце для скріншота: Сторінка завантаження файлів та генерації звітів]*

---

### Відправка статусних кодів

HTTP статусні коди інформують клієнта про результат обробки запиту. Razor Pages надає методи для повернення стандартних та кастомних статусів.

**Основні статусні коди:**
- **200 OK** - `Ok()` - успішний запит
- **400 Bad Request** - `BadRequest()` - невірний запит
- **401 Unauthorized** - `Unauthorized()` - не авторизований
- **403 Forbidden** - `Forbid()` - доступ заборонено
- **404 Not Found** - `NotFound()` - ресурс не знайдено
- **500 Internal Server Error** - `StatusCode(500)` - помилка сервера
- **503 Service Unavailable** - `StatusCode(503)` - сервіс недоступний

**Кастомні статуси:**
Метод `StatusCode(int code, object value)` дозволяє повертати будь-який HTTP статус з додатковими даними.

Правильне використання статусних кодів покращує взаємодію з API та допомагає клієнтам обробляти різні ситуації.

#### Основні статусні коди

**Приклад з різними статусними кодами:**

```csharp
public class StatusCodeExamplesModel : PageModel
{
    public string RequestedResource { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;

    public IActionResult OnGet(string? resource)
    {
        RequestedResource = resource ?? string.Empty;

        // Демонстрація різних статусних кодів
        return resource switch
        {
            "notfound" => NotFound("Ресурс не знайдено"),
            "forbidden" => Forbid("Доступ заборонено"),
            "unauthorized" => Unauthorized("Необхідна авторизація"),
            "badrequest" => BadRequest("Невірний запит"),
            "servererror" => StatusCode(500, "Внутрішня помилка сервера"),
            "maintenance" => StatusCode(503, "Сервіс тимчасово недоступний"),
            _ => Page()
        };
    }

    public IActionResult OnPostValidateData(string data)
    {
        // Валідація даних
        if (string.IsNullOrEmpty(data))
        {
            return BadRequest("Дані не можуть бути порожніми");
        }

        if (data.Length < 3)
        {
            return BadRequest("Дані занадто короткі");
        }

        if (data.Contains("forbidden"))
        {
            return Forbid("Заборонений контент");
        }

        // Успішна обробка
        return Ok(new { message = "Дані успішно оброблено", data });
    }

    public IActionResult OnGetApiStatus()
    {
        try
        {
            // Перевірка стану системи
            var systemStatus = CheckSystemHealth();

            if (!systemStatus.IsHealthy)
            {
                return StatusCode(503, new
                {
                    status = "unhealthy",
                    message = systemStatus.Message
                });
            }

            return Ok(new
            {
                status = "healthy",
                timestamp = DateTime.UtcNow,
                version = "1.0.0"
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                status = "error",
                message = ex.Message
            });
        }
    }

    private SystemHealthStatus CheckSystemHealth()
    {
        // Симуляція перевірки стану системи
        return new SystemHealthStatus
        {
            IsHealthy = true,
            Message = "Система працює нормально"
        };
    }

    public class SystemHealthStatus
    {
        public bool IsHealthy { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
```

#### Кастомні сторінки помилок

**Сторінка обробки помилок:**

```html
@page "/error/{statusCode:int?}"
@model ErrorModel

@{
    ViewData["Title"] = $"Помилка {Model.StatusCode}";
}

<div class="text-center">
    <h1 class="display-1">@Model.StatusCode</h1>
    <h2 class="mb-4">@Model.ErrorTitle</h2>
    <p class="lead">@Model.ErrorMessage</p>

    @if (!string.IsNullOrEmpty(Model.ErrorDetails))
    {
        <div class="alert alert-warning">
            <strong>Деталі:</strong> @Model.ErrorDetails
        </div>
    }

    <div class="mt-4">
        <a href="/" class="btn btn-primary">Повернутися на головну</a>
        <button onclick="history.back()" class="btn btn-secondary">Назад</button>
    </div>
</div>

@if (Model.ShowContactInfo)
{
    <div class="mt-5 text-center">
        <h4>Потрібна допомога?</h4>
        <p>Зв'яжіться з нашою службою підтримки:</p>
        <p>
            <strong>Email:</strong> <EMAIL><br>
            <strong>Телефон:</strong> +380 (44) 123-45-67
        </p>
    </div>
}
```

**PageModel для обробки помилок:**

```csharp
public class ErrorModel : PageModel
{
    public int StatusCode { get; set; }
    public string ErrorTitle { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public string ErrorDetails { get; set; } = string.Empty;
    public bool ShowContactInfo { get; set; }

    public void OnGet(int? statusCode)
    {
        StatusCode = statusCode ?? 500;

        (ErrorTitle, ErrorMessage, ShowContactInfo) = StatusCode switch
        {
            400 => ("Невірний запит", "Запит містить некоректні дані.", false),
            401 => ("Не авторизовано", "Для доступу до цієї сторінки необхідно увійти в систему.", false),
            403 => ("Доступ заборонено", "У вас немає прав для доступу до цього ресурсу.", true),
            404 => ("Сторінка не знайдена", "Запитувана сторінка не існує або була переміщена.", false),
            500 => ("Внутрішня помилка сервера", "Виникла технічна проблема. Спробуйте пізніше.", true),
            503 => ("Сервіс недоступний", "Сервіс тимчасово недоступний через технічні роботи.", true),
            _ => ("Невідома помилка", "Виникла невідома помилка.", true)
        };

        // Логування помилки
        LogError(StatusCode, Request.Path);
    }

    private void LogError(int statusCode, string path)
    {
        // Логіка логування помилок
        Console.WriteLine($"Error {statusCode} on path: {path}");
    }
}
```

---

### Переадресація

Переадресація (Redirect) перенаправляє користувача на іншу сторінку або URL. Це корисно після успішної обробки форм або для навігації.

**Типи переадресації:**
- `RedirectToPage()` - на іншу Razor сторінку
- `RedirectToAction()` - на дію MVC контролера
- `Redirect()` - на довільний URL
- `RedirectToPagePermanent()` - постійна переадресація (HTTP 301)

**Передача параметрів:**
- `RedirectToPage("/Page", new { id = 1 })` - параметри маршруту
- `RedirectToPage("/Page", new { query = "value" })` - query параметри

**TempData:**
Для передачі повідомлень між переадресаціями використовується `TempData`, яка зберігає дані до наступного запиту.

Переадресація запобігає повторній відправці форм при оновленні сторінки (POST-Redirect-GET pattern).

#### Типи переадресації

**Приклад різних типів переадресації:**

```csharp
public class RedirectExamplesModel : PageModel
{
    public string Message { get; set; } = string.Empty;

    public IActionResult OnGet(string? action)
    {
        return action switch
        {
            "external" => RedirectToExternalSite(),
            "page" => RedirectToAnotherPage(),
            "action" => RedirectToAction(),
            "permanent" => PermanentRedirect(),
            _ => Page()
        };
    }

    public IActionResult OnPostLogin(string username, string password)
    {
        if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
        {
            Message = "Введіть логін та пароль";
            return Page();
        }

        if (ValidateCredentials(username, password))
        {
            // Успішний вхід - переадресація на головну
            TempData["WelcomeMessage"] = $"Ласкаво просимо, {username}!";
            return RedirectToPage("/Index");
        }
        else
        {
            Message = "Невірний логін або пароль";
            return Page();
        }
    }

    public IActionResult OnPostCreateProduct(ProductCreateModel model)
    {
        if (!ModelState.IsValid)
        {
            return Page();
        }

        try
        {
            var productId = CreateProduct(model);

            // Переадресація на сторінку створеного продукту
            TempData["SuccessMessage"] = "Продукт успішно створено!";
            return RedirectToPage("/Products/Details", new { id = productId });
        }
        catch (Exception ex)
        {
            Message = $"Помилка: {ex.Message}";
            return Page();
        }
    }

    public IActionResult OnPostDeleteProduct(int id)
    {
        try
        {
            DeleteProduct(id);

            // Переадресація назад на список з повідомленням
            TempData["InfoMessage"] = "Продукт видалено";
            return RedirectToPage("/Products/Index");
        }
        catch (Exception ex)
        {
            TempData["ErrorMessage"] = $"Помилка видалення: {ex.Message}";
            return RedirectToPage();
        }
    }

    // Переадресація на зовнішній сайт
    private IActionResult RedirectToExternalSite()
    {
        return Redirect("https://docs.microsoft.com/aspnet/core");
    }

    // Переадресація на іншу сторінку
    private IActionResult RedirectToAnotherPage()
    {
        return RedirectToPage("/Products/Index");
    }

    // Переадресація на дію контролера (якщо використовується MVC)
    private IActionResult RedirectToAction()
    {
        return RedirectToAction("Index", "Home");
    }

    // Постійна переадресація (301)
    private IActionResult PermanentRedirect()
    {
        return RedirectToPagePermanent("/NewLocation");
    }

    private bool ValidateCredentials(string username, string password)
    {
        // Логіка перевірки облікових даних
        return username == "admin" && password == "password";
    }

    private int CreateProduct(ProductCreateModel model)
    {
        // Логіка створення продукту
        return new Random().Next(1000, 9999);
    }

    private void DeleteProduct(int id)
    {
        // Логіка видалення продукту
    }
}
```

#### Переадресація з параметрами

**Складна переадресація з параметрами:**

```csharp
public class AdvancedRedirectModel : PageModel
{
    public IActionResult OnPostProcessOrder(OrderModel order)
    {
        if (!ModelState.IsValid)
        {
            return Page();
        }

        try
        {
            var orderId = ProcessOrder(order);

            // Переадресація з множинними параметрами
            return RedirectToPage("/Orders/Confirmation", new
            {
                id = orderId,
                email = order.CustomerEmail,
                total = order.TotalAmount,
                status = "confirmed"
            });
        }
        catch (Exception ex)
        {
            TempData["ErrorMessage"] = ex.Message;
            return RedirectToPage();
        }
    }

    public IActionResult OnGetRedirectWithQuery(string category, int page, string sort)
    {
        // Переадресація зі збереженням query параметрів
        var queryParams = new Dictionary<string, string>
        {
            ["category"] = category,
            ["page"] = page.ToString(),
            ["sort"] = sort,
            ["timestamp"] = DateTime.Now.Ticks.ToString()
        };

        return RedirectToPage("/Products/Catalog", queryParams);
    }

    private int ProcessOrder(OrderModel order)
    {
        // Логіка обробки замовлення
        return new Random().Next(10000, 99999);
    }

    public class OrderModel
    {
        public string CustomerEmail { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
        public List<OrderItem> Items { get; set; } = new();
    }

    public class OrderItem
    {
        public int ProductId { get; set; }
        public int Quantity { get; set; }
        public decimal Price { get; set; }
    }
}
```

*[Місце для скріншота: Сторінка з різними типами переадресації]*

---

### Передача залежностей на сторінку

Dependency Injection (DI) - це патерн, який дозволяє впроваджувати залежності (сервіси) в PageModel через конструктор. Це забезпечує слабке зв'язування та легке тестування.

**Життєвий цикл сервісів:**
- `AddSingleton` - один екземпляр на весь додаток
- `AddScoped` - один екземпляр на HTTP запит
- `AddTransient` - новий екземпляр при кожному запиті

**Переваги DI:**
- Розділення відповідальності
- Легке тестування через mock об'єкти
- Централізована конфігурація сервісів
- Автоматичне управління життєвим циклом

**Реєстрація сервісів:**
Сервіси реєструються в `Program.cs` методами `builder.Services.Add*()` та автоматично впроваджуються в конструктори PageModel.

#### Базове впровадження залежностей

**Створення сервісів:**

```csharp
// Інтерфейс сервісу
public interface IProductService
{
    Task<List<Product>> GetAllProductsAsync();
    Task<Product?> GetProductByIdAsync(int id);
    Task<int> CreateProductAsync(Product product);
    Task<bool> UpdateProductAsync(Product product);
    Task<bool> DeleteProductAsync(int id);
}

// Реалізація сервісу
public class ProductService : IProductService
{
    private readonly ILogger<ProductService> _logger;
    private readonly List<Product> _products; // Симуляція бази даних

    public ProductService(ILogger<ProductService> logger)
    {
        _logger = logger;
        _products = new List<Product>
        {
            new Product { Id = 1, Name = "Ноутбук Dell", Price = 25000, Category = "Електроніка" },
            new Product { Id = 2, Name = "Книга C#", Price = 800, Category = "Книги" },
            new Product { Id = 3, Name = "Футболка", Price = 500, Category = "Одяг" }
        };
    }

    public async Task<List<Product>> GetAllProductsAsync()
    {
        _logger.LogInformation("Отримання всіх продуктів");
        await Task.Delay(100); // Симуляція асинхронної операції
        return _products.ToList();
    }

    public async Task<Product?> GetProductByIdAsync(int id)
    {
        _logger.LogInformation("Отримання продукту з ID: {ProductId}", id);
        await Task.Delay(50);
        return _products.FirstOrDefault(p => p.Id == id);
    }

    public async Task<int> CreateProductAsync(Product product)
    {
        _logger.LogInformation("Створення нового продукту: {ProductName}", product.Name);
        await Task.Delay(100);

        product.Id = _products.Count > 0 ? _products.Max(p => p.Id) + 1 : 1;
        _products.Add(product);

        return product.Id;
    }

    public async Task<bool> UpdateProductAsync(Product product)
    {
        _logger.LogInformation("Оновлення продукту з ID: {ProductId}", product.Id);
        await Task.Delay(100);

        var existingProduct = _products.FirstOrDefault(p => p.Id == product.Id);
        if (existingProduct == null) return false;

        existingProduct.Name = product.Name;
        existingProduct.Price = product.Price;
        existingProduct.Category = product.Category;

        return true;
    }

    public async Task<bool> DeleteProductAsync(int id)
    {
        _logger.LogInformation("Видалення продукту з ID: {ProductId}", id);
        await Task.Delay(100);

        var product = _products.FirstOrDefault(p => p.Id == id);
        if (product == null) return false;

        _products.Remove(product);
        return true;
    }
}
```

**Реєстрація сервісів в Program.cs:**

```csharp
var builder = WebApplication.CreateBuilder(args);

// Додавання сервісів
builder.Services.AddRazorPages();

// Реєстрація кастомних сервісів
builder.Services.AddScoped<IProductService, ProductService>();
builder.Services.AddSingleton<IEmailService, EmailService>();
builder.Services.AddTransient<IReportGenerator, ReportGenerator>();

var app = builder.Build();

// Налаштування pipeline
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();
app.UseAuthorization();

app.MapRazorPages();

app.Run();
```

**PageModel з впровадженими залежностями:**

```csharp
public class ProductsWithDIModel : PageModel
{
    private readonly IProductService _productService;
    private readonly IEmailService _emailService;
    private readonly ILogger<ProductsWithDIModel> _logger;

    // Впровадження залежностей через конструктор
    public ProductsWithDIModel(
        IProductService productService,
        IEmailService emailService,
        ILogger<ProductsWithDIModel> logger)
    {
        _productService = productService;
        _emailService = emailService;
        _logger = logger;
    }

    [BindProperty]
    public ProductCreateModel NewProduct { get; set; } = new();

    public List<Product> Products { get; set; } = new();
    public string Message { get; set; } = string.Empty;
    public bool IsSuccess { get; set; }

    public async Task OnGetAsync()
    {
        try
        {
            _logger.LogInformation("Завантаження сторінки продуктів");
            Products = await _productService.GetAllProductsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Помилка при завантаженні продуктів");
            Message = "Помилка завантаження даних";
            IsSuccess = false;
        }
    }

    public async Task<IActionResult> OnPostCreateAsync()
    {
        if (!ModelState.IsValid)
        {
            await LoadProductsAsync();
            return Page();
        }

        try
        {
            var product = new Product
            {
                Name = NewProduct.Name,
                Price = NewProduct.Price,
                Category = NewProduct.Category
            };

            var productId = await _productService.CreateProductAsync(product);

            // Відправка email сповіщення
            await _emailService.SendProductCreatedNotificationAsync(product);

            Message = $"Продукт '{product.Name}' успішно створено!";
            IsSuccess = true;

            _logger.LogInformation("Продукт створено з ID: {ProductId}", productId);

            // Очищення форми
            NewProduct = new ProductCreateModel();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Помилка при створенні продукту");
            Message = "Помилка при створенні продукту";
            IsSuccess = false;
        }

        await LoadProductsAsync();
        return Page();
    }

    public async Task<IActionResult> OnPostDeleteAsync(int id)
    {
        try
        {
            var product = await _productService.GetProductByIdAsync(id);
            if (product == null)
            {
                Message = "Продукт не знайдено";
                IsSuccess = false;
            }
            else
            {
                var deleted = await _productService.DeleteProductAsync(id);
                if (deleted)
                {
                    Message = $"Продукт '{product.Name}' видалено";
                    IsSuccess = true;
                    _logger.LogInformation("Продукт видалено: {ProductId}", id);
                }
                else
                {
                    Message = "Не вдалося видалити продукт";
                    IsSuccess = false;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Помилка при видаленні продукту {ProductId}", id);
            Message = "Помилка при видаленні продукту";
            IsSuccess = false;
        }

        await LoadProductsAsync();
        return Page();
    }

    private async Task LoadProductsAsync()
    {
        try
        {
            Products = await _productService.GetAllProductsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Помилка при завантаженні продуктів");
            Products = new List<Product>();
        }
    }
}

// Додаткові сервіси
public interface IEmailService
{
    Task SendProductCreatedNotificationAsync(Product product);
}

public class EmailService : IEmailService
{
    private readonly ILogger<EmailService> _logger;

    public EmailService(ILogger<EmailService> logger)
    {
        _logger = logger;
    }

    public async Task SendProductCreatedNotificationAsync(Product product)
    {
        _logger.LogInformation("Відправка email сповіщення про створення продукту: {ProductName}", product.Name);

        // Симуляція відправки email
        await Task.Delay(200);

        _logger.LogInformation("Email сповіщення відправлено успішно");
    }
}

public interface IReportGenerator
{
    Task<byte[]> GenerateProductReportAsync();
}

public class ReportGenerator : IReportGenerator
{
    private readonly IProductService _productService;
    private readonly ILogger<ReportGenerator> _logger;

    public ReportGenerator(IProductService productService, ILogger<ReportGenerator> logger)
    {
        _productService = productService;
        _logger = logger;
    }

    public async Task<byte[]> GenerateProductReportAsync()
    {
        _logger.LogInformation("Генерація звіту по продуктах");

        var products = await _productService.GetAllProductsAsync();

        var csv = new StringBuilder();
        csv.AppendLine("ID,Назва,Ціна,Категорія");

        foreach (var product in products)
        {
            csv.AppendLine($"{product.Id},{product.Name},{product.Price},{product.Category}");
        }

        return System.Text.Encoding.UTF8.GetBytes(csv.ToString());
    }
}
```

*[Місце для скріншота: Сторінка з впровадженими сервісами]*

---

### ViewBag та ViewData

ViewBag та ViewData - це механізми передачі даних з PageModel до представлення (.cshtml файлу). Вони дозволяють передавати додаткову інформацію, яка не є частиною основної моделі.

**ViewData:**
- Словник типу `ViewDataDictionary`
- Доступ через ключі: `ViewData["Key"] = value`
- Строго типізований доступ
- Потребує приведення типів при читанні

**ViewBag:**
- Динамічна обгортка над ViewData
- Доступ через властивості: `ViewBag.Property = value`
- Більш зручний синтаксис
- Перевірка типів під час виконання

**Використання:**
- Передача метаданих (заголовки, описи)
- Налаштування UI (теми, мови)
- Дані для JavaScript
- Допоміжна інформація для представлення

Обидва механізми працюють тільки в межах одного запиту та не зберігаються між переадресаціями.

#### Використання ViewData

**PageModel з ViewData:**

```csharp
public class ViewDataExampleModel : PageModel
{
    private readonly IProductService _productService;

    public ViewDataExampleModel(IProductService productService)
    {
        _productService = productService;
    }

    public List<Product> Products { get; set; } = new();

    public async Task OnGetAsync(string? category, string? sort)
    {
        // Основні дані
        Products = await _productService.GetAllProductsAsync();

        // Передача даних через ViewData
        ViewData["Title"] = "Каталог продуктів";
        ViewData["CurrentCategory"] = category ?? "Всі";
        ViewData["SortOrder"] = sort ?? "name";
        ViewData["TotalProducts"] = Products.Count;
        ViewData["LastUpdated"] = DateTime.Now.ToString("dd.MM.yyyy HH:mm");

        // Складні об'єкти
        ViewData["Categories"] = new List<string> { "Електроніка", "Книги", "Одяг", "Спорт" };
        ViewData["SortOptions"] = new Dictionary<string, string>
        {
            ["name"] = "За назвою",
            ["price"] = "За ціною",
            ["category"] = "За категорією"
        };

        // Налаштування сторінки
        ViewData["ShowFilters"] = true;
        ViewData["EnableSearch"] = true;
        ViewData["PageSize"] = 10;

        // Метадані для SEO
        ViewData["MetaDescription"] = "Каталог продуктів нашого магазину";
        ViewData["MetaKeywords"] = "продукти, каталог, магазин";

        // Фільтрація та сортування
        if (!string.IsNullOrEmpty(category) && category != "Всі")
        {
            Products = Products.Where(p => p.Category == category).ToList();
        }

        Products = sort switch
        {
            "price" => Products.OrderBy(p => p.Price).ToList(),
            "category" => Products.OrderBy(p => p.Category).ThenBy(p => p.Name).ToList(),
            _ => Products.OrderBy(p => p.Name).ToList()
        };

        ViewData["FilteredCount"] = Products.Count;
    }

    public async Task<IActionResult> OnPostSearchAsync(string searchTerm)
    {
        Products = await _productService.GetAllProductsAsync();

        if (!string.IsNullOrEmpty(searchTerm))
        {
            Products = Products.Where(p =>
                p.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                p.Category.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
            ).ToList();
        }

        // Передача результатів пошуку
        ViewData["Title"] = "Результати пошуку";
        ViewData["SearchTerm"] = searchTerm;
        ViewData["SearchResults"] = Products.Count;
        ViewData["ShowBackToAll"] = true;

        return Page();
    }
}
```

#### Використання ViewBag

**PageModel з ViewBag:**

```csharp
public class ViewBagExampleModel : PageModel
{
    public List<Product> Products { get; set; } = new();

    public void OnGet()
    {
        LoadProducts();

        // Використання ViewBag (динамічні властивості)
        ViewBag.Title = "Магазин продуктів";
        ViewBag.WelcomeMessage = "Ласкаво просимо до нашого магазину!";
        ViewBag.CurrentUser = "Іван Петренко";
        ViewBag.IsAdmin = true;
        ViewBag.CartItemsCount = 3;

        // Складні об'єкти
        ViewBag.Navigation = new
        {
            Home = "/",
            Products = "/products",
            About = "/about",
            Contact = "/contact"
        };

        ViewBag.Statistics = new
        {
            TotalProducts = Products.Count,
            Categories = Products.Select(p => p.Category).Distinct().Count(),
            AveragePrice = Products.Average(p => p.Price),
            MostExpensive = Products.Max(p => p.Price),
            Cheapest = Products.Min(p => p.Price)
        };

        // Налаштування UI
        ViewBag.Theme = "light";
        ViewBag.Language = "uk";
        ViewBag.Currency = "UAH";
        ViewBag.ShowPromotions = true;

        // Дані для JavaScript
        ViewBag.ApiEndpoint = "/api/products";
        ViewBag.RefreshInterval = 30000; // 30 секунд
    }

    private void LoadProducts()
    {
        Products = new List<Product>
        {
            new Product { Id = 1, Name = "Ноутбук", Price = 25000, Category = "Електроніка" },
            new Product { Id = 2, Name = "Книга", Price = 500, Category = "Книги" },
            new Product { Id = 3, Name = "Футболка", Price = 800, Category = "Одяг" }
        };
    }
}
```

#### Використання в представленні

**Приклад використання ViewData та ViewBag в .cshtml:**

```html
@page
@model ViewDataExampleModel

@{
    ViewData["Title"] = ViewData["Title"];
    var categories = ViewData["Categories"] as List<string>;
    var sortOptions = ViewData["SortOptions"] as Dictionary<string, string>;
}

<!-- Використання ViewData -->
<div class="container">
    <div class="row">
        <div class="col-12">
            <h1>@ViewData["Title"]</h1>
            <p class="text-muted">
                Категорія: <strong>@ViewData["CurrentCategory"]</strong> |
                Сортування: <strong>@ViewData["SortOrder"]</strong> |
                Оновлено: @ViewData["LastUpdated"]
            </p>
        </div>
    </div>

    <!-- Фільтри (якщо увімкнені) -->
    @if ((bool)ViewData["ShowFilters"])
    {
        <div class="row mb-4">
            <div class="col-md-6">
                <label class="form-label">Категорія:</label>
                <select class="form-control" onchange="filterByCategory(this.value)">
                    <option value="">Всі категорії</option>
                    @if (categories != null)
                    {
                        @foreach (var category in categories)
                        {
                            <option value="@category" selected="@(ViewData["CurrentCategory"].ToString() == category)">
                                @category
                            </option>
                        }
                    }
                </select>
            </div>

            <div class="col-md-6">
                <label class="form-label">Сортування:</label>
                <select class="form-control" onchange="sortProducts(this.value)">
                    @if (sortOptions != null)
                    {
                        @foreach (var option in sortOptions)
                        {
                            <option value="@option.Key" selected="@(ViewData["SortOrder"].ToString() == option.Key)">
                                @option.Value
                            </option>
                        }
                    }
                </select>
            </div>
        </div>
    }

    <!-- Пошук (якщо увімкнений) -->
    @if ((bool)ViewData["EnableSearch"])
    {
        <div class="row mb-4">
            <div class="col-12">
                <form method="post" asp-page-handler="Search">
                    <div class="input-group">
                        <input type="text" name="searchTerm" value="@ViewData["SearchTerm"]"
                               class="form-control" placeholder="Пошук продуктів...">
                        <button type="submit" class="btn btn-primary">Пошук</button>
                    </div>
                </form>
            </div>
        </div>
    }

    <!-- Статистика -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="alert alert-info">
                Знайдено: <strong>@ViewData["FilteredCount"]</strong> з <strong>@ViewData["TotalProducts"]</strong> продуктів
            </div>
        </div>
    </div>

    <!-- Список продуктів -->
    <div class="row">
        @foreach (var product in Model.Products)
        {
            <div class="col-md-4 mb-3">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">@product.Name</h5>
                        <p class="card-text">
                            <strong>@product.Price грн</strong><br>
                            <small class="text-muted">@product.Category</small>
                        </p>
                    </div>
                </div>
            </div>
        }
    </div>
</div>

<!-- Використання ViewBag в JavaScript -->
<script>
    // Дані з ViewBag
    const apiEndpoint = '@ViewBag.ApiEndpoint';
    const refreshInterval = @ViewBag.RefreshInterval;
    const isAdmin = @ViewBag.IsAdmin.ToString().ToLower();

    // Функції для роботи з фільтрами
    function filterByCategory(category) {
        const url = new URL(window.location);
        if (category) {
            url.searchParams.set('category', category);
        } else {
            url.searchParams.delete('category');
        }
        window.location = url;
    }

    function sortProducts(sort) {
        const url = new URL(window.location);
        url.searchParams.set('sort', sort);
        window.location = url;
    }
</script>
```

### Підсумок розширеної Глави 2

У розширеній частині Глави 2 ми детально розглянули:

1. **Параметри маршрутів** - прості та складні параметри, обмеження, опціональні значення
2. **Обробники сторінки** - іменовані обробники для різних дій на одній сторінці
3. **Повернення результату** - різні типи результатів, JSON відповіді
4. **Відправка файлів** - статичні файли, генерація звітів, завантаження
5. **Статусні коди** - правильна обробка помилок та статусів
6. **Переадресація** - різні типи переадресації з параметрами
7. **Впровадження залежностей** - сервіси, логування, асинхронні операції
8. **ViewBag та ViewData** - передача даних до представлення

**Ключові концепції:**
- Гнучка маршрутизація з параметрами та обмеженнями
- Множинні обробники на одній сторінці
- Правильна обробка різних типів результатів
- Робота з файлами та статусними кодами
- Dependency Injection для організації коду
- Ефективна передача даних до представлення

**Практичне застосування:**
- Створення RESTful інтерфейсів
- Обробка складних форм
- Генерація та завантаження файлів
- Організація коду через сервіси
- Створення інтерактивних сторінок

---

## Глава 3. Визначення користувацького інтерфейсу

### Зміст глави

1. [Мастер-сторінки Layout](#мастер-сторінки-layout)
2. [Файл _ViewImports.cshtml](#файл-_viewimportscshtml)
3. [Введення в tag-хелпери](#введення-в-tag-хелпери)
4. [Створення посилань](#створення-посилань)
5. [Робота з формами. Tag-хелпери форм](#робота-з-формами-tag-хелпери-форм)

---

### Мастер-сторінки Layout

Layout (макет) - це мастер-сторінка, яка визначає загальну структуру HTML для всіх сторінок додатку. Вона містить спільні елементи: навігацію, заголовок, підвал та місце для контенту конкретної сторінки.

**Основні переваги Layout:**
- Уникнення дублювання коду
- Єдиний стиль для всього сайту
- Централізоване управління загальними елементами
- Легкість підтримки та оновлення

**Структура Layout:**
- HTML каркас сторінки
- Посилання на CSS та JavaScript
- Навігаційне меню
- `@RenderBody()` - місце для контенту сторінки
- `@RenderSection()` - опціональні секції

#### Створення базового Layout

**Pages/Shared/_Layout.cshtml:**

```html
<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Мій сайт</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Кастомні стилі -->
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="~/favicon.ico">

    <!-- Meta теги для SEO -->
    <meta name="description" content="@ViewData["MetaDescription"]" />
    <meta name="keywords" content="@ViewData["MetaKeywords"]" />
</head>
<body>
    <!-- Заголовок сайту -->
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" asp-page="/Index">
                    <img src="~/images/logo.png" alt="Логотип" height="30" class="me-2">
                    Мій сайт
                </a>

                <!-- Кнопка для мобільного меню -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                        data-bs-target="#navbarNav" aria-controls="navbarNav"
                        aria-expanded="false" aria-label="Переключити навігацію">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Навігаційне меню -->
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" asp-page="/Index">Головна</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-page="/Products/Index">Продукти</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-page="/About">Про нас</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-page="/Contact">Контакти</a>
                        </li>
                    </ul>

                    <!-- Користувацьке меню -->
                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button"
                               data-bs-toggle="dropdown" aria-expanded="false">
                                Акаунт
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" asp-page="/Account/Profile">Профіль</a></li>
                                <li><a class="dropdown-item" asp-page="/Account/Settings">Налаштування</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" asp-page="/Account/Logout">Вихід</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Основний контент -->
    <main class="container my-4">
        <!-- Breadcrumb навігація -->
        @await RenderSectionAsync("Breadcrumb", required: false)

        <!-- Повідомлення -->
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                @TempData["SuccessMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Закрити"></button>
            </div>
        }

        @if (TempData["ErrorMessage"] != null)
        {
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                @TempData["ErrorMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Закрити"></button>
            </div>
        }

        <!-- Контент сторінки -->
        @RenderBody()
    </main>

    <!-- Підвал -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Мій сайт</h5>
                    <p>Опис вашого сайту або компанії.</p>
                </div>
                <div class="col-md-3">
                    <h6>Посилання</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-light">Політика конфіденційності</a></li>
                        <li><a href="#" class="text-light">Умови використання</a></li>
                        <li><a href="#" class="text-light">Підтримка</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h6>Контакти</h6>
                    <p class="mb-1">Email: <EMAIL></p>
                    <p class="mb-1">Телефон: +380 (44) 123-45-67</p>
                </div>
            </div>
            <hr class="my-3">
            <div class="row">
                <div class="col-12 text-center">
                    <p class="mb-0">&copy; @DateTime.Now.Year Мій сайт. Всі права захищені.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    <!-- Секція для додаткових скриптів -->
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
```

*[Місце для скріншота: Базовий Layout з навігацією та підвалом]*

#### Використання секцій

**Приклад сторінки з секціями:**

```html
@page
@model ProductsModel
@{
    ViewData["Title"] = "Каталог продуктів";
    ViewData["MetaDescription"] = "Перегляньте наш каталог продуктів";
}

@section Breadcrumb {
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-page="/Index">Головна</a></li>
            <li class="breadcrumb-item active" aria-current="page">Продукти</li>
        </ol>
    </nav>
}

<div class="row">
    <div class="col-12">
        <h1>@ViewData["Title"]</h1>
        <p>Знайдіть потрібний вам продукт з нашого широкого асортименту.</p>
    </div>
</div>

<!-- Контент сторінки -->
<div class="row">
    @foreach (var product in Model.Products)
    {
        <div class="col-md-4 mb-4">
            <div class="card">
                <img src="@product.ImageUrl" class="card-img-top" alt="@product.Name">
                <div class="card-body">
                    <h5 class="card-title">@product.Name</h5>
                    <p class="card-text">@product.Description</p>
                    <p class="card-text"><strong>@product.Price грн</strong></p>
                    <a asp-page="/Products/Details" asp-route-id="@product.Id"
                       class="btn btn-primary">Деталі</a>
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    <script>
        // Кастомний JavaScript для цієї сторінки
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Сторінка продуктів завантажена');

            // Додавання інтерактивності
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.transition = 'transform 0.3s ease';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
}
```

#### Файл _ViewStart.cshtml

**Pages/_ViewStart.cshtml** автоматично виконується перед кожною сторінкою та встановлює Layout за замовчуванням:

```html
@{
    Layout = "_Layout";
}
```

**Умовний Layout:**

```html
@{
    Layout = ViewContext.RouteData.Values["page"]?.ToString()?.StartsWith("/Admin") == true
        ? "_AdminLayout"
        : "_Layout";
}
```

*[Місце для скріншота: Сторінка з використанням секцій]*

---

### Файл _ViewImports.cshtml

Файл `_ViewImports.cshtml` дозволяє імпортувати простори імен, директиви та tag-хелпери, які будуть доступні на всіх сторінках. Це зменшує дублювання коду та спрощує розробку.

**Основні можливості:**
- Імпорт просторів імен (`@using`)
- Підключення tag-хелперів (`@addTagHelper`)
- Глобальні директиви (`@inject`)
- Спільні налаштування для всіх сторінок

#### Базовий _ViewImports.cshtml

**Pages/_ViewImports.cshtml:**

```html
@using Microsoft.AspNetCore.Identity
@using MyApp
@using MyApp.Models
@using MyApp.Services
@using MyApp.Extensions
@using System.Globalization

@namespace MyApp.Pages
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@addTagHelper *, MyApp

@inject IConfiguration Configuration
@inject IWebHostEnvironment Environment
```

#### Ієрархія _ViewImports

**Глобальний (Pages/_ViewImports.cshtml):**

```html
@using MyApp
@using MyApp.Models
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@inject IConfiguration Configuration
```

**Для адмін панелі (Pages/Admin/_ViewImports.cshtml):**

```html
@using MyApp.Areas.Admin.Models
@using MyApp.Areas.Admin.Services
@inject IAdminService AdminService
@inject IUserManager UserManager
```

**Для продуктів (Pages/Products/_ViewImports.cshtml):**

```html
@using MyApp.Models.Products
@using MyApp.Services.Products
@inject IProductService ProductService
@inject ICategoryService CategoryService
```

#### Кастомні tag-хелпери

**Створення кастомного tag-хелпера:**

```csharp
using Microsoft.AspNetCore.Razor.TagHelpers;

[HtmlTargetElement("alert")]
public class AlertTagHelper : TagHelper
{
    public string Type { get; set; } = "info";
    public string Title { get; set; } = string.Empty;
    public bool Dismissible { get; set; } = true;

    public override void Process(TagHelperContext context, TagHelperOutput output)
    {
        output.TagName = "div";
        output.Attributes.SetAttribute("class", $"alert alert-{Type}" + (Dismissible ? " alert-dismissible fade show" : ""));
        output.Attributes.SetAttribute("role", "alert");

        var content = output.GetChildContentAsync().Result.GetContent();

        var html = "";
        if (!string.IsNullOrEmpty(Title))
        {
            html += $"<h4 class=\"alert-heading\">{Title}</h4>";
        }

        html += content;

        if (Dismissible)
        {
            html += "<button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\" aria-label=\"Закрити\"></button>";
        }

        output.Content.SetHtmlContent(html);
    }
}
```

**Реєстрація в _ViewImports.cshtml:**

```html
@addTagHelper MyApp.TagHelpers.*, MyApp
```

**Використання кастомного tag-хелпера:**

```html
<alert type="success" title="Успіх!" dismissible="true">
    Операція виконана успішно!
</alert>

<alert type="warning">
    Будьте обережні при виконанні цієї дії.
</alert>
```

*[Місце для скріншота: Кастомні alert компоненти]*

---

### Введення в tag-хелпери

Tag-хелпери - це серверні компоненти, які генерують HTML на основі атрибутів. Вони забезпечують строго типізований та IntelliSense-підтримуваний спосіб створення HTML елементів.

**Переваги tag-хелперів:**
- Строга типізація
- IntelliSense підтримка
- Автоматична генерація атрибутів
- Інтеграція з моделями та валідацією
- Чистіший та читабельніший код

**Основні категорії:**
- **Anchor Tag Helper** - для посилань
- **Form Tag Helpers** - для форм та елементів вводу
- **Image Tag Helper** - для зображень
- **Environment Tag Helper** - для умовного контенту
- **Cache Tag Helper** - для кешування

#### Базові tag-хелпери

**Environment Tag Helper:**

```html
<environment include="Development">
    <link rel="stylesheet" href="~/css/site.css" />
    <script src="~/js/site.js"></script>
</environment>

<environment exclude="Development">
    <link rel="stylesheet" href="~/css/site.min.css" asp-append-version="true" />
    <script src="~/js/site.min.js" asp-append-version="true"></script>
</environment>
```

**Image Tag Helper:**

```html
<img src="~/images/logo.png" alt="Логотип" asp-append-version="true" class="img-fluid" />
```

**Cache Tag Helper:**

```html
<cache expires-after="TimeSpan.FromMinutes(30)" vary-by="@Model.CategoryId">
    <div class="expensive-content">
        @foreach (var item in Model.ExpensiveData)
        {
            <p>@item.Name - @item.Description</p>
        }
    </div>
</cache>
```

#### Умовні tag-хелпери

**Створення умовного tag-хелпера:**

```csharp
[HtmlTargetElement(Attributes = "asp-if")]
public class IfTagHelper : TagHelper
{
    [HtmlAttributeName("asp-if")]
    public bool Condition { get; set; }

    public override void Process(TagHelperContext context, TagHelperOutput output)
    {
        if (!Condition)
        {
            output.SuppressOutput();
        }

        output.Attributes.RemoveAll("asp-if");
    }
}
```

**Використання:**

```html
<div asp-if="@Model.IsAdmin" class="admin-panel">
    <h3>Адміністративна панель</h3>
    <p>Тільки для адміністраторів</p>
</div>

<button asp-if="@Model.CanEdit" class="btn btn-primary">Редагувати</button>
<span asp-if="@(!Model.CanEdit)" class="text-muted">Немає прав для редагування</span>
```

*[Місце для скріншота: Приклади різних tag-хелперів]*

---

### Створення посилань

Anchor Tag Helper спрощує створення посилань між сторінками Razor Pages та забезпечує строгу типізацію URL.

**Основні можливості:**
- Автоматична генерація URL
- Строга типізація маршрутів
- Підтримка параметрів маршруту
- Query string параметри
- Фрагменти (якорі)

#### Базові посилання

**Посилання на сторінки:**

```html
<!-- Посилання на головну сторінку -->
<a asp-page="/Index">Головна</a>

<!-- Посилання на сторінку в підпапці -->
<a asp-page="/Products/Index">Каталог продуктів</a>

<!-- Посилання на сторінку з параметром -->
<a asp-page="/Products/Details" asp-route-id="123">Деталі продукту</a>

<!-- Посилання з кількома параметрами -->
<a asp-page="/Products/Category"
   asp-route-category="electronics"
   asp-route-page="2">Електроніка (сторінка 2)</a>
```

#### Посилання з query параметрами

```html
<!-- Query string параметри -->
<a asp-page="/Search"
   asp-route-query="ноутбук"
   asp-route-category="electronics"
   asp-route-sort="price">Пошук ноутбуків</a>

<!-- Генерує: /Search?query=ноутбук&category=electronics&sort=price -->
```

#### Динамічні посилання

**У циклі:**

```html
@foreach (var product in Model.Products)
{
    <div class="card">
        <div class="card-body">
            <h5 class="card-title">@product.Name</h5>
            <p class="card-text">@product.Price грн</p>

            <!-- Посилання на деталі -->
            <a asp-page="/Products/Details"
               asp-route-id="@product.Id"
               class="btn btn-primary">Деталі</a>

            <!-- Посилання на редагування (для адмінів) -->
            @if (Model.IsAdmin)
            {
                <a asp-page="/Admin/Products/Edit"
                   asp-route-id="@product.Id"
                   class="btn btn-warning">Редагувати</a>
            }
        </div>
    </div>
}
```

#### Навігаційне меню з активними посиланнями

**Створення helper для активних посилань:**

```csharp
public static class NavigationExtensions
{
    public static string IsActive(this ViewContext viewContext, string page)
    {
        var currentPage = viewContext.RouteData.Values["page"]?.ToString();
        return string.Equals(currentPage, page, StringComparison.OrdinalIgnoreCase)
            ? "active" : "";
    }

    public static string IsActiveArea(this ViewContext viewContext, string area)
    {
        var currentArea = viewContext.RouteData.Values["area"]?.ToString();
        return string.Equals(currentArea, area, StringComparison.OrdinalIgnoreCase)
            ? "active" : "";
    }
}
```

**Використання в навігації:**

```html
<nav class="navbar navbar-expand-lg">
    <div class="container">
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link @ViewContext.IsActive("/Index")"
                   asp-page="/Index">Головна</a>
            </li>
            <li class="nav-item">
                <a class="nav-link @ViewContext.IsActive("/Products/Index")"
                   asp-page="/Products/Index">Продукти</a>
            </li>
            <li class="nav-item">
                <a class="nav-link @ViewContext.IsActive("/About")"
                   asp-page="/About">Про нас</a>
            </li>
            <li class="nav-item">
                <a class="nav-link @ViewContext.IsActive("/Contact")"
                   asp-page="/Contact">Контакти</a>
            </li>
        </ul>
    </div>
</nav>
```

#### Breadcrumb навігація

**Компонент Breadcrumb:**

```html
@model BreadcrumbModel

<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item">
            <a asp-page="/Index">Головна</a>
        </li>

        @foreach (var item in Model.Items.Take(Model.Items.Count - 1))
        {
            <li class="breadcrumb-item">
                <a asp-page="@item.Page" asp-all-route-data="@item.RouteData">@item.Title</a>
            </li>
        }

        @if (Model.Items.Any())
        {
            <li class="breadcrumb-item active" aria-current="page">
                @Model.Items.Last().Title
            </li>
        }
    </ol>
</nav>
```

**Модель для Breadcrumb:**

```csharp
public class BreadcrumbModel
{
    public List<BreadcrumbItem> Items { get; set; } = new();
}

public class BreadcrumbItem
{
    public string Title { get; set; } = string.Empty;
    public string Page { get; set; } = string.Empty;
    public Dictionary<string, string> RouteData { get; set; } = new();
}
```

#### Посилання з фрагментами

```html
<!-- Посилання на якір на тій же сторінці -->
<a href="#section1">Перейти до розділу 1</a>

<!-- Посилання на якір на іншій сторінці -->
<a asp-page="/Products/Details"
   asp-route-id="123"
   asp-fragment="reviews">Відгуки про продукт</a>

<!-- Генерує: /Products/Details/123#reviews -->
```

*[Місце для скріншота: Навігаційне меню з активними посиланнями]*

---

### Робота з формами. Tag-хелпери форм

Form Tag Helpers забезпечують строго типізовану роботу з формами, автоматичну валідацію та зручну прив'язку до моделей.

**Основні переваги:**
- Автоматична прив'язка до моделей
- Генерація атрибутів валідації
- CSRF захист
- Строга типізація
- IntelliSense підтримка

#### Базова форма

**Проста форма входу:**

```html
@page
@model LoginModel

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h4>Вхід в систему</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    <div asp-validation-summary="All" class="text-danger mb-3"></div>

                    <div class="mb-3">
                        <label asp-for="LoginData.Email" class="form-label"></label>
                        <input asp-for="LoginData.Email" class="form-control" placeholder="Введіть email" />
                        <span asp-validation-for="LoginData.Email" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="LoginData.Password" class="form-label"></label>
                        <input asp-for="LoginData.Password" class="form-control" placeholder="Введіть пароль" />
                        <span asp-validation-for="LoginData.Password" class="text-danger"></span>
                    </div>

                    <div class="mb-3 form-check">
                        <input asp-for="LoginData.RememberMe" class="form-check-input" />
                        <label asp-for="LoginData.RememberMe" class="form-check-label"></label>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Увійти</button>
                    </div>
                </form>

                <div class="mt-3 text-center">
                    <a asp-page="/Account/Register">Створити акаунт</a> |
                    <a asp-page="/Account/ForgotPassword">Забули пароль?</a>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
```

**Модель для форми входу:**

```csharp
public class LoginModel : PageModel
{
    [BindProperty]
    public LoginInputModel LoginData { get; set; } = new();

    public string ReturnUrl { get; set; } = string.Empty;

    public void OnGet(string? returnUrl = null)
    {
        ReturnUrl = returnUrl ?? "/";
    }

    public async Task<IActionResult> OnPostAsync(string? returnUrl = null)
    {
        ReturnUrl = returnUrl ?? "/";

        if (!ModelState.IsValid)
        {
            return Page();
        }

        // Логіка аутентифікації
        var result = await AuthenticateUser(LoginData.Email, LoginData.Password);

        if (result.Succeeded)
        {
            return LocalRedirect(ReturnUrl);
        }

        ModelState.AddModelError(string.Empty, "Невірний email або пароль");
        return Page();
    }

    private async Task<AuthResult> AuthenticateUser(string email, string password)
    {
        // Логіка перевірки користувача
        await Task.Delay(100); // Симуляція
        return new AuthResult { Succeeded = email == "<EMAIL>" && password == "password" };
    }
}

public class LoginInputModel
{
    [Required(ErrorMessage = "Email є обов'язковим")]
    [EmailAddress(ErrorMessage = "Невірний формат email")]
    [Display(Name = "Email")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Пароль є обов'язковим")]
    [DataType(DataType.Password)]
    [Display(Name = "Пароль")]
    public string Password { get; set; } = string.Empty;

    [Display(Name = "Запам'ятати мене")]
    public bool RememberMe { get; set; }
}

public class AuthResult
{
    public bool Succeeded { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
}
```

*[Місце для скріншота: Форма входу з валідацією]*

#### Складна форма з різними елементами

**Форма створення продукту:**

```html
@page
@model CreateProductModel

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4>Створення нового продукту</h4>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                    <!-- Основна інформація -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Product.Name" class="form-label"></label>
                                <input asp-for="Product.Name" class="form-control" />
                                <span asp-validation-for="Product.Name" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Product.Price" class="form-label"></label>
                                <input asp-for="Product.Price" class="form-control" />
                                <span asp-validation-for="Product.Price" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Категорія -->
                    <div class="mb-3">
                        <label asp-for="Product.CategoryId" class="form-label"></label>
                        <select asp-for="Product.CategoryId" class="form-select" asp-items="@Model.Categories">
                            <option value="">Виберіть категорію</option>
                        </select>
                        <span asp-validation-for="Product.CategoryId" class="text-danger"></span>
                    </div>

                    <!-- Опис -->
                    <div class="mb-3">
                        <label asp-for="Product.Description" class="form-label"></label>
                        <textarea asp-for="Product.Description" class="form-control" rows="4"></textarea>
                        <span asp-validation-for="Product.Description" class="text-danger"></span>
                    </div>

                    <!-- Характеристики -->
                    <div class="mb-3">
                        <label class="form-label">Характеристики</label>
                        <div id="specifications">
                            @for (int i = 0; i < Model.Product.Specifications.Count; i++)
                            {
                                <div class="row mb-2 specification-row">
                                    <div class="col-md-4">
                                        <input asp-for="Product.Specifications[i].Name"
                                               class="form-control" placeholder="Назва характеристики" />
                                    </div>
                                    <div class="col-md-6">
                                        <input asp-for="Product.Specifications[i].Value"
                                               class="form-control" placeholder="Значення" />
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-danger btn-sm remove-spec">Видалити</button>
                                    </div>
                                </div>
                            }
                        </div>
                        <button type="button" id="add-specification" class="btn btn-secondary btn-sm">
                            Додати характеристику
                        </button>
                    </div>

                    <!-- Зображення -->
                    <div class="mb-3">
                        <label asp-for="ImageFile" class="form-label"></label>
                        <input asp-for="ImageFile" class="form-control" accept="image/*" />
                        <span asp-validation-for="ImageFile" class="text-danger"></span>
                        <div class="form-text">Дозволені формати: JPG, PNG, GIF. Максимальний розмір: 5 МБ</div>
                    </div>

                    <!-- Опції -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input asp-for="Product.IsActive" class="form-check-input" />
                            <label asp-for="Product.IsActive" class="form-check-label"></label>
                        </div>

                        <div class="form-check">
                            <input asp-for="Product.IsFeatured" class="form-check-input" />
                            <label asp-for="Product.IsFeatured" class="form-check-label"></label>
                        </div>
                    </div>

                    <!-- Теги -->
                    <div class="mb-3">
                        <label asp-for="TagsInput" class="form-label"></label>
                        <input asp-for="TagsInput" class="form-control"
                               placeholder="Введіть теги через кому" />
                        <div class="form-text">Приклад: електроніка, ноутбук, Dell</div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a asp-page="/Products/Index" class="btn btn-secondary">Скасувати</a>
                        <button type="submit" class="btn btn-primary">Створити продукт</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Попередній перегляд -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>Попередній перегляд</h5>
            </div>
            <div class="card-body">
                <div id="preview">
                    <h6 id="preview-name">Назва продукту</h6>
                    <p id="preview-price" class="text-success">0 грн</p>
                    <p id="preview-description" class="text-muted">Опис продукту</p>
                    <div id="preview-image" class="mb-3">
                        <img id="image-preview" src="#" alt="Попередній перегляд"
                             class="img-fluid" style="display: none;" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        // Динамічне додавання характеристик
        let specIndex = @Model.Product.Specifications.Count;

        document.getElementById('add-specification').addEventListener('click', function() {
            const container = document.getElementById('specifications');
            const newRow = document.createElement('div');
            newRow.className = 'row mb-2 specification-row';
            newRow.innerHTML = `
                <div class="col-md-4">
                    <input name="Product.Specifications[${specIndex}].Name"
                           class="form-control" placeholder="Назва характеристики" />
                </div>
                <div class="col-md-6">
                    <input name="Product.Specifications[${specIndex}].Value"
                           class="form-control" placeholder="Значення" />
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-danger btn-sm remove-spec">Видалити</button>
                </div>
            `;
            container.appendChild(newRow);
            specIndex++;
        });

        // Видалення характеристик
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('remove-spec')) {
                e.target.closest('.specification-row').remove();
            }
        });

        // Попередній перегляд зображення
        document.querySelector('input[type="file"]').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('image-preview');
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });

        // Живий попередній перегляд
        document.querySelector('input[name="Product.Name"]').addEventListener('input', function(e) {
            document.getElementById('preview-name').textContent = e.target.value || 'Назва продукту';
        });

        document.querySelector('input[name="Product.Price"]').addEventListener('input', function(e) {
            document.getElementById('preview-price').textContent = (e.target.value || '0') + ' грн';
        });

        document.querySelector('textarea[name="Product.Description"]').addEventListener('input', function(e) {
            document.getElementById('preview-description').textContent = e.target.value || 'Опис продукту';
        });
    </script>
}
```

**Модель для складної форми:**

```csharp
public class CreateProductModel : PageModel
{
    [BindProperty]
    public ProductCreateModel Product { get; set; } = new();

    [BindProperty]
    public IFormFile? ImageFile { get; set; }

    [BindProperty]
    public string TagsInput { get; set; } = string.Empty;

    public SelectList Categories { get; set; } = new(new List<SelectListItem>());

    public void OnGet()
    {
        LoadCategories();
        InitializeProduct();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        if (!ModelState.IsValid)
        {
            LoadCategories();
            return Page();
        }

        try
        {
            // Обробка зображення
            if (ImageFile != null)
            {
                Product.ImageUrl = await SaveImageAsync(ImageFile);
            }

            // Обробка тегів
            if (!string.IsNullOrEmpty(TagsInput))
            {
                Product.Tags = TagsInput.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(tag => tag.Trim())
                    .ToList();
            }

            // Збереження продукту
            var productId = await CreateProductAsync(Product);

            TempData["SuccessMessage"] = $"Продукт '{Product.Name}' успішно створено!";
            return RedirectToPage("/Products/Details", new { id = productId });
        }
        catch (Exception ex)
        {
            ModelState.AddModelError("", $"Помилка створення продукту: {ex.Message}");
            LoadCategories();
            return Page();
        }
    }

    private void LoadCategories()
    {
        var categories = new List<SelectListItem>
        {
            new SelectListItem { Value = "1", Text = "Електроніка" },
            new SelectListItem { Value = "2", Text = "Книги" },
            new SelectListItem { Value = "3", Text = "Одяг" },
            new SelectListItem { Value = "4", Text = "Спорт" }
        };

        Categories = new SelectList(categories, "Value", "Text");
    }

    private void InitializeProduct()
    {
        Product.Specifications = new List<SpecificationModel>
        {
            new SpecificationModel()
        };
    }

    private async Task<string> SaveImageAsync(IFormFile imageFile)
    {
        // Логіка збереження зображення
        var fileName = Guid.NewGuid().ToString() + Path.GetExtension(imageFile.FileName);
        var filePath = Path.Combine("wwwroot", "images", "products", fileName);

        using (var stream = new FileStream(filePath, FileMode.Create))
        {
            await imageFile.CopyToAsync(stream);
        }

        return $"/images/products/{fileName}";
    }

    private async Task<int> CreateProductAsync(ProductCreateModel product)
    {
        // Логіка створення продукту в базі даних
        await Task.Delay(100); // Симуляція
        return new Random().Next(1000, 9999);
    }
}

public class ProductCreateModel
{
    [Required(ErrorMessage = "Назва є обов'язковою")]
    [StringLength(100, ErrorMessage = "Назва не може бути довшою за 100 символів")]
    [Display(Name = "Назва продукту")]
    public string Name { get; set; } = string.Empty;

    [Required(ErrorMessage = "Ціна є обов'язковою")]
    [Range(0.01, 1000000, ErrorMessage = "Ціна повинна бути від 0.01 до 1,000,000")]
    [Display(Name = "Ціна (грн)")]
    public decimal Price { get; set; }

    [Required(ErrorMessage = "Категорія є обов'язковою")]
    [Display(Name = "Категорія")]
    public int CategoryId { get; set; }

    [StringLength(1000, ErrorMessage = "Опис не може бути довшим за 1000 символів")]
    [Display(Name = "Опис")]
    public string Description { get; set; } = string.Empty;

    [Display(Name = "Активний")]
    public bool IsActive { get; set; } = true;

    [Display(Name = "Рекомендований")]
    public bool IsFeatured { get; set; }

    public string ImageUrl { get; set; } = string.Empty;
    public List<string> Tags { get; set; } = new();
    public List<SpecificationModel> Specifications { get; set; } = new();
}

public class SpecificationModel
{
    public string Name { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
}
```