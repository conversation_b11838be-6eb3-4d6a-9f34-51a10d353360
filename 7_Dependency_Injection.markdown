# Внедрення залежностей (Dependency Injection) в ASP.NET Core

Внедрення залежностей (Dependency Injection, DI) є ключовим механізмом у ASP.NET Core, який дозволяє створювати слабозв’язані компоненти програми. Це забезпечує гнучкість, легкість тестування та розширюваність системи. У цьому матеріалі ми розглянемо основи DI, роботу з `IServiceCollection`, створення власних сервісів, способи отримання залежностей, управління їхнім життєвим циклом та множинну реєстрацію сервісів.

## Основи Dependency Injection

Dependency Injection дозволяє компонентам програми залежати від абстракцій (наприклад, інтерфейсів), а не від конкретних реалізацій. Це зменшує зв’язність між класами, полегшує заміну компонентів та спрощує тестування.

### Приклад без DI
Розглянемо приклад, де клас `Message` напряму залежить від класу `Logger`:

```csharp
class Logger
{
    public void Log(string message) => Console.WriteLine(message);
}

class Message
{
    Logger logger = new Logger();
    public string Text { get; set; } = "";
    public void Print() => logger.Log(Text);
}
```

**Проблеми**:
- Клас `Message` тісно пов’язаний із `Logger`, що ускладнює заміну логера (наприклад, на логування у файл).
- Зміна залежностей `Logger` вимагає модифікації коду `Message`.
- Тестування ускладнене через пряме створення об’єкта `Logger`.

### Використання DI
Впровадимо абстракцію через інтерфейс `ILogger`:

```csharp
interface ILogger
{
    void Log(string message);
}

class Logger : ILogger
{
    public void Log(string message) => Console.WriteLine(message);
}

class Message
{
    private readonly ILogger logger;
    public string Text { get; set; } = "";
    public Message(ILogger logger)
    {
        this.logger = logger;
    }
    public void Print() => logger.Log(Text);
}
```

**Переваги**:
- Клас `Message` залежить від абстракції `ILogger`, а не від конкретної реалізації.
- Залежність передається ззовні через конструктор, що полегшує заміну реалізації.
- Легше тестувати, оскільки можна передати мок-об’єкт `ILogger`.

### IoC-контейнер в ASP.NET Core
ASP.NET Core має вбудований IoC-контейнер, представлений інтерфейсом `IServiceProvider`. Цей контейнер керує залежностями (сервісами), створюючи їх та передаючи в потрібні компоненти. Залежності додаються до колекції `IServiceCollection`, доступної через властивість `Services` об’єкта `WebApplicationBuilder`.

## IServiceCollection та вбудовані сервіси

Колекція `IServiceCollection` містить усі сервіси, зареєстровані в програмі. Навіть без ручного додавання сервісів, ASP.NET Core автоматично реєструє ряд вбудованих сервісів, таких як `ILogger<T>`, `ILoggerFactory`, `IWebHostEnvironment` тощо.

### Перегляд зареєстрованих сервісів
Приклад виведення всіх сервісів у колекції `IServiceCollection`:

```csharp
using System.Text;

var builder = WebApplication.CreateBuilder(args);
var services = builder.Services;
var app = builder.Build();

app.Run(async context =>
{
    var sb = new StringBuilder();
    sb.Append("<h1>Усі сервіси</h1>");
    sb.Append("<table>");
    sb.Append("<tr><th>Тип</th><th>Життєвий цикл</th><th>Реалізація</th></tr>");
    foreach (var svc in services)
    {
        sb.Append("<tr>");
        sb.Append($"<td>{svc.ServiceType.FullName}</td>");
        sb.Append($"<td>{svc.Lifetime}</td>");
        sb.Append($"<td>{svc.ImplementationType?.FullName ?? "N/A"}</td>");
        sb.Append("</tr>");
    }
    sb.Append("</table>");
    context.Response.ContentType = "text/html;charset=utf-8";
    await context.Response.WriteAsync(sb.ToString());
});

app.Run();
```

**Результат виведення сервісів**:
*Зображення: Таблиця з переліком сервісів, їх типами, життєвим циклом та реалізаціями*

**Пояснення**:
- Кожен сервіс у колекції представлений об’єктом `ServiceDescriptor`, який містить:
  - `ServiceType`: Тип сервісу (наприклад, інтерфейс).
  - `ImplementationType`: Тип реалізації (конкретний клас).
  - `Lifetime`: Життєвий цикл (`Transient`, `Scoped`, `Singleton`).
- За замовчуванням ASP.NET Core додає десятки сервісів, які можна використовувати в програмі.

### Реєстрація вбудованих сервісів
ASP.NET Core надає методи розширення для додавання вбудованих сервісів, які починаються з `Add`. Наприклад, додавання сервісів MVC:

```csharp
var builder = WebApplication.CreateBuilder(args);
builder.Services.AddMvc();
var app = builder.Build();

app.Run(async context => await context.Response.WriteAsync("MVC Services Added"));
app.Run();
```

Метод `AddMvc` додає сервіси, необхідні для роботи MVC, такі як контролери, представлення тощо.

## Створення власних сервісів

ASP.NET Core дозволяє створювати власні сервіси, які можна реєструвати та використовувати в програмі.

### Приклад створення сервісу
Створимо інтерфейс `ITimeService` та два класи для отримання часу в різних форматах:

```csharp
interface ITimeService
{
    string GetTime();
}

class ShortTimeService : ITimeService
{
    public string GetTime() => DateTime.Now.ToShortTimeString();
}

class LongTimeService : ITimeService
{
    public string GetTime() => DateTime.Now.ToLongTimeString();
}
```

Використання сервісу в програмі:

```csharp
var builder = WebApplication.CreateBuilder(args);
builder.Services.AddTransient<ITimeService, ShortTimeService>();
var app = builder.Build();

app.Run(async context =>
{
    var timeService = app.Services.GetService<ITimeService>();
    await context.Response.WriteAsync($"Час: {timeService?.GetTime() ?? "Сервіс недоступний"}");
});

app.Run();
```

**Результат використання сервісу**:
*Зображення: Веб-браузер відображає поточний час у форматі hh:mm*

**Пояснення**:
- Метод `AddTransient<ITimeService, ShortTimeService>` реєструє `ShortTimeService` як реалізацію `ITimeService`.
- Сервіс отримується через `app.Services.GetService<ITimeService>()`.
- Зміна реалізації на `LongTimeService` змінить формат часу на hh:mm:ss.

### Сервіс як конкретний клас
Сервіс не обов’язково має бути інтерфейсом. Наприклад, використаємо клас `TimeService`:

```csharp
public class TimeService
{
    public string GetTime() => DateTime.Now.ToShortTimeString();
}

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddTransient<TimeService>();
var app = builder.Build();

app.Run(async context =>
{
    var timeService = app.Services.GetService<TimeService>();
    await context.Response.WriteAsync($"Час: {timeService?.GetTime() ?? "Сервіс недоступний"}");
});

app.Run();
```

### Метод розширення для сервісу
Для зручності реєстрації сервісів створюють методи розширення:

```csharp
public static class ServiceProviderExtensions
{
    public static void AddTimeService(this IServiceCollection services)
    {
        services.AddTransient<TimeService>();
    }
}

public class TimeService
{
    public string GetTime() => DateTime.Now.ToShortTimeString();
}

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddTimeService();
var app = builder.Build();

app.Run(async context =>
{
    var timeService = app.Services.GetService<TimeService>();
    context.Response.ContentType = "text/html;charset=utf-8";
    await context.Response.WriteAsync($"Час: {timeService?.GetTime() ?? "Сервіс недоступний"}");
});

app.Run();
```

**Результат методу розширення**:
*Зображення: Веб-браузер відображає поточний час у форматі hh:mm*

## Отримання залежностей

ASP.NET Core пропонує кілька способів отримання зареєстрованих сервісів.

### Через `app.Services` (Service Locator)
Використання властивості `Services` об’єкта `WebApplication`:

```csharp
interface ITimeService
{
    string GetTime();
}

class ShortTimeService : ITimeService
{
    public string GetTime() => DateTime.Now.ToShortTimeString();
}

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddTransient<ITimeService, ShortTimeService>();
var app = builder.Build();

app.Run(async context =>
{
    var timeService = app.Services.GetService<ITimeService>();
    await context.Response.WriteAsync($"Час: {timeService?.GetTime() ?? "Сервіс недоступний"}");
});

app.Run();
```

**Примітка**: Метод `GetService` повертає `null`, якщо сервіс не зареєстровано, тоді як `GetRequiredService` викликає виняток.

**Результат Service Locator**:
*Зображення: Веб-браузер відображає поточний час або повідомлення про відсутність сервісу*

### Через `HttpContext.RequestServices`
Отримання сервісів через `RequestServices` у `HttpContext`:

```csharp
interface ITimeService
{
    string GetTime();
}

class ShortTimeService : ITimeService
{
    public string GetTime() => DateTime.Now.ToShortTimeString();
}

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddTransient<ITimeService, ShortTimeService>();
var app = builder.Build();

app.Run(async context =>
{
    var timeService = context.RequestServices.GetService<ITimeService>();
    await context.Response.WriteAsync($"Час: {timeService?.GetTime() ?? "Сервіс недоступний"}");
});

app.Run();
```

### Через конструктор
Передача залежностей через конструктор є рекомендованим способом:

```csharp
interface ITimeService
{
    string GetTime();
}

class ShortTimeService : ITimeService
{
    public string GetTime() => DateTime.Now.ToShortTimeString();
}

class TimeMessage
{
    private readonly ITimeService timeService;
    public TimeMessage(ITimeService timeService)
    {
        this.timeService = timeService;
    }
    public string GetTime() => $"Час: {timeService.GetTime()}";
}

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddTransient<ITimeService, ShortTimeService>();
builder.Services.AddTransient<TimeMessage>();
var app = builder.Build();

app.Run(async context =>
{
    var timeMessage = context.RequestServices.GetService<TimeMessage>();
    context.Response.ContentType = "text/html;charset=utf-8";
    await context.Response.WriteAsync($"<h2>{timeMessage?.GetTime() ?? "Сервіс недоступний"}</h2>");
});

app.Run();
```

**Результат Constructor Injection**:
*Зображення: Веб-браузер відображає відформатоване повідомлення з часом*

### Через метод `Invoke/InvokeAsync` у Middleware
Залежності можна передавати в метод `InvokeAsync` компонента middleware:

```csharp
interface ITimeService
{
    string GetTime();
}

class ShortTimeService : ITimeService
{
    public string GetTime() => DateTime.Now.ToShortTimeString();
}

class TimeMessageMiddleware
{
    private readonly RequestDelegate next;

    public TimeMessageMiddleware(RequestDelegate next)
    {
        this.next = next;
    }

    public async Task InvokeAsync(HttpContext context, ITimeService timeService)
    {
        context.Response.ContentType = "text/html;charset=utf-8";
        await context.Response.WriteAsync($"<h1>Час: {timeService.GetTime()}</h1>");
    }
}

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddTransient<ITimeService, ShortTimeService>();
var app = builder.Build();

app.UseMiddleware<TimeMessageMiddleware>();
app.Run();
```

**Результат Middleware Injection**:
*Зображення: Веб-браузер відображає час у заголовку*

## Життєвий цикл залежностей

ASP.NET Core підтримує три типи життєвого циклу сервісів:
- **Transient**: Новий об’єкт створюється при кожному зверненні.
- **Scoped**: Один об’єкт на запит.
- **Singleton**: Один об’єкт для всіх запитів.

### Приклад життєвого циклу
Розглянемо приклад із сервісом `ICounter`, який генерує випадкове число:

```csharp
public interface ICounter
{
    int Value { get; }
}

public class RandomCounter : ICounter
{
    private static readonly Random rnd = new Random();
    private readonly int _value;
    public RandomCounter()
    {
        _value = rnd.Next(0, 1000000);
    }
    public int Value => _value;
}

public class CounterService
{
    public ICounter Counter { get; }
    public CounterService(ICounter counter)
    {
        Counter = counter;
    }
}

public class CounterMiddleware
{
    private readonly RequestDelegate next;
    private int i = 0;

    public CounterMiddleware(RequestDelegate next)
    {
        this.next = next;
    }

    public async Task InvokeAsync(HttpContext httpContext, ICounter counter, CounterService counterService)
    {
        i++;
        httpContext.Response.ContentType = "text/html;charset=utf-8";
        await httpContext.Response.WriteAsync($"Запит {i}; Counter: {counter.Value}; Service: {counterService.Counter.Value}");
    }
}
```

#### Transient
Реєстрація з `AddTransient`:

```csharp
var builder = WebApplication.CreateBuilder(args);
builder.Services.AddTransient<ICounter, RandomCounter>();
builder.Services.AddTransient<CounterService>();
var app = builder.Build();

app.UseMiddleware<CounterMiddleware>();
app.Run();
```

**Результат Transient**:
*Зображення: Різні значення для `Counter` і `CounterService.Counter` у кожному запиті*

**Пояснення**: Кожен виклик `ICounter` створює новий об’єкт `RandomCounter`, тому значення різні.

#### Scoped
Реєстрація з `AddScoped`:

```csharp
var builder = WebApplication.CreateBuilder(args);
builder.Services.AddScoped<ICounter, RandomCounter>();
builder.Services.AddScoped<CounterService>();
var app = builder.Build();

app.UseMiddleware<CounterMiddleware>();
app.Run();
```

**Результат Scoped**:
*Зображення: Однакове значення для `Counter` і `CounterService.Counter` у межах одного запиту, нове значення для нового запиту*

**Пояснення**: Один об’єкт `RandomCounter` використовується в межах запиту.

#### Singleton
Реєстрація з `AddSingleton`:

```csharp
var builder = WebApplication.CreateBuilder(args);
builder.Services.AddSingleton<ICounter, RandomCounter>();
builder.Services.AddSingleton<CounterService>();
var app = builder.Build();

app.UseMiddleware<CounterMiddleware>();
app.Run();
```

**Результат Singleton**:
*Зображення: Однакове значення для всіх запитів*

**Пояснення**: Один об’єкт `RandomCounter` створюється при першому зверненні та використовується для всіх запитів.

#### Ручне створення Singleton
Можна створити об’єкт вручну та зареєструвати його:

```csharp
var builder = WebApplication.CreateBuilder(args);
var rndCounter = new RandomCounter();
builder.Services.AddSingleton<ICounter>(rndCounter);
builder.Services.AddSingleton<CounterService>(new CounterService(rndCounter));
var app = builder.Build();

app.UseMiddleware<CounterMiddleware>();
app.Run();
```

## Сервіси в Middleware

Сервіси можна використовувати в middleware через конструктор, `InvokeAsync` або `HttpContext.RequestServices`.

### Через конструктор
Приклад middleware із сервісом у конструкторі:

```csharp
public class TimeService
{
    public TimeService()
    {
        Time = DateTime.Now.ToLongTimeString();
    }
    public string Time { get; }
}

public class TimerMiddleware
{
    private readonly RequestDelegate next;
    private readonly TimeService timeService;

    public TimerMiddleware(RequestDelegate next, TimeService timeService)
    {
        this.next = next;
        this.timeService = timeService;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        if (context.Request.Path == "/time")
        {
            context.Response.ContentType = "text/html; charset=utf-8";
            await context.Response.WriteAsync($"Час: {timeService.Time}");
        }
        else
        {
            await next.Invoke(context);
        }
    }
}

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddTransient<TimeService>();
var app = builder.Build();

app.UseMiddleware<TimerMiddleware>();
app.Run(async (context) => await context.Response.WriteAsync("Hello ITStep"));
app.Run();
```

**Результат Middleware Constructor**:
*Зображення: Відображення одного й того ж часу для всіх запитів до /time*

**Пояснення**: Оскільки `TimerMiddleware` є singleton, а `TimeService` — transient, об’єкт `TimeService` створюється один раз при запуску програми.

### Через `InvokeAsync`
Використання transient/scoped сервісів у `InvokeAsync`:

```csharp
public class TimeService
{
    public TimeService()
    {
        Time = DateTime.Now.ToLongTimeString();
    }
    public string Time { get; }
}

public class TimerMiddleware
{
    private readonly RequestDelegate next;

    public TimerMiddleware(RequestDelegate next)
    {
        this.next = next;
    }

    public async Task InvokeAsync(HttpContext context, TimeService timeService)
    {
        if (context.Request.Path == "/time")
        {
            context.Response.ContentType = "text/html; charset=utf-8";
            await context.Response.WriteAsync($"Час: {timeService.Time}");
        }
        else
        {
            await next.Invoke(context);
        }
    }
}

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddTransient<TimeService>();
var app = builder.Build();

app.UseMiddleware<TimerMiddleware>();
app.Run(async (context) => await context.Response.WriteAsync("Hello ITStep"));
app.Run();
```

**Результат Middleware Invoke**:
*Зображення: Відображення нового часу для кожного запиту до /time*

**Пояснення**: Transient-сервіс створюється заново для кожного запиту.

## Scoped-сервіси в Singleton-об’єктах

Scoped-сервіси не можна передавати в конструктор singleton-об’єктів, оскільки це викликає помилку.

### Приклад помилки
Спроба передати scoped-сервіс у singleton:

```csharp
public interface ITimer
{
    string Time { get; }
}

public class Timer : ITimer
{
    public Timer()
    {
        Time = DateTime.Now.ToLongTimeString();
    }
    public string Time { get; }
}

public class TimeService
{
    private readonly ITimer timer;
    public TimeService(ITimer timer)
    {
        this.timer = timer;
    }
    public string GetTime() => timer.Time;
}

public class TimerMiddleware
{
    private readonly TimeService timeService;
    public TimerMiddleware(RequestDelegate next, TimeService timeService)
    {
        this.timeService = timeService;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        await context.Response.WriteAsync($"Час: {timeService.GetTime()}");
    }
}

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddTransient<ITimer, Timer>();
builder.Services.AddScoped<TimeService>();
var app = builder.Build();

app.UseMiddleware<TimerMiddleware>();
app.Run();
```

**Результат помилки**:
*Зображення: Помилка "InvalidOperationException: Cannot resolve scoped service 'TimeService' from root provider"*

### Вирішення проблеми
Використовуйте `InvokeAsync` для scoped-сервісів:

```csharp
public interface ITimer
{
    string Time { get; }
}

public class Timer : ITimer
{
    public Timer()
    {
        Time = DateTime.Now.ToLongTimeString();
    }
    public string Time { get; }
}

public class TimeService
{
    private readonly ITimer timer;
    public TimeService(ITimer timer)
    {
        this.timer = timer;
    }
    public string GetTime() => timer.Time;
}

public class TimerMiddleware
{
    public TimerMiddleware(RequestDelegate next) { }

    public async Task InvokeAsync(HttpContext context, TimeService timeService)
    {
        await context.Response.WriteAsync($"Час: {timeService.GetTime()}");
    }
}

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddTransient<ITimer, Timer>();
builder.Services.AddScoped<TimeService>();
var app = builder.Build();

app.UseMiddleware<TimerMiddleware>();
app.Run();
```

**Результат вирішення**:
*Зображення: Відображення нового часу для кожного запиту*

## Множинна реєстрація сервісів

ASP.NET Core дозволяє реєструвати кілька реалізацій для одного сервісу або один об’єкт для кількох залежностей.

### Кілька реалізацій для одного сервісу
Приклад із двома реалізаціями `IHelloService`:

```csharp
interface IHelloService
{
    string Message { get; }
}

class RuHelloService : IHelloService
{
    public string Message => "Привіт, ITStep!";
}

class EnHelloService : IHelloService
{
    public string Message => "Hello, ITStep!";
}

class HelloMiddleware
{
    private readonly IEnumerable<IHelloService> helloServices;

    public HelloMiddleware(RequestDelegate _, IEnumerable<IHelloService> helloServices)
    {
        this.helloServices = helloServices;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        context.Response.ContentType = "text/html; charset=utf-8";
        string responseText = "";
        foreach (var service in helloServices)
        {
            responseText += $"<h3>{service.Message}</h3>";
        }
        await context.Response.WriteAsync(responseText);
    }
}

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddTransient<IHelloService, RuHelloService>();
builder.Services.AddTransient<IHelloService, EnHelloService>();
var app = builder.Build();

app.UseMiddleware<HelloMiddleware>();
app.Run();
```

**Результат множинної реєстрації**:
*Зображення: Відображення обох повідомлень: "Привіт, ITStep!" та "Hello, ITStep!"*

### Один об’єкт для кількох залежностей
Приклад із класом `ValueStorage`, який реалізує два інтерфейси:

```csharp
interface IGenerator
{
    int GenerateValue();
}

interface IReader
{
    int ReadValue();
}

class ValueStorage : IGenerator, IReader
{
    private int value;
    public int GenerateValue()
    {
        value = new Random().Next();
        return value;
    }
    public int ReadValue() => value;
}

class GeneratorMiddleware
{
    private readonly RequestDelegate next;
    private readonly IGenerator generator;

    public GeneratorMiddleware(RequestDelegate next, IGenerator generator)
    {
        this.next = next;
        this.generator = generator;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        if (context.Request.Path == "/generate")
            await context.Response.WriteAsync($"Нове значення: {generator.GenerateValue()}");
        else
            await next.Invoke(context);
    }
}

class ReaderMiddleware
{
    private readonly IReader reader;

    public ReaderMiddleware(RequestDelegate _, IReader reader)
    {
        this.reader = reader;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        await context.Response.WriteAsync($"Поточне значення: {reader.ReadValue()}");
    }
}

var builder = WebApplication.CreateBuilder(args);
var valueStorage = new ValueStorage();
builder.Services.AddSingleton<IGenerator>(valueStorage);
builder.Services.AddSingleton<IReader>(valueStorage);
var app = builder.Build();

app.UseMiddleware<GeneratorMiddleware>();
app.UseMiddleware<ReaderMiddleware>();
app.Run();
```

**Результат одного об’єкта**:
*Зображення: Синхронізовані значення для генерації та читання*

### Альтернативний підхід
Використання одного сервісу для кількох залежностей:

```csharp
interface IGenerator
{
    int GenerateValue();
}

interface IReader
{
    int ReadValue();
}

class ValueStorage : IGenerator, IReader
{
    private int value;
    public int GenerateValue()
    {
        value = new Random().Next();
        return value;
    }
    public int ReadValue() => value;
}

class GeneratorMiddleware
{
    private readonly RequestDelegate next;
    private readonly IGenerator generator;

    public GeneratorMiddleware(RequestDelegate next, IGenerator generator)
    {
        this.next = next;
        this.generator = generator;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        if (context.Request.Path == "/generate")
            await context.Response.WriteAsync($"Нове значення: {generator.GenerateValue()}");
        else
            await next.Invoke(context);
    }
}

class ReaderMiddleware
{
    private readonly IReader reader;

    public ReaderMiddleware(RequestDelegate _, IReader reader)
    {
        this.reader = reader;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        await context.Response.WriteAsync($"Поточне значення: {reader.ReadValue()}");
    }
}

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddSingleton<ValueStorage>();
builder.Services.AddSingleton<IGenerator>(serv => serv.GetRequiredService<ValueStorage>());
builder.Services.AddSingleton<IReader>(serv => serv.GetRequiredService<ValueStorage>());
var app = builder.Build();

app.UseMiddleware<GeneratorMiddleware>();
app.UseMiddleware<ReaderMiddleware>();
app.Run();
```

**Результат альтернативного підходу**:
*Зображення: Синхронізовані значення для генерації та читання*

## Висновок

Внедрення залежностей у ASP.NET Core забезпечує гнучкість і модульність програми. Використання `IServiceCollection` дозволяє реєструвати сервіси з різними життєвими циклами (`Transient`, `Scoped`, `Singleton`), а методи отримання залежностей (через конструктор, `InvokeAsync` або `RequestServices`) надають зручні способи їх використання. Множинна реєстрація сервісів дозволяє обробляти складні сценарії, такі як кілька реалізацій одного інтерфейсу або один об’єкт для кількох залежностей. У наступних темах можна розглянути інтеграцію DI з контролерами MVC або використання зовнішніх IoC-контейнерів.