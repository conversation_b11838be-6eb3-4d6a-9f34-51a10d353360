# Робота з Middleware та Середовищем в ASP.NET Core

У цьому матеріалі ми розглянемо методи створення та використання middleware в ASP.NET Core, включаючи методи `Use`, `<PERSON><PERSON><PERSON>`, `Map`, `<PERSON><PERSON><PERSON>`, а також роботу з класами middleware та управління середовищем через інтерфейс `IWebHostEnvironment`. Ми детально розберемо, як створювати конвеєр обробки запитів, розгалужувати його та налаштовувати поведінку залежно від середовища виконання.

## Метод Use

Метод `Use` є основним інструментом для додавання компонентів middleware до конвеєра обробки запитів у ASP.NET Core. Він дозволяє виконувати дії до та після передачі запиту наступному компоненту в конвеєрі.

### Огляд методу Use
Метод `Use` реалізований як метод розширення для типу `IApplicationBuilder` і має дві основні перегрузки:

1. `Use(this IApplicationBuilder app, Func<HttpContext, Func<Task>, Task> middleware)`
2. `Use(this IApplicationBuilder app, Func<HttpContext, RequestDelegate, Task> middleware)`

- **Перший параметр** (`HttpContext`): Надає доступ до контексту запиту, дозволяючи отримати дані запиту та керувати відповіддю.
- **Другий параметр** (`Func<Task>` або `RequestDelegate`): Представляє наступний компонент middleware у конвеєрі, якому передається обробка запиту.
- **Повертає**: Об’єкт `Task`, що забезпечує асинхронну обробку.

Типовий вигляд використання методу `Use`:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Use(async (context, next) =>
{
    // Дії перед передачею запиту
    await next.Invoke();
    // Дії після обробки наступним middleware
});

app.Run(async (context) => await context.Response.WriteAsync("Hello ITStep"));
app.Run();
```

**Як працює middleware**:
1. Виконується код до виклику `await next.Invoke()` (попередня обробка).
2. Запит передається наступному компоненту через `next.Invoke()`.
3. Після завершення обробки наступним компонентом виконується код після `await next.Invoke()` (постобробка).

### Приклад використання Use
Розглянемо приклад, де middleware записує поточну дату та передає запит далі:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

string date = "";

app.Use(async (context, next) =>
{
    date = DateTime.Now.ToShortDateString();
    await next.Invoke();
    Console.WriteLine($"Поточна дата: {date}");
});

app.Run(async (context) => await context.Response.WriteAsync($"Дата: {date}"));

app.Run();
```

**Результат виконання**:
![alt text](image-16.png)
*Зображення: Веб-браузер відображає текст "Дата: [поточна дата]", а консоль показує "Поточна дата: [поточна дата]"*

**Пояснення**:
- Middleware у `app.Use` встановлює змінну `date` і передає запит до `app.Run`.
- `app.Run` відправляє клієнту відповідь із датою.
- Після завершення `app.Run` middleware у `app.Use` виводить дату в консоль.

### Застереження
Не рекомендується викликати `next.Invoke()` після `Response.WriteAsync`, оскільки це може порушити протокол відповіді (наприклад, перевищення `Content-Length` або змішування форматів). Приклад неправильного використання:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Use(async (context, next) =>
{
    await context.Response.WriteAsync("<p>Hello world!</p>");
    await next.Invoke(); // Неправильно: виклик після WriteAsync
});

app.Run(async (context) => await context.Response.WriteAsync("<p>Good bye, World...</p>"));

app.Run();
```

**Результат неправильного використання**:
![alt text](image-17.png)
*Зображення: Можливе змішане відображення HTML у браузері або помилка протоколу*

### Використання RequestDelegate
Використання `RequestDelegate` замість `Func<Task>` потребує явної передачі `HttpContext` у `next.Invoke(context)`:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

string date = "";

app.Use(async (context, next) =>
{
    date = DateTime.Now.ToShortDateString();
    await next.Invoke(context);
    Console.WriteLine($"Поточна дата: {date}");
});

app.Run(async (context) => await context.Response.WriteAsync($"Дата: {date}"));

app.Run();
```

### Термінальний Middleware
Middleware у `Use` може завершувати обробку запиту, не передаючи його далі:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Use(async (context, next) =>
{
    string? path = context.Request.Path.Value?.ToLower();
    if (path == "/date")
    {
        await context.Response.WriteAsync($"Дата: {DateTime.Now.ToShortDateString()}");
    }
    else
    {
        await next.Invoke();
    }
});

app.Run(async (context) => await context.Response.WriteAsync("Hello ITStep"));

app.Run();
```

**Результат термінального middleware**:
![alt text](image-19.png)
![alt text](image-18.png)
*Зображення: Відображення дати для шляху /date або "Hello ITStep" для інших шляхів*

### Винесення Middleware у методи
Для покращення читабельності middleware можна винести в окремий метод:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Use(GetDate);
app.Run(async (context) => await context.Response.WriteAsync("Hello ITStep"));

app.Run();

async Task GetDate(HttpContext context, RequestDelegate next)
{
    string? path = context.Request.Path.Value?.ToLower();
    if (path == "/date")
    {
        await context.Response.WriteAsync($"Дата: {DateTime.Now.ToShortDateString()}");
    }
    else
    {
        await next.Invoke(context);
    }
}
```

## UseWhen та MapWhen

Методи `UseWhen` і `MapWhen` дозволяють створювати розгалуження конвеєра обробки запитів на основі умов.

### UseWhen
Метод `UseWhen` створює гілку конвеєра, якщо виконано певну умову:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.UseWhen(
    context => context.Request.Path == "/time",
    appBuilder =>
    {
        appBuilder.Use(async (context, next) =>
        {
            var time = DateTime.Now.ToShortTimeString();
            Console.WriteLine($"Час: {time}");
            await next();
        });

        appBuilder.Run(async context =>
        {
            var time = DateTime.Now.ToShortTimeString();
            await context.Response.WriteAsync($"Час: {time}");
        });
    });

app.Run(async (context) => await context.Response.WriteAsync("Hello ITStep"));

app.Run();
```

**Результат UseWhen**:
![alt text](image-20.png)
*Зображення: Відображення часу для шляху /time або "Hello ITStep" для інших шляхів*

**Пояснення**:
- Якщо шлях запиту `/time`, активується гілка конвеєра, яка логує час у консоль і відправляє його клієнту.
- Для інших шляхів виконується основний конвеєр.

**Важливо**: Гілка створюється один раз під час запуску програми, тому змінні, визначені поза middleware, не оновлюються при кожному запиті.

### MapWhen
Метод `MapWhen` схожий на `UseWhen`, але зазвичай використовується для створення гілок із термінальними компонентами:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapWhen(
    context => context.Request.Path == "/time",
    appBuilder => appBuilder.Run(async context =>
    {
        var time = DateTime.Now.ToShortTimeTime();
        await context.Response.WriteAsync($"Час: {time}");
    }));

app.Run(async (context) => await context.Response.WriteAsync("Hello ITStep"));

app.Run();
```

**Результат MapWhen**:
![alt text](image-21.png)
*Зображення: Відображення часу для шляху /time або "Hello ITStep" для інших шляхів*

## Метод Map

Метод `Map` створює гілку конвеєра для обробки запитів за певним шляхом.

### Простий приклад Map
Приклад створення гілки для шляху `/time`:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Map("/time", appBuilder =>
{
    var time = DateTime.Now.ToShortTimeString();
    appBuilder.Use(async (context, next) =>
    {
        Console.WriteLine($"Час: {time}");
        await next();
    });
    appBuilder.Run(async context => await context.Response.WriteAsync($"Час: {time}"));
});

app.Run(async (context) => await context.Response.WriteAsync("Hello ITStep"));

app.Run();
```

**Результат Map**:
![alt text](image-22.png)
*Зображення: Відображення часу для шляху /time або "Hello ITStep" для інших шляхів*

### Кілька гілок Map
Створення кількох гілок для різних шляхів:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Map("/index", appBuilder =>
{
    appBuilder.Run(async context => await context.Response.WriteAsync("Index Page"));
});
app.Map("/about", appBuilder =>
{
    appBuilder.Run(async context => await context.Response.WriteAsync("About Page"));
});

app.Run(async (context) => await context.Response.WriteAsync("Page Not Found"));

app.Run();
```

**Результат кількох гілок**:
![alt text](image-23.png)
![alt text](image-24.png)
*Зображення: Відображення "Index Page" для /index, "About Page" для /about або "Page Not Found"*

### Вкладені гілки Map
Гілки можуть бути вкладеними для обробки підшляхів:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Map("/home", appBuilder =>
{
    appBuilder.Map("/index", Index);
    appBuilder.Map("/about", About);
    appBuilder.Run(async (context) => await context.Response.WriteAsync("Home Page"));
});

app.Run(async (context) => await context.Response.WriteAsync("Page Not Found"));

app.Run();

void Index(IApplicationBuilder appBuilder)
{
    appBuilder.Run(async context => await context.Response.WriteAsync("Index Page"));
}

void About(IApplicationBuilder appBuilder)
{
    appBuilder.Run(async context => await context.Response.WriteAsync("About Page"));
}
```

**Результат вкладених гілок**:
![alt text](image-25.png)
*Зображення: Відображення "Index Page" для /home/<USER>"About Page" для /home/<USER>"Home Page" для /home*

## Класи Middleware

Middleware можна визначати як окремі класи для кращої модульності та повторного використання.

### Приклад класу Middleware
Створимо клас `TokenMiddleware` для перевірки токена:

```csharp
public class TokenMiddleware
{
    private readonly RequestDelegate next;

    public TokenMiddleware(RequestDelegate next)
    {
        this.next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var token = context.Request.Query["token"];
        if (token != "12345678")
        {
            context.Response.StatusCode = 403;
            await context.Response.WriteAsync("Невалідний токен");
        }
        else
        {
            await next.Invoke(context);
        }
    }
}
```

Використання в `Program.cs`:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.UseMiddleware<TokenMiddleware>();

app.Run(async (context) => await context.Response.WriteAsync("Hello ITStep"));

app.Run();
```

**Результат класу middleware**:
![alt text](image-26.png)
![alt text](image-27.png)
*Зображення: Помилка "Невалідний токен" при відсутності токена або відображення "Hello ITStep" при валідному токені*

### Метод розширення для Middleware
Для зручності вбудовування middleware створюють методи розширення:

```csharp
public static class TokenExtensions
{
    public static IApplicationBuilder UseToken(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<TokenMiddleware>();
    }
}
```

Використання:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.UseToken();

app.Run(async (context) => await context.Response.WriteAsync("Hello ITStep"));

app.Run();
```

### Передача параметрів у Middleware
Додамо параметр для порівняння токена:

```csharp
public class TokenMiddleware
{
    private readonly RequestDelegate next;
    private readonly string pattern;

    public TokenMiddleware(RequestDelegate next, string pattern)
    {
        this.next = next;
        this.pattern = pattern;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var token = context.Request.Query["token"];
        if (token != pattern)
        {
            context.Response.StatusCode = 403;
            await context.Response.WriteAsync("Невалідний токен");
        }
        else
        {
            await next.Invoke(context);
        }
    }
}
```

Оновлений метод розширення:

```csharp
public static class TokenExtensions
{
    public static IApplicationBuilder UseToken(this IApplicationBuilder builder, string pattern)
    {
        return builder.UseMiddleware<TokenMiddleware>(pattern);
    }
}
```

Використання з параметром:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.UseToken("555555");

app.Run(async (context) => await context.Response.WriteAsync("Hello ITStep"));

app.Run();
```

## Побудова конвеєра обробки запитів

Конвеєр обробки запитів складається з кількох middleware, порядок яких впливає на обробку.

### Приклад конвеєра
Створимо три класи middleware: `ErrorHandlingMiddleware`, `AuthenticationMiddleware`, `RoutingMiddleware`:

```csharp
public class ErrorHandlingMiddleware
{
    private readonly RequestDelegate next;

    public ErrorHandlingMiddleware(RequestDelegate next)
    {
        this.next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        await next.Invoke(context);
        if (context.Response.StatusCode == 403)
        {
            await context.Response.WriteAsync("Доступ заборонено");
        }
        else if (context.Response.StatusCode == 404)
        {
            await context.Response.WriteAsync("Не знайдено");
        }
    }
}
```

```csharp
public class AuthenticationMiddleware
{
    private readonly RequestDelegate next;

    public AuthenticationMiddleware(RequestDelegate next)
    {
        this.next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var token = context.Request.Query["token"];
        if (string.IsNullOrWhiteSpace(token))
        {
            context.Response.StatusCode = 403;
        }
        else
        {
            await next.Invoke(context);
        }
    }
}
```

```csharp
public class RoutingMiddleware
{
    public RoutingMiddleware(RequestDelegate _)
    {
    }

    public async Task InvokeAsync(HttpContext context)
    {
        string path = context.Request.Path;
        if (path == "/index")
        {
            await context.Response.WriteAsync("Home Page");
        }
        else if (path == "/about")
        {
            await context.Response.WriteAsync("About Page");
        }
        else
        {
            context.Response.StatusCode = 404;
        }
    }
}
```

Використання в `Program.cs`:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.UseMiddleware<ErrorHandlingMiddleware>();
app.UseMiddleware<AuthenticationMiddleware>();
app.UseMiddleware<RoutingMiddleware>();

app.Run();
```

**Результат конвеєра**:
*Зображення: Відображення "Home Page" або "About Page" при валідному токені, "Доступ заборонено" без токена або "Не знайдено" для інших шляхів*

**Схема конвеєра**:
*Зображення: Схематичне зображення конвеєра обробки запитів*

## IWebHostEnvironment

Інтерфейс `IWebHostEnvironment` дозволяє взаємодіяти із середовищем виконання програми.

### Властивості IWebHostEnvironment
- **ApplicationName**: Ім’я програми.
- **EnvironmentName**: Назва середовища (Development, Staging, Production).
- **ContentRootPath**: Шлях до кореневої папки програми.
- **WebRootPath**: Шлях до папки зі статичним контентом (зазвичай `wwwroot`).
- **ContentRootFileProvider**: Провайдер для читання файлів із `ContentRootPath`.
- **WebRootFileProvider**: Провайдер для читання файлів із `WebRootPath`.

### Використання EnvironmentName
Середовище визначається змінною `ASPNETCORE_ENVIRONMENT` у файлі `launchSettings.json`:

```json
{
  "iisSettings": {
    "windowsAuthentication": false,
    "anonymousAuthentication": true,
    "iisExpress": {
      "applicationUrl": "http://localhost:56234",
      "sslPort": 44384
    }
  },
  "profiles": {
    "HelloApp": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "applicationUrl": "https://localhost:7256;http://localhost:5256",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    "IIS Express": {
      "commandName": "IISExpress",
      "launchBrowser": true,
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  }
}
```

**Результат launchSettings.json**:
*Зображення: Відображення структури файлу launchSettings.json*

### Використання методів розширення
Методи розширення для перевірки середовища:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.Run(async (context) => await context.Response.WriteAsync("In Development Stage"));
}
else
{
    app.Run(async (context) => await context.Response.WriteAsync("In Production Stage"));
}

Console.WriteLine($"Середовище: {app.Environment.EnvironmentName}");

app.Run();
```

**Результат перевірки середовища**:
*Зображення: Відображення "In Development Stage" або "In Production Stage"*

### Зміна середовища програмно
Середовище можна змінити програмно:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Environment.EnvironmentName = "Test";

if (app.Environment.IsEnvironment("Test"))
{
    app.Run(async (context) => await context.Response.WriteAsync("In Test Stage"));
}
else
{
    app.Run(async (context) => await context.Response.WriteAsync("In Development or Production Stage"));
}

app.Run();
```

**Результат власного середовища**:
*Зображення: Відображення "In Test Stage" для середовища Test*

## Висновок

Методи `Use`, `UseWhen`, `Map`, `MapWhen` та класи middleware дозволяють гнучко налаштовувати конвеєр обробки запитів у ASP.NET Core. Інтерфейс `IWebHostEnvironment` забезпечує доступ до середовища виконання, дозволяючи адаптувати поведінку програми залежно від стадії розробки. У наступних темах можна розглянути використання вбудованих middleware для статичних файлів, автентифікації чи інтеграцію з базами даних.
