# Огляд push-повідомлень

## Що таке push-повідомлення?

Push-повідомлення дозволяють вам донести інформацію до уваги ваших користувачів, навіть коли вони не використовують ваш сайт. Вони називаються push-повідомленнями, тому що ви можете «проштовхувати» інформацію своїм користувачам, навіть коли вони не активні.

Порівняйте [технологію Push](https://en.wikipedia.org/wiki/Push_technology) з [технологією Pull](https://en.wikipedia.org/wiki/Pull_technology), щоб краще зрозуміти цю концепцію.

Повідомлення представляють користувачеві невеликі фрагменти інформації. Веб-сайти можуть використовувати повідомлення, щоб повідомляти користувачам про важливі, термінові події або дії, які необхідно вжити користувачеві.

Зовнішній вигляд і сприйняття повідомлень різняться на різних платформах:

![Приклади повідомлень на різних платформах](https://web.dev/static/articles/push-notifications-overview/image/examples-notifications-940d01bcab836_856.png)

### Різниця між Push та Notifications

Push-повідомлення та сповіщення — це дві окремі, але взаємодоповнюючі технології:

- **Push** — це технологія надсилання повідомлень з вашого сервера користувачам, навіть якщо вони не використовують ваш сайт
- **Сповіщення** — це технологія відображення push-інформації на пристрої користувача

Можливо використовувати повідомлення без push-повідомлень. Одного разу також може стати можливим використання push-повідомлень без повідомлень, що відображаються користувачем (тихий push), але в даний час браузери не дозволяють цього.

На практиці вони зазвичай використовуються разом. Нетехнічний користувач, ймовірно, не зрозуміє різницю між push-повідомленнями і повідомленнями.

**Термінологія:**
- Коли ми говоримо **push-повідомлення** — маємо на увазі комбінацію push-повідомлення з подальшим відображенням його у вигляді повідомлення
- Коли ми говоримо **push** — маємо на увазі push-технологію як таку
- Коли ми говоримо **повідомлення** — маємо на увазі технологію повідомлення як таку

## Навіщо використовувати push-повідомлення?

**Для користувачів:** push-повідомлення — це спосіб отримувати своєчасну, актуальну та точну інформацію.

**Для вас (власника сайту):** push-повідомлення — це спосіб підвищити залученість користувачів.

> ⚠️ **Попередження:** якщо ви спробуєте використовувати push-повідомлення для контенту, який ваші користувачі не вважають своєчасним, актуальним і точним, ви, швидше за все, розлютите своїх користувачів і знизите загальну залученість.

## Як працюють push-повідомлення?

На високому рівні ключовими кроками щодо впровадження push-повідомлень є:

1. **Додавання клієнтської логіки** для запиту у користувача дозволу на відправку push-повідомлень, а потім відправка інформації про ідентифікатор клієнта на ваш сервер для зберігання в базі даних
2. **Додавання серверної логіки** для відправки повідомлень на клієнтські пристрої
3. **Додавання клієнтської логіки** для отримання повідомлень, відправлених на пристрій, і відображення їх у вигляді повідомлень

Далі на цій сторінці ці кроки пояснюються більш детально.

### Крок 1: Отримайте дозвіл на відправлення push-повідомлень

По-перше, ваш веб-сайт повинен отримати дозвіл користувача на відправлення push-повідомлень.

**Важливо:** Це повинно бути викликано жестом користувача, наприклад, натисканням кнопки «Так» поруч із запитом "Do you want to receive push notifications?".

Після цього підтвердження викличте [`Notification.requestPermission()`](https://developer.mozilla.org/docs/Web/API/Notification/requestPermission).

Операційна система або браузер на пристрої користувача, ймовірно, представлять якийсь користувальницький інтерфейс для формального підтвердження того, що користувач хоче погодитися на push-повідомлення. Цей користувальницький інтерфейс відрізняється на різних платформах.

### Крок 2: Підпишіть клієнта на push-повідомлення

Після отримання дозволу ваш веб-сайт повинен ініціювати процес підписки користувача на push-повідомлення.

Це робиться через JavaScript з використанням [Push API](https://developer.mozilla.org/docs/Web/API/Push_API). Вам потрібно буде надати відкритий ключ аутентифікації під час процесу підписки, про який ви дізнаєтеся більше пізніше.

Після того, як ви запустите процес підписки, браузер робить мережевий запит до веб-служби, відомої як **push-служба (Push Service)**, про яку ви також дізнаєтеся більше пізніше.

Припускаючи, що підписка пройшла успішно, браузер повертає об'єкт [`PushSubscription`](https://developer.mozilla.org/docs/Web/API/PushSubscription). Вам потрібно буде зберігати ці дані довгостроково. Зазвичай це робиться шляхом відправки інформації на сервер, який ви контролюєте, а потім сервер зберігає її в базі даних.

![Схема отримання дозволу та підписки](https://web.dev/static/articles/push-notifications-overview/image/get-permission-send-push-bd9323129e7a5.svg)

### Крок 3: Надіслати push-повідомлення

Ваш сервер насправді не надсилає push-повідомлення безпосередньо клієнту. Це робить **push-сервіс**.

**Push-сервіс** — це веб-сервіс, контрольований постачальником браузера вашого користувача. Коли ви хочете надіслати push-повідомлення клієнту, вам необхідно зробити запит веб-сервісу до push-сервісу.

Запит веб-сервісу, який ви надсилаєте push-сервісу, називається **запитом протоколу web push**.

#### Що повинен включати запит протоколу web push:

- **Які дані** включити в повідомлення
- **Якому клієнту** надіслати повідомлення
- **Інструкції** про те, як push-сервіс повинен доставляти повідомлення (наприклад, ви можете вказати, що push-сервіс повинен припинити спроби надіслати повідомлення через 10 хвилин)

Зазвичай ви робите запит протоколу web push через сервер, який ви контролюєте. Звичайно, ваш сервер не повинен сам створювати сирий запит веб-сервісу. Існують бібліотеки, які можуть зробити це за вас, наприклад, [web-push-libs](https://github.com/web-push-libs/). Але базовий механізм — це запит веб-сервісу через HTTP.

![Схема надсилання push-повідомлення](https://web.dev/static/articles/push-notifications-overview/image/your-server-sends-web-pu-55e06fc9c0017.svg)

#### Як працює push-сервіс

Служба push отримує ваш запит, аутентифікує його і направляє push-повідомлення відповідному клієнту. Якщо браузер клієнта знаходиться в автономному режимі, служба push ставить push-повідомлення в чергу, поки браузер не з'явиться в мережі.

#### Стандартизація протоколу

Кожен браузер використовує ту службу push, яку хоче. Ви як розробник веб-сайту не можете це контролювати. Це не проблема, оскільки запит протоколу web push [стандартизований](https://tools.ietf.org/html/draft-ietf-webpush-protocol).

Іншими словами, вам не потрібно турбуватися про те, яку службу push використовує постачальник браузера. Вам просто потрібно переконатися, що ваш запит протоколу web push відповідає специфікації. Серед іншого, специфікація говорить, що запит повинен включати певні заголовки, а дані повинні бути відправлені у вигляді потоку байтів.

#### Визначення правильної push-служби

Однак вам необхідно переконатися, що ви відправляєте запит протоколу web push в правильну службу push. Дані `PushSubscription`, які браузер повернув вам під час процесу підписки, надають цю інформацію.

Об'єкт `PushSubscription` виглядає наступним чином:

```json
{
  "endpoint": "https://fcm.googleapis.com/fcm/send/c1KrmpTuRm…",
  "expirationTime": null,
  "keys": {
    "p256dh": "BGyyVt9FFV…",
    "auth": "R9sidzkcdf…"
  }
}
```

**Пояснення структури:**
- **`endpoint`** — домен endpoint по суті є push-сервісом. Шлях `endpoint` — це інформація про ідентифікатор клієнта, яка допомагає push-сервісу точно визначити, якому клієнту слід надіслати повідомлення
- **`keys`** — використовуються для шифрування, про що йдеться далі

## Безпека push-повідомлень

### Шифрування push-повідомлень

Дані, які ви відправляєте в push-сервіс, повинні бути **зашифровані**. Це не дозволяє push-сервісу переглядати дані, які ви відправляєте клієнту.

> 🔒 **Важливо:** Пам'ятайте, що постачальник браузера вирішує, який push-сервіс використовувати, і цей push-сервіс теоретично може бути небезпечним або незахищеним.

Ваш сервер повинен використовувати `keys`, надані в `PushSubscription`, для шифрування своїх запитів протоколу web push.

### Підпис запитів push-протоколу

Служба push-повідомлень дозволяє запобігти відправленню повідомлень вашим користувачам кимось іншим.

**Підтримка браузерами:**
- **Chrome:** найпростіша реалізація вимагає цього
- **Firefox:** необов'язково
- **Інші браузери:** можуть вимагати цього в майбутньому

#### Як працює процес аутентифікації

Цей робочий процес включає в себе **закритий ключ** і **відкритий ключ**, які є унікальними для вашого додатка.

**Процес аутентифікації працює так:**

1. **Генерація ключів:** Ви генеруєте закритий і відкритий ключ як одноразове завдання. Комбінація закритого і відкритого ключа відома як **ключі сервера додатків** або **ключі VAPID**. [VAPID](https://tools.ietf.org/html/draft-thomson-webpush-vapid-02) — це специфікація, яка визначає цей процес аутентифікації

2. **Підписка клієнта:** Коли ви підписуєте клієнта на push-повідомлення з вашого коду JavaScript, ви надаєте свій відкритий ключ. Коли push-сервіс генерує endpoint для пристрою, він пов'язує наданий відкритий ключ з endpoint

3. **Надсилання повідомлення:** Коли ви надсилаєте запит за протоколом Web Push, ви підписуєте певну інформацію JSON своїм закритим ключем

4. **Перевірка на сервері:** Коли служба push отримує ваш запит протоколу web push, вона використовує збережений відкритий ключ для автентифікації підписаної інформації. Якщо підпис дійсний, служба push знає, що запит надійшов із сервера з відповідним закритим ключем

#### Детальний розбір процесу підписки

---

У цьому процесі використовуються два ключі — **закритий** (private) і **відкритий** (public). Вони унікальні для вашого застосунку.

Як це працює:

1. **Генерація ключів**
   Один раз створюються пара ключів: закритий і відкритий. Разом вони називаються **ключами сервера додатка** або **VAPID-ключами** (згідно зі стандартом [VAPID](https://tools.ietf.org/html/draft-thomson-webpush-vapid-02)).

2. **Підписка користувача**
   Коли користувач підписується на push-повідомлення через ваш JavaScript-код, ви передаєте йому **відкритий ключ**.
   Push-сервіс, створюючи endpoint для цього пристрою, "прив’язує" його до переданого відкритого ключа.

3. **Надсилання повідомлення**
   Коли ви надсилаєте push-повідомлення через Web Push API, ви підписуєте певні дані JSON своїм **закритим ключем**.

4. **Перевірка на боці push-сервісу**
   Push-сервіс отримує ваш запит, бере збережений **відкритий ключ** і перевіряє підпис.
   Якщо все збігається, сервіс розуміє, що повідомлення дійсно від вашого сервера (того, у кого є відповідний закритий ключ).

**Підписка користувача (детально)**

1. **Генерація ключів**
   Ви (розробник) один раз створюєте пару ключів — відкритий і закритий. Це робиться на сервері за допомогою бібліотек або інструментів, що підтримують VAPID.

2. **Передача відкритого ключа браузеру**
   Коли користувач заходить на ваш сайт і ваш JavaScript-код намагається підписати його на push-повідомлення, ви в коді викликаєте метод

   ```javascript
   registration.pushManager.subscribe({ applicationServerKey: ваш_відкритий_ключ })
   ```

   Тут `applicationServerKey` — це саме ваш **відкритий ключ**, який ви передаєте у виклик методу.

3. **Що робить браузер**
   Браузер бере цей відкритий ключ, звертається до свого push-сервісу (наприклад, Google Push Service або Mozilla Push Service), і той створює унікальний **endpoint** (адресу для доставки повідомлень на цей пристрій).

4. **Прив’язка ключа до endpoint**
   Push-сервіс "прив’язує" ваш відкритий ключ до цього endpoint, щоб у майбутньому перевіряти, що повідомлення приходять саме від вашого сервера.

---

Тобто, **відкриті ключі видаєте ви самі**, а **"передача браузеру"** відбувається через виклик JavaScript-методу `subscribe()`, де ви вставляєте свій відкритий ключ у параметри.


## Налаштування доставки push-повідомлень

Специфікація запиту протоколу web push також визначає параметри, які дозволяють вам налаштувати, як служба push намагається відправити push-повідомлення клієнту.

**Параметри налаштування:**

- **Час життя (TTL) повідомлення** — визначає, як довго служба push-повідомлень повинна намагатися доставити повідомлення
- **Терміновість повідомлення** — корисна в разі, якщо служба push-повідомлень економить заряд батареї клієнта, доставляючи тільки повідомлення з високим пріоритетом
- **Тема повідомлення** — замінює всі очікувані повідомлення тієї ж теми останнім повідомленням

## Отримання і відображення push-повідомлень

Після того як ви відправили запит по протоколу web push в push-службу, push-служба зберігає ваш запит в черзі до тих пір, поки не відбудеться одне з наступних подій:

1. **Клієнт підключається до мережі**, і push-сервіс доставляє push-повідомлення
2. **Термін дії повідомлення закінчився**

### Обробка повідомлень на клієнті

Коли клієнтський браузер отримує push-повідомлення, він:

1. **Розшифровує** дані push-повідомлення
2. **Відправляє push-подію** вашому [service worker](https://web.dev/articles/service-workers-cache-storage?hl=ru#service_workers)

**Service worker** — це, по суті, код JavaScript, який може працювати у фоновому режимі, навіть якщо ваш веб-сайт не відкритий або браузер закритий.

В обробнику push-подій вашого service worker ви викликаєте `ServiceWorkerRegistration.showNotification()`, щоб відобразити інформацію у вигляді повідомлення.

![Схема доставки повідомлення на пристрій](https://web.dev/static/articles/push-notifications-overview/image/message-arrives-device-a6799c98fd39b.svg)

# 🌐 Протокол Web Push

Ми бачили, як можна використовувати бібліотеку для запуску push-повідомлень, але **що саме роблять ці бібліотеки?**

## 🔍 Що відбувається під капотом?

Вони відправляють **мережеві запити**, гарантуючи, що такі запити мають **правильний формат**. Специфікацією, що визначає цей мережевий запит, є **Web Push Protocol**.

![Web Push Protocol Diagram](https://web.dev/static/articles/push-notifications-web-push-protocol/image/diagram-sending-push-me-f952f3399947e.svg)

*Схема відправки push-повідомлення з вашого сервера на Push Service*

## 📋 Що розглянемо в цьому розділі:

У цьому розділі описується:

1. **🔐 Як сервер може ідентифікувати себе** за допомогою Application Server Keys
2. **📦 Як відправляються зашифровані дані** та пов'язані з ними метадані
3. **🔧 Технічні деталі** протоколу

> ⚠️ **Примітка:** Це складна сторона Web Push, і я не експерт у шифруванні, але давайте розглянемо кожну частину, оскільки **корисно знати, що ці бібліотеки роблять під капотом**.

## 🔑 Application Server Keys

Коли ми підписуємо користувача, ми передаємо **`applicationServerKey`**. Цей ключ передається в Push Service і використовується для перевірки того, що **додаток, який підписався на користувача**, також є **додатком, який запускає push-повідомлення**.

### 🔐 Процес автентифікації

Коли ми запускаємо push-повідомлення, ми відправляємо набір **заголовків**, які дозволяють Push Service **автентифікувати додаток**. (Це визначено специфікацією **VAPID**.)

### 🔄 Що насправді відбувається?

Ось кроки, зроблені для **автентифікації Application Server**:

1. **📝 Підписання:** Application Server підписує деяку **інформацію JSON** своїм **приватним ключем**
2. **📤 Відправка:** Ця підписана інформація відправляється в Push Service у вигляді **заголовка в POST запиті**
3. **🔍 Перевірка:** Push Service використовує збережений **публічний ключ**, отриманий від `pushManager.subscribe()` для перевірки того, що отримана інформація підписана **приватним ключем**, що відноситься до публічного ключа
4. **✅ Доставка:** Якщо підписана інформація дійсна, Push Service **надсилає користувачеві push-повідомлення**

> 💡 **Пам'ятайте:** Публічний ключ — це ключ **`applicationServerKey`**, що передається у виклик підписки.

### 📊 Діаграма процесу

Приклад такого потоку інформації наведено нижче. (Зверніть увагу на легенду внизу ліворуч, що позначає публічний і приватний ключі.)

**«Підписана інформація»**, додана в заголовок запиту, являє собою **JSON Web Token**.

## 🎫 JSON Web Token (JWT)

**JSON Web Token** (або скорочено **JWT**) — це спосіб відправлення повідомлення третій стороні, що дозволяє **одержувачу перевірити, хто його відправив**.

### 🔍 Як працює перевірка JWT?

Коли третя сторона отримує повідомлення:

1. **🔑 Отримує публічний ключ** відправника
2. **✅ Використовує його для перевірки підпису** JWT
3. **🎯 Якщо підпис дійсний** → JWT повинен бути підписаний відповідним приватним ключем
4. **✅ Результат:** Повідомлення від очікуваного відправника

### 📚 Бібліотеки для JWT

На [https://jwt.io/](https://jwt.io/) є **безліч бібліотек**, які можуть виконати підписання за вас, і я б **рекомендував вам робити це** там, де ви можете.

> 📖 **Для повноти:** Давайте подивимося, як вручну створити підписаний JWT.

## 🔧 Web Push і підписані JWT

Підписаний **JWT** — це просто **рядок**, хоча його можна розглядати як **три рядки, з'єднані крапками**.

![JWT Structure](https://web.dev/static/articles/push-notifications-web-push-protocol/image/a-illustration-the-strin-21d10f4865585.svg)

### 📋 Структура JWT

**Перший і другий рядки** (інформація JWT і дані JWT) являють собою **фрагменти JSON, закодовані в base64**, що означає, що вони **доступні для публічного читання**.

### 1️⃣ JWT Header (Перший рядок)

**Перший рядок** — це **інформація про сам JWT**, що вказує, який **алгоритм використовувався** для створення підпису.

**Інформація JWT для Web Push** повинна містити наступну інформацію:

```json
{
  "typ": "JWT",
  "alg": "ES256"
}
```

### 2️⃣ JWT Payload (Другий рядок)

**Другий рядок** — це **дані JWT**. Він містить інформацію про:

- **👤 Відправника JWT**
- **🎯 Для кого він призначений**
- **⏰ Як довго він дійсний**

**Для Web Push** дані матимуть такий формат:

```json
{
  "aud": "https://some-push-service.org",
  "exp": "1469618703",
  "sub": "mailto:<EMAIL>"
}
```

### 📊 Пояснення полів JWT

#### 🎯 `aud` (Audience)
**Значення `aud`** — це **«аудиторія»**, тобто для кого призначений JWT. Для Web Push аудиторією є **Push Service**, тому ми встановлюємо її як **origin Push Service**.

#### ⏰ `exp` (Expiration)
**Значення `exp`** — це **закінчення терміну дії JWT**, що не дозволяє зловмисникам **повторно використовувати JWT**, якщо вони його перехоплять.

**Вимоги:**
- Термін дії являє собою **timestamp в секундах**
- **Не повинен перевищувати 24 години**

**У C# термін дії встановлюється за допомогою:**

```csharp
// 12 годин від поточного часу
var expiration = DateTimeOffset.UtcNow.AddHours(12).ToUnixTimeSeconds();
```

> 💡 **Чому 12 годин, а не 24?** Щоб уникнути проблем з **різницею в часі** між Application Server і Push Service.

#### 📧 `sub` (Subject)
**Значення `sub`** повинно бути або:
- **🌐 URL-адресою** (наприклад, `https://example.com`)
- **📧 Адресою електронної пошти** `mailto:` (наприклад, `mailto:<EMAIL>`)

**Навіщо це потрібно?** Якщо Push Service необхідно зв'язатися з відправником, вона може знайти **контактну інформацію в JWT**.

### 🔐 Кодування JWT

Як і **інформація JWT**, **дані JWT** кодуються як **безпечний для URL-адреси рядок base64**.

### 3️⃣ JWT Signature (Третій рядок)

**Третій рядок, підпис**, є результатом:

1. **🔗 Об'єднання** перших двох рядків (інформації JWT і даних JWT) за допомогою **крапки**
2. **📝 Підписання** цього «непідписаного токена»

### 🔒 Процес підпису

Процес підпису вимагає **шифрування «непідписаного токена»** за допомогою **ES256**.

**ES256** — це скорочення від **«ECDSA з використанням кривої P-256 і алгоритму хешування SHA-256»**.

### 💻 Реалізація в C#

Замість JavaScript Web Crypto API, в C# ми можемо використовувати бібліотеку для JWT:

```csharp
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.IdentityModel.Tokens;
using System.Security.Cryptography;

public class VapidJwtGenerator
{
    public static string GenerateVapidJwt(string audience, string subject, ECDsa privateKey)
    {
        // Створюємо JWT header
        var header = new JwtHeader(new SigningCredentials(
            new ECDsaSecurityKey(privateKey),
            SecurityAlgorithms.EcdsaSha256));

        // Створюємо JWT payload
        var payload = new JwtPayload(
            issuer: null,
            audience: audience,
            claims: new[]
            {
                new Claim("sub", subject)
            },
            notBefore: DateTime.UtcNow,
            expires: DateTime.UtcNow.AddHours(12)); // 12 годин

        // Створюємо та підписуємо JWT
        var jwt = new JwtSecurityToken(header, payload);
        var tokenHandler = new JwtSecurityTokenHandler();

        return tokenHandler.WriteToken(jwt);
    }
}

// Приклад використання
public class VapidExample
{
    public static string CreateVapidJwt()
    {
        // Завантажуємо приватний ключ (з конфігурації)
        var privateKeyBytes = Convert.FromBase64String("your-private-key-here");
        using var ecdsa = ECDsa.Create();
        ecdsa.ImportPkcs8PrivateKey(privateKeyBytes, out _);

        // Генеруємо JWT
        var jwt = VapidJwtGenerator.GenerateVapidJwt(
            audience: "https://fcm.googleapis.com", // Push Service URL
            subject: "mailto:<EMAIL>",
            privateKey: ecdsa);

        Console.WriteLine($"VAPID JWT: {jwt}");
        return jwt;
    }
}
```

> 💡 **Примітка:** Бібліотека `Lib.Net.Http.WebPush` автоматично генерує та підписує JWT токени, тому вам **не потрібно робити це вручну**.

### 🔍 Перевірка JWT

**Push Service** може перевірити JWT, використовуючи **публічний ключ Application Server**, щоб:

1. **🔓 Розшифрувати підпис**
2. **✅ Переконатися**, що розшифрований рядок збігається з **«непідписаним токеном»** (тобто першими двома рядками в JWT)

### 📤 HTTP заголовки

#### 🔐 Authorization Header

**Підписаний JWT** (тобто всі три рядки, з'єднані крапками) надсилається до **Web Push Service** у вигляді заголовка **`Authorization`** з доданим **`WebPush`**:

```http
Authorization: WebPush [JWT Info].[JWT Data].[Signature]
```

#### 🔑 Crypto-Key Header

**Web Push Protocol** також стверджує, що **публічний ключ Application Server** повинен бути відправлений в заголовку **`Crypto-Key`** у вигляді **URL-безпечного рядка в кодуванні Base64** з доданим до нього **`p256ecdsa=`**:

```http
Crypto-Key: p256ecdsa=[URL Safe Base64 Public Application Server Key]
```

## 🔒 Шифрування Payload

Далі давайте подивимося, як ми можемо **відправити payload** за допомогою push-повідомлення, щоб, коли наш веб-додаток отримає push-повідомлення, він міг **отримати доступ до отриманих даних**.

### ❓ Чому потрібне шифрування?

**Загальне питання**, яке виникає у будь-кого, хто використовував інші Push Services, полягає в тому, **чому payload Web Push має бути зашифроване?** У нативних додатках push-повідомлення можуть надсилати дані у вигляді **звичайного тексту**.

### 🌐 Переваги Web Push

**Привабливість Web Push** полягає в тому, що, оскільки всі Push Services використовують **один і той же API** (Web Push Protocol), розробникам **не потрібно турбуватися** про те, хто є Push Service.

**Ми можемо:**
- ✅ Зробити запит у потрібному форматі
- ✅ Чекати відправлення push-повідомлення

### 🛡️ Безпека через шифрування

**Зворотним боком** цього є те, що розробники можуть відправляти повідомлення в **ненадійну Push Service**.

**Рішення:** Зашифрувавши payload:
- **🔒 Push Service не може прочитати** відправлені дані
- **🔓 Тільки браузер може розшифрувати** інформацію
- **🛡️ Це захищає дані користувача**

> 📚 **Специфікація:** Шифрування payload визначено в [**Message Encryption Specification**](https://tools.ietf.org/html/draft-ietf-webpush-encryption).

### 🔧 Криптографічні методи

Перш ніж ми розглянемо конкретні кроки щодо **шифрування payload push-повідомлень**, нам слід розглянути деякі **методи**, які будуть використовуватися в процесі шифрування.

> 🙏 **Подяка:** Величезна подяка **Matt Scales** за його чудову статтю про push-шифрування.

## 🔐 ECDH і HKDF

І **ECDH**, і **HKDF** використовуються протягом усього процесу шифрування і пропонують переваги для **шифрування інформації**.

### 🔄 ECDH: Elliptic Curve Diffie-Hellman

**ECDH** — це **обмін ключами Діффі-Хеллмана на основі еліптичної кривої**.

#### 👥 Приклад з Алісою та Бобом

Уявіть, що у вас є дві людини, **Аліса і Боб**, які хочуть поділитися інформацією:

1. **🔑 Генерація ключів:** І Аліса, і Боб мають свої власні **публічні та приватні ключі**
2. **🤝 Обмін:** Аліса і Боб **діляться своїми публічними ключами** один з одним
3. **🔒 Створення секрету:**
   - Аліса використовує **свій приватний ключ + публічний ключ Боба** → створює секретне значення **«X»**
   - Боб використовує **свій приватний ключ + публічний ключ Аліси** → створює те ж саме значення **«X»**
4. **✅ Результат:** **«X»** стає **загальним секретом**, і Алісі та Бобу потрібно було тільки поділитися **публічними ключами**

### 💻 Реалізація в C#

З точки зору коду, більшість мов/платформ постачаються з **бібліотеками**, що спрощують створення цих ключів.

**У C# ми зробимо наступне:**

```csharp
using System.Security.Cryptography;

// Створюємо ECDH ключі
using var ecdh = ECDiffieHellman.Create(ECCurve.NamedCurves.nistP256);

// Генеруємо ключі
var publicKey = ecdh.PublicKey.ExportSubjectPublicKeyInfo();
var privateKey = ecdh.ExportPkcs8PrivateKey();

Console.WriteLine($"Public Key: {Convert.ToBase64String(publicKey)}");
Console.WriteLine($"Private Key: {Convert.ToBase64String(privateKey)}");
```

### 🔑 HKDF: HMAC-based Key Derivation Function

**У Вікіпедії** є короткий опис **HKDF**:

> **HKDF** — це функція отримання ключів на основі HMAC, яка **перетворює будь-який слабкий ключовий матеріал** у **криптографічно стійкий ключовий матеріал**. Його можна використовувати, наприклад, для перетворення загальних секретів, якими обмінювалися Діффі-Хеллман, в ключовий матеріал, придатний для використання в **шифруванні**, **перевірці цілісності** або **автентифікації**.

#### 🛡️ Що робить HKDF?

По суті, **HKDF** буде приймати **вхідні дані**, які не є особливо безпечними, і **робити їх більш безпечними**.

#### 📋 Вимоги специфікації

**Специфікація**, що визначає це шифрування, вимагає:
- **🔐 Використання SHA-256** в якості алгоритму хешування
- **📏 Результуючі ключі** для HKDF при Web Push **не повинні бути довше 256 біт (32 байти)**

#### 💻 Реалізація в C#

**У C# це можна реалізувати так:**

```csharp
using System.Security.Cryptography;

public static class HkdfHelper
{
    // Спрощена HKDF реалізація для Web Push
    public static byte[] Hkdf(byte[] salt, byte[] ikm, byte[] info, int length)
    {
        // Extract phase
        using var extractHmac = new HMACSHA256(salt);
        var prk = extractHmac.ComputeHash(ikm);

        // Expand phase
        using var expandHmac = new HMACSHA256(prk);
        expandHmac.TransformBlock(info, 0, info.Length, null, 0);

        // Додаємо один байт зі значенням 0x01
        var oneByte = new byte[] { 0x01 };
        expandHmac.TransformFinalBlock(oneByte, 0, 1);

        var result = expandHmac.Hash;

        // Повертаємо тільки потрібну довжину
        var output = new byte[length];
        Array.Copy(result, output, Math.Min(length, result.Length));

        return output;
    }
}
```

## 📥 Входи для шифрування

Коли ми хочемо відправити користувачеві push-повідомлення з **payload**, нам потрібні **три входи**:

1. **📦 Саме payload** (дані для відправки)
2. **🔐 Секрет `auth`** з `PushSubscription`
3. **🔑 Ключ `p256dh`** з `PushSubscription`

### 🔍 Нагадування про ключі

Ми бачили значення **`auth`** і **`p256dh`**, отримані з `PushSubscription`, але для швидкого нагадування: для підписки нам будуть потрібні ці значення:

**У C# з бібліотекою `Lib.Net.Http.WebPush`:**

```csharp
// Отримуємо ключі з PushSubscription
var authKey = pushSubscription.GetKey(PushEncryptionKeyName.Auth);
var p256dhKey = pushSubscription.GetKey(PushEncryptionKeyName.P256DH);
```

### 🔐 Пояснення ключів

#### 🤫 Auth Key (Секрет)
**Значення `auth`** слід розглядати як **секрет** і **не передавати за межі** вашого додатка.

#### 🔓 P256DH Key (Публічний ключ)
**Ключ `p256dh`** є **публічним ключем**, його іноді називають **публічним ключем клієнта**. Тут ми будемо називати `p256dh` **публічним ключем підписки**.

**Як це працює:**
- **🌐 Публічний ключ підписки** генерується **браузером**
- **🔒 Браузер зберігає приватний ключ** в секреті
- **🔓 Використовує його для розшифрування** payload

### 📊 Результат шифрування

**Ці три значення:** `auth`, `p256dh` і `payload` необхідні в якості **вхідних даних**, а **результатом процесу шифрування** будуть:

1. **🔒 Зашифровані payload**
2. **🧂 Допоміжне значення (salt)**
3. **🔑 Публічний ключ**, який використовується тільки для шифрування даних

---

# Як працює push

Перш ніж перейти до API, давайте розглянемо push **з високого рівня, від початку до кінця**. Потім, коли ми пізніше розглянемо окремі теми або API, ви зрозумієте, **як і чому це важливо**.

## 🎯 Три ключові кроки до реалізації push-повідомлень:

1. **🖥️ Клієнтська логіка** — додавання логіки на стороні клієнта для підписки користувача на відправку повідомлень (тобто JavaScript і користувальницький інтерфейс у вашому веб-додатку, який реєструє користувача для відправки повідомлень)

2. **🚀 Серверний API** — виклик API з вашої серверної частини/додатку, який запускає push-повідомлення на пристрій користувача

3. **⚙️ Service Worker** — файл JavaScript service worker-а, який отримає «подію push», коли push-повідомлення надійде на пристрій. Саме в цьому JavaScript ви зможете відображати повідомлення

Давайте розглянемо, що передбачає кожен з цих кроків, **більш детально**.

## Крок 1: Клієнтська частина

Першим кроком є **«підписка» користувача** на розсилку повідомлень.

### 📋 Що потрібно для підписки користувача:

**Дві основні речі:**

1. **🔐 Отримання дозволу** від користувача на відправку йому push-повідомлень
2. **📱 Отримання `PushSubscription`** з браузера

### 🆔 Що таке PushSubscription?

`PushSubscription` містить **всю інформацію, необхідну** для відправки push-повідомлення цьому користувачеві.

> 💡 **Аналогія:** Ви можете думати про це як про **ідентифікатор пристрою** цього користувача.

### 🛠️ Технічна реалізація

Все це робиться на **JavaScript** за допомогою **[Push API](https://developer.mozilla.org/docs/Web/API/Push_API)**.

> ⚠️ **Важливо:** Перед підпискою користувача вам необхідно згенерувати набір **«ключів сервера додатків»**, про які ми поговоримо пізніше.

### 🔑 Ключі сервера додатків (VAPID)

**Ключі сервера додатків**, також відомі як **ключі VAPID**, є **унікальними для вашого сервера**.

**Їх призначення:**
- 🔍 Дозволяють службі push-повідомлень **дізнатися, який сервер додатків** підписався на користувача
- 🛡️ **Гарантувати**, що саме той самий сервер ініціює відправку push-повідомлень цьому користувачеві

### 💾 Збереження підписки

Після того як ви підписалися на користувача і отримали `PushSubscription`:

1. **📤 Відправте дані** `PushSubscription` на свій бекенд/сервер
2. **🗄️ Збережіть цю підписку** в базі даних
3. **🚀 Використовуйте її** для відправки push-повідомлення цьому користувачеві

![](https://web.dev/static/articles/push-notifications-how-push-works/image/make-sure-send-pushsubs-1102393ad9967.svg?hl=ru)


## Крок 2: Надішліть push-повідомлення

Якщо ви хочете надіслати push-повідомлення своїм користувачам, вам необхідно виконати **API-виклик служби push**.

### 📋 Що включає API-виклик:

- **📊 Які дані** надсилати
- **👤 Кому надсилати** повідомлення
- **⚙️ Будь-які критерії** того, як надсилати повідомлення

> 🖥️ **Примітка:** Зазвичай цей API-виклик виконується **з вашого сервера**.

### ❓ Ключові питання:

- **🤔 Хто і що таке push-сервіс?**
- **🔧 Як виглядає API?** Це JSON, XML чи щось інше?
- **⚡ Що може API?**

### 🏢 Хто і що таке push-сервіс?

**Push-сервіс** — це служба, яка:

1. **📨 Отримує мережевий запит**
2. **✅ Перевіряє його**
3. **🚀 Доставляє push-повідомлення** відповідному браузеру

> 📱 **Офлайн режим:** Якщо браузер знаходиться в автономному режимі, повідомлення **поміщається в чергу** до тих пір, поки браузер не підключиться до мережі.

### 🌐 Універсальність API

**Важливі факти:**

- 🔄 Кожен браузер може використовувати **будь-яку службу push-повідомлень**, яку хоче
- 🚫 Розробники **не можуть це контролювати**
- ✅ Це **не проблема**, оскільки кожна служба push-повідомлень очікує **один і той же виклик API**

> 💡 **Висновок:** Вам не потрібно турбуватися про те, хто є службою push. Вам просто потрібно переконатися, що ваш **виклик API дійсний**.

### 🔗 Як отримати URL push-сервісу?

Щоб отримати відповідну **URL-адресу для запуску push-повідомлення** (тобто URL-адресу служби push-повідомлень), вам просто потрібно подивитися значення **`endpoint`** в **`PushSubscription`**.

Нижче наведено приклад значень, які ви отримаєте від `PushSubscription` :

```json
{
  "endpoint": "https://random-push-service.com/some-kind-of-unique-id-1234/v2/",
  "keys": {
    "p256dh": "BNcRdreALRFXTkOOUHK1EtK2wtaz5Ry4YfYCA_0QTpQtUbVlUls0VJXg7A8u-Ts1XbjhazAkj7I99e8QcYP7DkM=",
    "auth": "tBHItJI5svbpez7KI4CCXg=="
  }
}
```
**Кінцева точка (endpoint)** в цьому випадку — `https://random-push-service.com/some-kind-of-unique-id-1234/v2/`.

Служба push-повідомлень буде називатися **«random-push-service.com»**, і кожна кінцева точка є **унікальною для користувача** і позначається **«some-kind-of-unique-id-1234»**.

> 📌 **Важливо:** Коли ви почнете працювати з push, ви помітите цю закономірність.

**Ключі** в підписці будуть розглянуті пізніше.

### Як виглядає API?

Я згадував, що кожен веб-сервіс очікує одного і того ж виклику API. Цей API — **[протокол Web Push](https://tools.ietf.org/html/draft-ietf-webpush-protocol)**. Це стандарт **IETF**, який визначає, як ви виконуєте виклик API до служби push-повідомлень.

**Вимоги до API виклику:**
- Повинні бути встановлені **певні заголовки**
- Дані представляють собою **потік байтів**

Ми розглянемо бібліотеки, які можуть виконати цей виклик API за нас, а також те, як зробити це самостійно.

### Що може API?

API надає спосіб відправки повідомлення користувачеві **з даними або без них**, а також надає **інструкції щодо відправки** повідомлення.

#### 🔒 Шифрування даних

Дані, які ви відправляєте за допомогою push-повідомлення, повинні бути **зашифровані**.

**Причина:** Це не дозволяє службам push-повідомлень, якими може бути хто завгодно, переглядати дані, відправлені разом з push-повідомленням.

> ⚠️ **Важливо:** Це критично важливо, оскільки саме браузер вирішує, яку службу push-повідомлень використовувати, що може відкрити двері для браузерів, які використовують службу push-повідомлень, яка не є безпечною або надійною.

#### 📬 Черга повідомлень

Коли ви запускаєте push-повідомлення:

1. **Служба push-повідомлень** отримує виклик API
2. **Ставить повідомлення в чергу**
3. Повідомлення **залишається в черзі** до тих пір, поки пристрій користувача не підключиться до мережі
4. Служба push-повідомлень **доставляє повідомлення**

Інструкції, які ви можете дати службі push-повідомлень, визначають, **як push-повідомлення поміщаються в чергу**.

**В інструкції можна вказати такі деталі:**

- **⏰ Час життя push-повідомлення (TTL)** — визначає, як довго повідомлення повинно перебувати в черзі, перш ніж воно буде видалено і не доставлено
- **🔋 Терміновість повідомлення** — корисно в разі, якщо служба push-повідомлень продовжує термін служби батареї користувача, доставляючи тільки повідомлення з високим пріоритетом
- **🏷️ Тема повідомлення** — присвоїти push-повідомленню ім'я «теми», яке замінить будь-яке повідомлення, що очікує, цим новим повідомленням

![Схема надсилання push-повідомлення з сервера](https://web.dev/static/articles/push-notifications-how-push-works/image/when-server-wishes-send-79b65f127c0a4.svg)

## Крок 3: Надішліть подію на пристрій користувача

Після того як ми надіслали push-повідомлення, **служба push-повідомлень зберігатиме ваше повідомлення** на своєму сервері доти, доки не відбудеться одна з таких подій:

1. **✅ Пристрій підключається до мережі**, і служба push доставляє повідомлення
2. **⏰ Термін дії повідомлення закінчується** — у цьому випадку служба push-повідомлень видаляє повідомлення зі своєї черги і воно ніколи не буде доставлено

#### 🔄 Процес доставки

Коли служба push-повідомлень доставляє повідомлення, браузер:

1. **Отримує повідомлення**
2. **Розшифровує всі дані**
3. **Відправляє подію push-повідомлення** в ваш service worker

#### 🛠️ Що таке Service Worker?

**Service Worker** — це «спеціальний» файл JavaScript з унікальними можливостями:

- ✨ Браузер може виконати цей JavaScript **без відкриття вашої сторінки**
- 🔒 Може виконуватися навіть коли **браузер закритий**
- 🚀 Має доступ до спеціальних API (такі як push), які **недоступні на веб-сторінці**

#### 📋 Можливості в push-події

Всередині події «push» service worker-а ви можете виконувати **будь-які фонові завдання**:

- 📊 Здійснювати **аналітичні виклики**
- 💾 **Кешувати сторінки** в автономному режимі
- 🔔 **Показувати повідомлення**

![Схема доставки push-повідомлення на пристрій](https://web.dev/static/articles/push-notifications-how-push-works/image/when-push-message-is-sen-d0038e68ec88c.svg?hl=ru)

---

# Підписка користувача

Перший крок — отримати **дозвіл користувача** на надсилання йому push-повідомлень, а потім ми зможемо отримати **`PushSubscription`**.

JavaScript API для цього досить простий, тому давайте пройдемося по логічній схемі.

## 🔍 Feature Detection (Виявлення функцій)

Спочатку нам потрібно перевірити, чи дійсно поточний браузер **підтримує push-повідомлення**.

### 📋 Дві простих перевірки:

1. **Перевірте наявність `serviceWorker`** в navigator
2. **Перевірте наявність `PushManager`** у window

```js
if (!('serviceWorker' in navigator)) {
  // Service Worker isn't supported on this browser, disable or hide UI.
  return;
}

if (!('PushManager' in window)) {
  // Push isn't supported on this browser, disable or hide UI.
  return;
}
```

> 📈 **Підтримка браузерів:** Незважаючи на те, що підтримка браузерів як для Service Worker, так і для push-повідомлень швидко зростає, завжди корисно передбачити feature detection для обох і **поступово вдосконалювати** (progressive enhancement).

## 📝 Реєстрація Service Worker

Завдяки feature detection ми знаємо, що підтримуються як **Service Worker**, так і **Push**. Наступним кроком буде **«реєстрація»** нашого Service Worker.

### 🔧 Що відбувається при реєстрації:

Коли ми реєструємо Service Worker, ми **повідомляємо браузеру**, де знаходиться наш файл Service Worker.

**Важливо:**
- Файл, як і раніше, являє собою просто **JavaScript**
- Браузер **«надасть йому доступ»** до Service Worker API, включаючи push
- Точніше, браузер запускає файл в **Service Worker environment**

### 💻 Код реєстрації:

Щоб зареєструвати Service Worker, викличте `navigator.serviceWorker.register()`, передавши шлях до нашого файлу:

```js
function registerServiceWorker() {
  return navigator.serviceWorker
    .register('/service-worker.js')
    .then(function (registration) {
      console.log('Service worker successfully registered.');
      return registration;
    })
    .catch(function (err) {
      console.error('Unable to register service worker.', err);
    });
}
```

### 🔄 Що відбувається за лаштунками:

Ця функція повідомляє браузеру, що у нас є **Service Worker файл** і де він знаходиться. У даному випадку файл Service Worker знаходиться за адресою **`/service-worker.js`**.

**Кроки, які виконує браузер після виклику `register()`:**

1. **📥 Завантажує файл** Service Worker
2. **▶️ Запускає JavaScript**
3. **✅ Якщо все працює правильно** і помилок немає, Promise, що повертається функцією `register()`, буде resolved
4. **❌ Якщо є помилки**, Promise буде rejected

> 🛠️ **Debugging:** Якщо `register()` відхиляє запит, двічі перевірте свій JavaScript на наявність помилок в **Chrome DevTools**.

### 📋 ServiceWorkerRegistration

Коли `register()` успішно виконується, він повертає **`ServiceWorkerRegistration`**. Ми будемо використовувати цю реєстрацію для доступу до **[PushManager API](https://developer.mozilla.org/docs/Web/API/PushManager)**.

## 🔐 Запит дозволу (Permission Request)

Ми зареєстрували нашого Service Worker і готові підписати користувача. Наступний крок — **отримати від користувача дозвіл** на надсилання йому push-повідомлень.

### ⚠️ Особливості API

**Permission API** для отримання дозволу відносно простий, але є **недолік**:

- API нещодавно змінився з **callback** на повернення **Promise**
- Ми не можемо сказати, яка версія API реалізована в поточному браузері
- Тому доведеться **реалізувати обидві версії** та обробляти обидві

```js
function askPermission() {
  return new Promise(function (resolve, reject) {
    const permissionResult = Notification.requestPermission(function (result) {
      resolve(result);
    });

    if (permissionResult) {
      permissionResult.then(resolve, reject);
    }
  }).then(function (permissionResult) {
    if (permissionResult !== 'granted') {
      throw new Error("We weren't granted permission.");
    }
  });
}
```

### 🔔 Permission Prompt

У наведеному вище коді **важливим фрагментом** є виклик `Notification.requestPermission()`. Цей метод відобразить користувачу **permission prompt**:

![Permission prompt на desktop](https://web.dev/static/articles/push-notifications-subscribing-a-user/image/permission-prompt-deskto-bbcb86863e3f3_856.png)

### 📊 Можливі результати

Щойно користувач взаємодіяв із запитом дозволу, натиснувши **«Allow»**, **«Block»** або просто **закривши його**, ми отримаємо результат у вигляді рядка:

- **`'granted'`** — дозвіл надано
- **`'default'`** — користувач закрив prompt без вибору
- **`'denied'`** — користувач заблокував повідомлення

### ✅ Обробка результату

У наведеному вище прикладі коду **Promise**, що повертається функцією `askPermission()`, виконується, якщо дозвіл надано, в іншому випадку ми видаємо помилку, через яку Promise відхиляється.

### ⚠️ Важливий edge case - кнопка "Block"

**Критично важливо:** Якщо користувач натисне кнопку **«Block»**, ваш веб-застосунок **не зможе знову запитати** у користувача дозвіл.

**Що доведеться робити користувачу:**
- Вручну **«розблокувати»** ваш застосунок
- Змінити статус дозволів у **прихованих налаштуваннях браузера**

> 🎯 **Best Practice:** Добре обміркуйте, **як і коли** ви запитуєте у користувача дозвіл, тому що якщо він натисне «Block», скасувати це рішення буде **непросто**.

### 😊 Позитивна статистика

**Хороша новина:** Більшість користувачів **раді надати дозвіл**, якщо вони **знають, чому його запитують**.

> 📚 **Далі:** Пізніше ми розглянемо, як деякі популярні сайти запитують дозвіл.

## 📝 Підписка користувача через PushManager

Щойно наш Service Worker **зареєстрований** і ми **отримали дозвіл**, ми можемо підписати користувача, викликавши **`registration.pushManager.subscribe()`**.
```js
function subscribeUserToPush() {
  return navigator.serviceWorker
    .register('/service-worker.js')
    .then(function (registration) {
      const subscribeOptions = {
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(
          'BEl62iUYgUivxIkv69yViEuiBIa-Ib9-SkvMeAtA3LFgDzkrxZJjSgSnfckjBJuBkr3qBUYIHBQFLXYp5Nksh8U',
        ),
      };

      return registration.pushManager.subscribe(subscribeOptions);
    })
    .then(function (pushSubscription) {
      console.log(
        'Received PushSubscription: ',
        JSON.stringify(pushSubscription),
      );
      return pushSubscription;
    });
}
```

Під час виклику методу `subscribe()` ми передаємо об’єкт параметрів, який складається як з обов’язкових, так і з необов’язкових параметрів.

Давайте подивимось на всі варіанти, які ми можемо передати.

#### 👁️ Параметр `userVisibleOnly`
Коли push-повідомлення вперше було додано до браузерів, існувала **невпевненість** у тому, чи зможуть розробники надсилати push-повідомлення та **не показувати notifications**. Це зазвичай називають **«silent push»**, оскільки користувач не знає, що щось відбулося у фоновому режимі.

**Побоювання полягало в тому**, що розробники могли робити неприємні речі, наприклад, **постійно відстежувати місцезнаходження** користувача без його відома.

### 🛡️ Рішення проблеми

Щоб уникнути цього сценарію та дати авторам специфікацій час подумати, як найкраще підтримувати цю функцію, було додано параметр **`userVisibleOnly`**.

**Передача значення `true`** є **символічною угодою** з браузером про те, що веб-застосунок буде **відображати notification** щоразу при отриманні push-повідомлення (тобто **без silent push**).

### ⚠️ Обов'язкове значення

**На даний момент ви повинні передавати значення `true`**. Якщо ви не вкажете ключ **`userVisibleOnly`** або вкажете **`false`**, ви отримаєте таку помилку:

> ❌ **Chrome Error:** Наразі Chrome підтримує Push API лише для підписок, результатом яких будуть повідомлення, видимі користувачу. Ви можете вказати це, викликавши замість цього `pushManager.subscribe({userVisibleOnly: true})`. Див. [https://goo.gl/yqv4Q4](https://goo.gl/yqv4Q4) для отримання більш детальної інформації.

### 🔮 Майбутнє silent push

Зараз виглядає так, що **повноцінний silent push ніколи не буде реалізований** у Chrome. Замість цього автори специфікацій вивчають ідею **Budget API**, який дозволить веб-застосункам надсилати певну кількість автоматичних push-повідомлень залежно від використання веб-застосунку.

#### 🔑 Опція `applicationServerKey`
У попередньому розділі ми коротко згадали «ключі сервера застосунків». «Ключі сервера застосунків» використовуються службою push-сповіщень для ідентифікації застосунку, який підписує користувача, і для забезпечення того, щоб саме цей застосунок надсилав повідомлення цьому користувачу.

**Ключі сервера застосунків** — це пара відкритого і закритого ключів, унікальна для вашого застосунку. Закритий ключ вашого застосунку має зберігатися в таємниці, а відкритий ключ можна вільно передавати.

Опція **applicationServerKey**, що передається у виклик **subscribe()**, **є відкритим ключем застосунку**. Браузер передає його у службу push-сповіщень під час підписки користувача. Це означає, що служба push-сповіщень може пов’язати відкритий ключ вашого застосунку з **PushSubscription** користувача.

Діаграма нижче ілюструє ці кроки:
1. Ваш вебзастосунок завантажується у браузер, і ви викликаєте **subscribe()**, передаючи загальнодоступний ключ сервера застосунків.
2. Потім браузер надсилає мережевий запит до служби push-сповіщень, яка згенерує кінцеву точку, пов’яже цю кінцеву точку з відкритим ключем застосунку та поверне кінцеву точку браузеру.
3. Браузер додасть цю кінцеву точку в **PushSubscription**, який повертається через обіцянку **subscribe()**.

![](https://web.dev/static/articles/push-notifications-subscribing-a-user/image/illustration-the-public-7fcb49da285e.svg?hl=ru)

Якщо пізніше ви захочете надіслати push-повідомлення, вам потрібно буде створити заголовок **авторизації**, який міститиме інформацію, підписану **закритим ключем** вашого сервера застосунків. Коли служба push-сповіщень отримує запит на надсилання push-повідомлення, вона може перевірити цей підписаний заголовок авторизації, знайшовши відкритий ключ, пов’язаний із кінцевою точкою, що отримує запит. Якщо підпис дійсний, служба push-сповіщень знає, що він має бути отриманий із сервера застосунків із **відповідним секретним ключем**. По суті, це захід безпеки, який не дозволяє будь-кому надсилати повідомлення користувачам застосунку.

![](https://web.dev/static/articles/push-notifications-subscribing-a-user/image/how-private-application-b195d7ea99942.svg)

Технічно, **applicationServerKey** не є обов’язковим. Однак це потрібно для найпростіших реалізацій у Chrome, і в майбутньому це може знадобитися іншим браузерам. У Firefox це необов’язково.

Специфікація, яка визначає, яким має бути ключ сервера застосунків, — це [специфікація **VAPID**](https://tools.ietf.org/html/draft-thomson-webpush-vapid). Щоразу, коли ви читаєте щось про «ключі сервера застосунків» або «ключі VAPID», просто пам’ятайте, що це одне й те саме.

**Як створити ключі сервера застосунків**

Ви можете створити публічний і приватний набір ключів сервера застосунків кількома способами:

### 🌐 Онлайн генератор
Відвідайте [web-push-codelab.glitch.me](https://web-push-codelab.glitch.me/) для швидкої генерації ключів через браузер.

### 🔧 Програмний спосіб (C#)
Використовуйте бібліотеку `Lib.Net.Http.WebPush` для генерації ключів:

```csharp
using Lib.Net.Http.WebPush;

class Program
{
    static void Main()
    {
        // Генеруємо VAPID ключі
        var vapidKeys = VapidHelper.GenerateVapidKeys();

        Console.WriteLine("=== VAPID Keys Generated ===");
        Console.WriteLine($"Public Key:  {vapidKeys.PublicKey}");
        Console.WriteLine($"Private Key: {vapidKeys.PrivateKey}");
        Console.WriteLine("============================");

        // Зберігаємо в appsettings.json формат
        Console.WriteLine("\nAdd to appsettings.json:");
        Console.WriteLine($@"{{
  ""PushServiceClient"": {{
    ""Subject"": ""mailto:<EMAIL>"",
    ""PublicKey"": ""{vapidKeys.PublicKey}"",
    ""PrivateKey"": ""{vapidKeys.PrivateKey}""
  }}
}}");
    }
}
```

> ⚠️ **Важливо:** Вам потрібно створити ці ключі для вашого застосунку лише **один раз**, просто переконайтеся, що **закритий ключ залишається конфіденційним**.

**Дозволи і subscribe()**
Виклик **subscribe()** має один побічний ефект. Якщо у вашого вебзастосунку немає дозволів на показ сповіщень під час виклику **subscribe()**, браузер запросить у вас дозвіл. Це корисно, якщо ваш інтерфейс користувача працює з цим потоком, але якщо ви хочете більшого контролю (а я думаю, що більшість розробників так і зроблять), дотримуйтеся API **Notification.requestPermission()**, який ми використовували раніше.


## Що таке PushSubscription?
Ми викликаємо `subscribe()`, передаємо деякі параметри і взамін отримуємо `Permision`, яка перетворюється у **PushSubscription**, у результаті чого виходить такий код:
```js
function subscribeUserToPush() {
  return navigator.serviceWorker
    .register('/service-worker.js')
    .then(function (registration) {
      const subscribeOptions = {
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(
          'BEl62iUYgUivxIkv69yViEuiBIa-Ib9-SkvMeAtA3LFgDzkrxZJjSgSnfckjBJuBkr3qBUYIHBQFLXYp5Nksh8U',
        ),
      };

      return registration.pushManager.subscribe(subscribeOptions);
    })
    .then(function (pushSubscription) {
      console.log(
        'Received PushSubscription: ',
        JSON.stringify(pushSubscription),
      );
      return pushSubscription;
    });
}
```
Об’єкт `PushSubscription` містить усю необхідну інформацію, потрібну для надсилання push-повідомлень цьому користувачу. Якщо ви виведете вміст за допомогою `JSON.stringify()`, ви побачите наступне:
```json
    {
      "endpoint": "https://some.pushservice.com/something-unique",
      "keys": {
        "p256dh":
    "BIPUL12DLfytvTajnryr2PRdAgXS3HGKiLqndGcJGabyhHheJYlNGCeXl1dn18gSJ1WAkAPIxr4gK0_dQds4yiI=",
        "auth":"FPssNDTKnInHVndSTdbKFw=="
      }
    }
```

`endpoint` — це URL-адреса служби push-повідомлень. Щоб викликати push-повідомлення, надішліть POST-запит на цю URL-адресу.

Об'єкт `keys` містить значення, що використовуються для шифрування даних повідомлення, що надсилаються за допомогою push-повідомлення (про що ми поговоримо пізніше в цьому розділі).

## Регулярна повторна підписка для запобігання закінченню терміну дії
При підписці на push-повідомлення ви часто отримуєте `PushSubscription.expirationTime `зі значенням `null`. Теоретично це означає, що термін дії підписки ніколи не закінчується (на відміну від випадку, коли ви отримуєте `DOMHighResTimeStamp` , який повідомляє вам точний момент часу закінчення терміну дії підписки). На практиці, однак, браузери часто допускають закінчення терміну дії підписки, наприклад, якщо push-повідомлення не були отримані протягом тривалого часу або якщо браузер виявляє, що користувач не використовує додаток, у якого є дозвіл на push-повідомлення. Одним із способів запобігання цьому є повторна підписка користувача при кожному отриманому повідомленні, як показано в наступному фрагменті коду. Це вимагає, щоб ви надсилали повідомлення досить часто, щоб браузер не автоматично припиняв дію підписки, і вам слід дуже ретельно зважити переваги та недоліки законних потреб у повідомленнях проти ненавмисної розсилки спаму користувачеві тільки для того, щоб термін дії підписки не закінчився. Зрештою, не варто намагатися боротися з браузером в його спробах захистити користувача від давно забутих підписок на повідомлення.


```js
/* In the Service Worker. */

self.addEventListener('push', function(event) {
  console.log('Received a push message', event);

  // Display notification or handle data
  // Example: show a notification
  const title = 'New Notification';
  const body = 'You have new updates!';
  const icon = '/images/icon.png';
  const tag = 'simple-push-demo-notification-tag';

  event.waitUntil(
    self.registration.showNotification(title, {
      body: body,
      icon: icon,
      tag: tag
    })
  );

  // Attempt to resubscribe after receiving a notification
  event.waitUntil(resubscribeToPush());
});

function resubscribeToPush() {
  return self.registration.pushManager.getSubscription()
    .then(function(subscription) {
      if (subscription) {
        return subscription.unsubscribe();
      }
    })
    .then(function() {
      return self.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array('YOUR_PUBLIC_VAPID_KEY_HERE')
      });
    })
    .then(function(subscription) {
      console.log('Resubscribed to push notifications:', subscription);
      // Optionally, send new subscription details to your server
    })
    .catch(function(error) {
      console.error('Failed to resubscribe:', error);
    });
}
```

## Надіслати підписку на ваш сервер
Якщо у вас є примусова підписка, ви захочете надіслати її на свій сервер. Як це зробити, вирішувати вам, але невелика порада: використовуйте `JSON.stringify()`, щоб отримати всі необхідні дані з об'єкта підписки. Як альтернативу, ви можете зібрати той самий результат вручну, наприклад:

```js
const subscriptionObject = {
  endpoint: pushSubscription.endpoint,
  keys: {
    p256dh: pushSubscription.getKeys('p256dh'),
    auth: pushSubscription.getKeys('auth'),
  },
};

// The above is the same output as:

const subscriptionObjectToo = JSON.stringify(pushSubscription);
```
Відправлення підписки здійснюється на веб-сторінці наступним чином:
```js
function sendSubscriptionToBackEnd(subscription) {
  return fetch('/api/save-subscription/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(subscription),
  })
    .then(function (response) {
      if (!response.ok) {
        throw new Error('Bad status code from server.');
      }

      return response.json();
    })
    .then(function (responseData) {
      if (!(responseData.data && responseData.data.success)) {
        throw new Error('Bad response from server.');
      }
    });
}
```
Сервер вузла отримує цей запит і зберігає дані в базі даних для подальшого використання.
```js
app.post('/api/save-subscription/', function (req, res) {
  if (!isValidSaveRequest(req, res)) {
    return;
  }

  return saveSubscriptionToDatabase(req.body)
    .then(function (subscriptionId) {
      res.setHeader('Content-Type', 'application/json');
      res.send(JSON.stringify({data: {success: true}}));
    })
    .catch(function (err) {
      res.status(500);
      res.setHeader('Content-Type', 'application/json');
      res.send(
        JSON.stringify({
          error: {
            id: 'unable-to-save-subscription',
            message:
              'The subscription was received but we were unable to save it to our database.',
          },
        }),
      );
    });
});
```
Завдяки даним `PushSubscription` на нашому сервері ми можемо надсилати користувачеві повідомлення в будь-який час.


## Регулярна повторна підписка для запобігання закінченню терміну дії
При підписці на push-повідомлення ви часто отримуєте PushSubscription.expirationTime зі значенням null . Теоретично це означає, що термін дії підписки ніколи не закінчується (на відміну від випадку, коли ви отримуєте DOMHighResTimeStamp , який повідомляє вам точний момент закінчення терміну дії підписки). На практиці, однак, браузери часто допускають закінчення терміну дії підписки, наприклад, якщо push-повідомлення не надходили протягом тривалого часу або якщо браузер виявляє, що користувач не використовує додаток, у якого є дозвіл на push-повідомлення. Одним із способів запобігання цьому є повторна підписка користувача при кожному отриманому повідомленні, як показано в наступному фрагменті коду. Це вимагає, щоб ви надсилали повідомлення досить часто, щоб браузер не автоматично припиняв дію підписки, і вам слід дуже ретельно зважити переваги та недоліки законних потреб у повідомленнях проти розсилки спаму користувачеві тільки для того, щоб термін дії підписки не закінчився. Зрештою, не варто намагатися боротися з браузером в його спробах захистити користувача від давно забутих підписок на повідомлення.
```js
/* In the Service Worker. */

self.addEventListener('push', function(event) {
  console.log('Received a push message', event);

  // Display notification or handle data
  // Example: show a notification
  const title = 'New Notification';
  const body = 'You have new updates!';
  const icon = '/images/icon.png';
  const tag = 'simple-push-demo-notification-tag';

  event.waitUntil(
    self.registration.showNotification(title, {
      body: body,
      icon: icon,
      tag: tag
    })
  );

  // Attempt to resubscribe after receiving a notification
  event.waitUntil(resubscribeToPush());
});

function resubscribeToPush() {
  return self.registration.pushManager.getSubscription()
    .then(function(subscription) {
      if (subscription) {
        return subscription.unsubscribe();
      }
    })
    .then(function() {
      return self.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array('YOUR_PUBLIC_VAPID_KEY_HERE')
      });
    })
    .then(function(subscription) {
      console.log('Resubscribed to push notifications:', subscription);
      // Optionally, send new subscription details to your server
    })
    .catch(function(error) {
      console.error('Failed to resubscribe:', error);
    });
}
```

Часті запитання
Декілька поширених питань, які люди задають на цьому етапі:

**Чи можу я змінити службу push-сповіщень, яку використовує браузер?**
Ні. Службу push обирає браузер, і, як ми бачили на прикладі виклику subscribe(), браузер надсилатиме мережеві запити до служби push для отримання даних, які складають PushSubscription.

**Кожен браузер використовує різні служби Push, чи мають вони різні API?**
Усі push-сервіси очікують однаковий API.

Цей загальний API називається протоколом Web Push і описує мережевий запит, який ваш застосунок має виконати для запуску push-повідомлення.

**Якщо я підпишу користувача на робочому столі, чи підпишеться він також на своєму телефоні?**
На жаль, ні. Користувач має зареєструватися для push-сповіщень у кожному браузері, у якому хоче отримувати повідомлення. Також варто зазначити, що для цього потрібно буде надати користувачу дозвіл на кожному пристрої.

# Відправлення повідомлень за допомогою Web Push бібліотек

Однією з **болючих точок** при роботі з web push є те, що запуск push-повідомлення надзвичайно **«складний»**.

## 🔧 Технічні вимоги

Щоб викликати push-повідомлення, додатку необхідно:

1. **📤 Надіслати POST-запит** до push service відповідно до **Web Push Protocol**
2. **🔐 Використовувати VAPID** (також відомий як Application Server Keys), який по суті вимагає встановлення заголовка зі значенням, що доводить, що ваш додаток може надсилати повідомлення користувачеві
3. **🔒 Зашифрувати дані** і додати певні заголовки, щоб браузер міг правильно розшифрувати повідомлення

## ⚠️ Проблеми з діагностикою

**Основна проблема** із запуском push-повідомлень полягає в тому, що якщо ви зіткнетеся з проблемою, **її буде складно діагностувати**. З часом ситуація поліпшується і розширюється підтримка браузерів, але це далеко не просто.

> 💡 **Рекомендація:** З цієї причини я настійно рекомендую **використовувати бібліотеку** для шифрування, форматування і запуску вашого push-повідомлення.

## 📚 Структура розділу

Якщо ви дійсно хочете дізнатися, що роблять бібліотеки, ми розглянемо це в наступному розділі. Зараз ми розглянемо:

- **📋 Управління підписками**
- **📤 Використання існуючої Web Push бібліотеки** для відправки push-запитів

## 🖥️ Реалізація Backend

На стороні сервера будемо використовувати бібліотеку **`Lib.Net.Http.WebPush`**, яка під капотом буде:

- **🔒 Шифрувати** наші повідомлення
- **✍️ Підписувати** запити
- **📤 Відправляти** наші notifications

Для цього вона дає декілька базових моделей та один клас для відправки push-повідомлень.

### 📊 Модель для зберігання підписок

Почнемо з реалізації **створення та видалення підписок** та зберігання їх в БД. Для цього додамо клас `ApplicationPushSubscription`, який описує структуру таблиці та підключимо його в **Entity Framework** контекст:

```csharp
public class ApplicationPushSubscription
{
    [Key]
    public string P256Dh { get; set; }
    public string Endpoint { get; set; }
    public string Auth { get; set; }
    public Guid UserId { get; set } // Id користувача в нашій системі
}

public class DatabaseContext : DbContext
{
    public DbSet<ApplicationPushSubscription> PushSubscriptions { get; set; }
}
```

### 🔧 Service для управління підписками

Тепер створимо клас **`PushNotificationsService`**, в який додамо методи для **створення та видалення підписок**.

Метод створення буде приймати екземпляр класу **`PushSubscription`** з бібліотеки `Lib.Net.Http.WebPush`.

> 📝 **Примітка:** Його можна було б змінити на свій клас, щоб логіка бібліотеки не просочувалась в інші модулі, але задля простоти залишимо так як є.

```csharp
public class PushNotificationsService
{
    private readonly DatabaseContext _context;
    private readonly PushServiceClient _pushClient;
    
    public PushNotificationsService (DatabaseContext context, PushServiceClient pushClient)
    {
        _context = context;
        _pushClient = pushClient;
    }
    
    public async Task CreateSubscription(Guid userId, PushSubscription pushSubscription)
    {
        var subscription = new ApplicationPushSubscription()
        {
            UserId = userId,
            Endpoint = pushSubscription.Endpoint,
            P256Dh = pushSubscription.GetKey(PushEncryptionKeyName.P256DH),
            Auth = pushSubscription.GetKey(PushEncryptionKeyName.Auth)
        };
        
        if (await _context.PushSubscriptions.AnyAsync(x => x.P256Dh == subscription.P256Dh))
        {
            return;
        }
        
        _context.PushSubscriptions.Add(subscription);
    
        try
        {
            await _context.SaveChangesAsync();
        }
        catch (DbUpdateException)
        {
            var alreadyExists = await _context.PushSubscriptions.AnyAsync(x => x.P256Dh == subscription.P256Dh);
            if (!alreadyExists)
            {
                throw;
            }
        }
    }
    
    public async Task DeleteSubscription(Guid userId, string endpoint)
    {
        var subscription = await _context.PushSubscriptions
            .FirstOrDefaultAsync(x => x.UserId == userId && x.Endpoint == endpoint);
        
        if (subscription == null)
        {
            return;
        }
        
        _context.PushSubscriptions.Remove(subscription);
        await _context.SaveChangesAsync();
    }
}
```
### 📤 Метод відправки повідомлень

Реалізовуємо метод для **відправки notifications** нашим користувачам. Він буде приймати:

- **`userId`** - ID користувача, якому ми відправляємо повідомлення
- **`model`** - модель з даними повідомлення

**Алгоритм роботи:**
1. **📊 Отримати підписку** з БД
2. **📝 Серіалізувати повідомлення** в JSON
3. **📤 Передати все** бібліотеці для відправки

```csharp
public async Task SendNotificationAsync(Guid userId, PushNotificationModel model)
{
    try
    {
        var applicationPushSubscriptions = await _context.PushSubscriptions
            .Where(x => x.UserId == userId)
            .ToListAsync();
            
        foreach(var applicationPushSubscription in applicationPushSubscriptions)
        {
            var pushSubscription = new PushSubscription();
            pushSubscription.Endpoint = applicationPushSubscription.Endpoint;
            pushSubscription.SetKey(PushEncryptionKeyName.P256DH, applicationPushSubscription.P256Dh);
            pushSubscription.SetKey(PushEncryptionKeyName.Auth, applicationPushSubscription.Auth);
            
            var settings = new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver() };
            var payload = JsonConvert.SerializeObject(model, settings);
    
            var pushMessage = new PushMessage(payload);
            await _pushClient.RequestPushMessageDeliveryAsync(
                pushSubscription,
                pushMessage,
                _pushClient.DefaultAuthentication,
                VapidAuthenticationScheme.WebPush);
        }
    }
    catch (PushServiceClientException exception)
    {
        if (exception.StatusCode is HttpStatusCode.NotFound or HttpStatusCode.Gone)
        {
            // Підписка більше не дійсна, видаляємо її з БД
            var invalidSubscriptions = await _context.PushSubscriptions
                .Where(x => x.UserId == userId)
                .ToListAsync();
            _context.PushSubscriptions.RemoveRange(invalidSubscriptions);
            await _context.SaveChangesAsync();
        }
        else
        {
            // Re-Throw exception or log
            throw;
        }
    }
    catch (Exception exception)
    {
        // Re-Throw exception or log
    }
}
```
### 📋 Модель повідомлення

Структура класу **`PushNotificationModel`** досить проста та універсальна. Передаємо:

- **`Title`** - заголовок notification
- **`Body`** - текст повідомлення
- **`AdditionalData`** - додаткові дані

```csharp
public class PushNotificationModel
{
    public string Title { get; set; }
    public string Body { get; set; }
    public object AdditionalData { get; set; }
}
```

### 🌐 API Controller

Підключаємо наш сервіс в **API endpoints**:

```csharp
[Route("api/push-notifications")]
[Authorize]
[ApiController]
public class PushNotificationsController : ControllerBase
{
    private readonly PushNotificationService _pushNotificationService;

    public PushNotificationsController(PushNotificationService pushNotificationService)
    {
        _pushNotificationService = pushNotificationService;
    }
    
    [HttpPost("subscriptions")]
    public async Task<IActionResult> CreateSubscription([FromBody] PushSubscription subscription)
    {
        var user = HttpContext.GetCurrentUser(); // Our custom method to get current logged-in user
        await _pushNotificationService.CreateSubscription(user.Id, subscription);
        return Ok();
    }

    [HttpDelete("subscriptions")]
    public async Task<IActionResult> DeleteSubscription([FromQuery] string endpoint)
    {
        var user = HttpContext.GetCurrentUser();
        await _pushNotificationService.DeleteSubscription(user.Id, endpoint);
        return Ok();
    }
}
```
### ⚙️ Dependency Injection

Все що залишається — **підключити наші класи в DI** і все готово. Для простоти підключення можемо додати ще один пакет **`Lib.AspNetCore.WebPush`**.

```csharp
services.AddTransient<PushNotificationService>();
services.AddMemoryVapidTokenCache();
services.AddPushServiceClient(options =>
{
    var clientOptions = configuration.GetSection(nameof(PushServiceClient));
    options.Subject = clientOptions.GetValue<string>(nameof(options.Subject));
    options.PublicKey = clientOptions.GetValue<string>(nameof(options.PublicKey));
    options.PrivateKey = clientOptions.GetValue<string>(nameof(options.PrivateKey));
});
```

## 🌐 Реалізація Frontend

Перше, що потрібно реалізувати на стороні клієнта — це **запит прав на отримання notifications**. Для цього використовуємо об'єкт **`Notification`**:
```js
// Запит у браузера прав на підключення пушей
async function askPermission() {
    const permissionPromiseResult = await new Promise(function (resolve, reject) {
        const permissionResult = Notification.requestPermission(function (result) {
            resolve(result);
        });

        if (permissionResult) {
            permissionResult.then(resolve, reject);
        }
    });
    
    if (permissionPromiseResult !== 'granted') {
        alert("We weren't granted permission.");
    } else {
        await subscribeUserToPush();
    }
}
```

### 📋 Наступні кроки

Після того, як отримали права, нам потрібно:

1. **📝 Зареєструвати Service Worker** - в якому буде логіка відображення notifications
2. **🔐 Створити підписку** - з нашим публічним ключем
3. **📤 Відправити підписку** на наш сервер

### 🔑 Важливо про публічний ключ

При створенні підписки необхідно передати наш **публічний ключ**. Але він повинен бути в форматі **масиву байтів**, тому треба спочатку його **декодувати** і вже тоді передавати.

В результаті отримуємо об'єкт **`pushSubscription`**, який ми відправляємо на створений раніше endpoint. Крім цього, ще відправляємо **Bearer token**, щоб розуміти до якого користувача прив'язати підписку.

### 🌐 JavaScript версія (Frontend)

```js
const publicKey = "vapid-public-key";

async function subscribeUserToPush() {
    const registration = await navigator.serviceWorker.register('/service-worker.js');
    const subscribeOptions = {
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(publicKey),
    };

    var pushSubscription = await registration.pushManager.subscribe(subscribeOptions);

    fetch('http://localhost:5000/api/push-notifications/subscriptions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer 1245'
        },
        body: JSON.stringify(pushSubscription)
    }).then(function (response) {
        if (response.ok) {
            alert('Successfully subscribed for Push Notifications');
        } else {
            alert('Failed to store the Push Notifications subscription on server');
        }
    }).catch(function (error) {
        console.log('Failed to store the Push Notifications subscription on server: ' + error);
    });

    return pushSubscription;
}

function urlBase64ToUint8Array(base64String) {
    var padding = '='.repeat((4 - (base64String.length % 4)) % 4);
    var base64 = (base64String + padding).replace(/\-/g, '+').replace(/_/g, '/');

    var rawData = window.atob(base64);
    var outputArray = new Uint8Array(rawData.length);

    for (var i = 0; i < rawData.length; ++i) {
        outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
};
```

### 🔧 C# версія (Backend/Console App)

Еквівалентний код на C# для відправки підписки на сервер (наприклад, з консольного додатку або іншого backend сервісу):

```csharp
using System.Text;
using System.Text.Json;
using Lib.Net.Http.WebPush;

public class PushSubscriptionService
{
    private readonly HttpClient _httpClient;
    private readonly string _apiBaseUrl;
    private readonly string _bearerToken;

    public PushSubscriptionService(HttpClient httpClient, string apiBaseUrl, string bearerToken)
    {
        _httpClient = httpClient;
        _apiBaseUrl = apiBaseUrl;
        _bearerToken = bearerToken;
    }

    public async Task<bool> SubscribeUserToPushAsync(string endpoint, string p256dh, string auth)
    {
        try
        {
            // Створюємо PushSubscription об'єкт
            var pushSubscription = new PushSubscription
            {
                Endpoint = endpoint
            };
            pushSubscription.SetKey(PushEncryptionKeyName.P256DH, p256dh);
            pushSubscription.SetKey(PushEncryptionKeyName.Auth, auth);

            // Серіалізуємо в JSON
            var jsonContent = JsonSerializer.Serialize(pushSubscription, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            // Додаємо Authorization header
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_bearerToken}");

            // Відправляємо POST запит
            var response = await _httpClient.PostAsync($"{_apiBaseUrl}/api/push-notifications/subscriptions", content);

            if (response.IsSuccessStatusCode)
            {
                Console.WriteLine("Successfully subscribed for Push Notifications");
                return true;
            }
            else
            {
                Console.WriteLine("Failed to store the Push Notifications subscription on server");
                return false;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to store the Push Notifications subscription on server: {ex.Message}");
            return false;
        }
    }

    // Метод для декодування Base64 URL (еквівалент urlBase64ToUint8Array)
    public static byte[] UrlBase64ToByteArray(string base64String)
    {
        // Додаємо padding якщо потрібно
        var padding = 4 - (base64String.Length % 4);
        if (padding != 4)
        {
            base64String += new string('=', padding);
        }

        // Замінюємо URL-safe символи на стандартні Base64
        var base64 = base64String.Replace('-', '+').Replace('_', '/');

        // Декодуємо Base64
        return Convert.FromBase64String(base64);
    }
}

// Приклад використання
public class Program
{
    public static async Task Main(string[] args)
    {
        using var httpClient = new HttpClient();
        var service = new PushSubscriptionService(
            httpClient,
            "http://localhost:5000",
            "your-bearer-token-here"
        );

        // Приклад підписки (ці дані зазвичай отримуються з браузера)
        await service.SubscribeUserToPushAsync(
            endpoint: "https://fcm.googleapis.com/fcm/send/example-endpoint",
            p256dh: "example-p256dh-key",
            auth: "example-auth-key"
        );
    }
}
```

### ⚙️ Service Worker Implementation

Наш **Service Worker** буде максимально простим. Для початку додамо:

1. **📱 Логіку відображення push-повідомлень** та передачі в них додаткових даних з сервера
2. **🖱️ Логіку обробки кліку** по notification

### 🔄 Логіка обробки кліків

Ми будемо перевіряти чи відкритий сайт в браузері:
- **❌ Якщо ні** → відкривати його в новій вкладці
- **✅ Якщо є вкладка** → переключаємось на неї і робимо redirect на потрібну сторінку

```js

self.addEventListener('push', function (event) {
    var data = event.data.json();
    // {
    //     "title": "New Notification",
    //     "body": "This is the body of the notification"
    //     "additionalData": {
    //         "pageId": "12512515"
    //     }
    // }

    event.waitUntil(self.registration.showNotification(data.title, {
        body: data.body,
        icon: '/logo.png',
        data: {
            pageId: data.additionalData?.pageId,
        },
    }));
});

self.addEventListener('notificationclick', function (event) {
    var notification = event.notification;

    event.waitUntil(
        self.clients.matchAll().then(function (allClients) {
            if (allClients.length === 0) {
                self.clients.openWindow(
                    `${self.location.origin}/page/${notification?.data?.pageId}`,
                );
                notification.close();
                return;
            }

            allClients[0]?.navigate(`/page/${notification?.data?.pageId}`);
            allClients[0]?.focus();
            notification.close();
        }),
    );
});
```
### ✅ Результат

В результаті всіх цих маніпуляцій у нас є:

**🖥️ Сервер**, який вміє:
- **📤 Відправляти notifications**
- **📊 Управляти підписками**
- **🔐 Автентифікувати запити**

**🌐 Клієнт**, який вміє:
- **📝 Створювати підписки**
- **📱 Відображати notifications**
- **🖱️ Обробляти кліки**

## 📚 Додатковий приклад реалізації

Ми пройдемо наступні кроки:

1. **📤 Надішліть підписку** на наш backend і збережіть її
2. **📊 Отримайте збережені підписки** та ініціюйте push-повідомлення

### 💾 Збереження підписок

Збереження та запит **`PushSubscription`** з бази даних буде залежати від **мови вашого сервера** та **вибору бази даних**, але може бути корисно побачити приклад того, як це можна зробити.

На демонстраційній веб-сторінці **`PushSubscription`** надсилається на наш сервер шляхом виконання простого **POST запиту**:

### 🌐 JavaScript версія (Frontend)

```js
function sendSubscriptionToBackEnd(subscription) {
  return fetch('/api/save-subscription/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(subscription),
  })
    .then(function (response) {
      if (!response.ok) {
        throw new Error('Bad status code from server.');
      }

      return response.json();
    })
    .then(function (responseData) {
      if (!(responseData.data && responseData.data.success)) {
        throw new Error('Bad response from server.');
      }
    });
}
```

### 🔧 C# версія (Backend)

Еквівалентний код на **ASP.NET Core** для обробки збереження підписок:

```csharp
[ApiController]
[Route("api")]
public class SubscriptionController : ControllerBase
{
    private readonly ISubscriptionService _subscriptionService;
    private readonly ILogger<SubscriptionController> _logger;

    public SubscriptionController(ISubscriptionService subscriptionService, ILogger<SubscriptionController> logger)
    {
        _subscriptionService = subscriptionService;
        _logger = logger;
    }

    [HttpPost("save-subscription")]
    public async Task<IActionResult> SaveSubscription([FromBody] PushSubscriptionRequest request)
    {
        // Валідація запиту
        if (!IsValidSaveRequest(request))
        {
            return BadRequest(new
            {
                error = new
                {
                    id = "no-endpoint",
                    message = "Subscription must have an endpoint."
                }
            });
        }

        try
        {
            // Зберігаємо підписку в базі даних
            var subscriptionId = await _subscriptionService.SaveSubscriptionAsync(request);

            return Ok(new { data = new { success = true } });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unable to save subscription");

            return StatusCode(500, new
            {
                error = new
                {
                    id = "unable-to-save-subscription",
                    message = "The subscription was received but we were unable to save it to our database."
                }
            });
        }
    }

    private bool IsValidSaveRequest(PushSubscriptionRequest request)
    {
        // Перевіряємо чи є endpoint
        if (request == null || string.IsNullOrEmpty(request.Endpoint))
        {
            return false;
        }

        // Якщо потрібна підтримка payload, перевіряємо також auth і p256dh ключі
        if (request.Keys != null &&
            (string.IsNullOrEmpty(request.Keys.Auth) || string.IsNullOrEmpty(request.Keys.P256dh)))
        {
            return false;
        }

        return true;
    }
}

// Модель для запиту
public class PushSubscriptionRequest
{
    public string Endpoint { get; set; }
    public long? ExpirationTime { get; set; }
    public PushSubscriptionKeys Keys { get; set; }
}

public class PushSubscriptionKeys
{
    public string P256dh { get; set; }
    public string Auth { get; set; }
}

// Сервіс для роботи з підписками
public interface ISubscriptionService
{
    Task<int> SaveSubscriptionAsync(PushSubscriptionRequest subscription);
}

public class SubscriptionService : ISubscriptionService
{
    private readonly DatabaseContext _context;

    public SubscriptionService(DatabaseContext context)
    {
        _context = context;
    }

    public async Task<int> SaveSubscriptionAsync(PushSubscriptionRequest subscription)
    {
        var entity = new ApplicationPushSubscription
        {
            Endpoint = subscription.Endpoint,
            P256Dh = subscription.Keys?.P256dh,
            Auth = subscription.Keys?.Auth,
            // UserId можна отримати з контексту автентифікації
        };

        _context.PushSubscriptions.Add(entity);
        await _context.SaveChangesAsync();

        return entity.Id; // Припускаємо, що є Id поле
    }
}
```

### 📝 Важливі примітки

> 📌 **Валідація:** У цьому прикладі ми перевіряємо тільки **endpoint**. Якщо вам потрібна підтримка **payload**, обов'язково перевірте також ключі **`auth`** і **`p256dh`**.

### 🗄️ Вибір бази даних

У JavaScript демонстрації для зберігання підписок використовується **nedb** - це проста файлова база даних, але ви можете використовувати **будь-яку базу даних** на ваш вибір:

- **Для розробки:** SQLite, nedb
- **Для production:** PostgreSQL, MySQL, SQL Server, MongoDB

> 💡 **Рекомендація:** Для production хотілося б використовувати щось більш надійне (наприклад, PostgreSQL або MySQL).

## 📤 Відправлення Push-повідомлень

Коли справа доходить до відправлення push-повідомлення, нам в кінцевому підсумку потрібна **якась подія**, яка ініціює процес відправлення повідомлення користувачам.

### 🎯 Поширені підходи:

1. **👨‍💼 Admin панель** - створення сторінки адміністратора, на якій можна налаштувати і запустити push-повідомлення
2. **💻 Console додаток** - програма для локального запуску
3. **🔄 Event-driven** - автоматичне відправлення на основі подій в системі
4. **⏰ Scheduled jobs** - відправлення за розкладом

Будь-який підхід, який дозволить отримати доступ до списку **`PushSubscription`** і запустити код для запуску push-повідомлення.

### 🎮 Demo приклад

У нашій demo-версії є сторінка **«Admin Panel»**, яка дозволяє вам ініціювати відправку push-повідомлень. Оскільки це лише demo-версія, це **загальнодоступна сторінка**.

### 📋 Покрокова інструкція

Я збираюся розглянути **кожен крок**, необхідний для того, щоб demo-версія запрацювала. Це будуть **детальні кроки**, щоб кожен міг слідувати їм, включаючи тих, хто вперше працює з **C# та ASP.NET Core**.

### 🔑 VAPID Keys на сервері

Коли ми обговорювали підписку користувача, ми розглянули додавання **`applicationServerKey`** до опцій `subscribe()`. Цей **публічний ключ** передається на frontend, а відповідний **приватний ключ** нам знадобиться на серверній стороні.

У C# додатку ці значення додаються в конфігурацію наступним чином:

### 📋 Конфігурація VAPID Keys

**appsettings.json:**
```json
{
  "PushServiceClient": {
    "Subject": "mailto:<EMAIL>",
    "PublicKey": "BEl62iUYgUivxIkv69yViEuiBIa-Ib9-SkvMeAtA3LFgDzkrxZJjSgSnfckjBJuBkr3qBUYIHBQFLXYp5Nksh8U",
    "PrivateKey": "UUxI4O8-FbRouAevSmBQ6o18hgE4nSG3qwvJTfKc-ls"
  }
}
```

### 📦 Встановлення NuGet пакетів

Спочатку нам потрібно встановити необхідні NuGet пакети:

```bash
dotnet add package Lib.Net.Http.WebPush
dotnet add package Lib.AspNetCore.WebPush
```

### ⚙️ Налаштування в Program.cs

```csharp
using Lib.Net.Http.WebPush;
using Lib.AspNetCore.WebPush;

var builder = WebApplication.CreateBuilder(args);

// Додаємо сервіси
builder.Services.AddControllers();
builder.Services.AddDbContext<DatabaseContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// Налаштовуємо Push Service Client
builder.Services.AddTransient<PushNotificationsService>();
builder.Services.AddMemoryVapidTokenCache();
builder.Services.AddPushServiceClient(options =>
{
    var clientOptions = builder.Configuration.GetSection(nameof(PushServiceClient));
    options.Subject = clientOptions.GetValue<string>(nameof(options.Subject));
    options.PublicKey = clientOptions.GetValue<string>(nameof(options.PublicKey));
    options.PrivateKey = clientOptions.GetValue<string>(nameof(options.PrivateKey));
});

var app = builder.Build();

// Налаштовуємо pipeline
app.UseRouting();
app.MapControllers();

app.Run();
```

### 📧 Важливо про Subject

**Зверніть увагу**, що ми також включили поле **`Subject`** в конфігурації. Цей рядок повинен бути або:

- **🌐 URL-адресою** (наприклад, `https://example.com`)
- **📧 Адресою електронної пошти** `mailto:` (наприклад, `mailto:<EMAIL>`)

### 🔍 Навіщо це потрібно?

Ця частина інформації фактично буде **відправлена в Push Service** як частина запиту на запуск push-повідомлень.

**Причина:** Якщо Push Service необхідно зв'язатися з відправником, у них є **контактна інформація**, яка дозволить їм це зробити.

### ✅ Готовність до використання

Після цього **`PushServiceClient`** готовий до використання, наступним кроком буде **запуск push-повідомлення**.

### 🎮 Demo Admin Panel

У demo-версії використовується **уявна панель адміністратора** для запуску push-повідомлень.

![Admin Panel Screenshot](https://web.dev/static/articles/sending-messages-with-web-push-libraries/image/screenshot-the-admin-pag-7bb2e26069ffc_856.png)

### 🔄 Trigger механізм

Натискання кнопки **«Trigger Push Message»** призведе до відправлення **POST-запиту** до `/api/trigger-push-msg/`, який є сигналом для нашого backend відправляти push-повідомлення, тому ми створюємо **ASP.NET Core Controller** для цієї кінцевої точки:

### 📊 C# Controller для відправки

```csharp
[HttpPost("trigger-push-msg")]
public async Task<IActionResult> TriggerPushMessage([FromBody] TriggerPushRequest request)
{
    try
    {
        // Витягуємо підписки з бази даних
        var subscriptions = await _context.PushSubscriptions.ToListAsync();

        // Створюємо список задач для паралельної відправки
        var tasks = new List<Task>();

        foreach (var subscription in subscriptions)
        {
            var task = TriggerPushMessageAsync(subscription, request.DataToSend);
            tasks.Add(task);
        }

        // Чекаємо завершення всіх відправок
        await Task.WhenAll(tasks);

        return Ok(new { data = new { success = true } });
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Unable to send messages to all subscriptions");

        return StatusCode(500, new
        {
            error = new
            {
                id = "unable-to-send-messages",
                message = $"We were unable to send messages to all subscriptions: '{ex.Message}'"
            }
        });
    }
}
```

### 📤 Функція відправки

Метод **`TriggerPushMessageAsync()`** використовує бібліотеку **`Lib.Net.Http.WebPush`** для відправки повідомлення в надану підписку:

```csharp
private async Task TriggerPushMessageAsync(ApplicationPushSubscription subscription, object dataToSend)
{
    try
    {
        // Створюємо PushSubscription об'єкт
        var pushSubscription = new PushSubscription
        {
            Endpoint = subscription.Endpoint
        };
        pushSubscription.SetKey(PushEncryptionKeyName.P256DH, subscription.P256Dh);
        pushSubscription.SetKey(PushEncryptionKeyName.Auth, subscription.Auth);

        // Серіалізуємо дані
        var payload = JsonSerializer.Serialize(dataToSend, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        var pushMessage = new PushMessage(payload);

        // Відправляємо повідомлення
        await _pushClient.RequestPushMessageDeliveryAsync(
            pushSubscription,
            pushMessage,
            _pushClient.DefaultAuthentication,
            VapidAuthenticationScheme.WebPush);
    }
    catch (PushServiceClientException ex)
    {
        // Обробляємо помилки недійсних підписок
        if (ex.StatusCode == HttpStatusCode.NotFound || ex.StatusCode == HttpStatusCode.Gone)
        {
            _logger.LogInformation("Subscription has expired or is no longer valid, removing from database");

            // Видаляємо недійсну підписку
            _context.PushSubscriptions.Remove(subscription);
            await _context.SaveChangesAsync();
        }
            // Для інших помилок логуємо і перекидаємо далі
            _logger.LogError(ex, "Error sending push notification");
            throw;
        }
    }
}

// Модель для запиту
public class TriggerPushRequest
{
    public object DataToSend { get; set; }
}
```
### 🔄 Обробка результату

Виклик **`RequestPushMessageDeliveryAsync()`** поверне **Task**:

- **✅ Успіх:** Якщо повідомлення було надіслано успішно, Task завершиться успішно, і нам нічого не потрібно робити
- **❌ Помилка:** Якщо Task кидає exception, вам необхідно **вивчити помилку**, оскільки вона повідомить вам, чи дійсна **PushSubscription** чи ні

### 🔍 Аналіз помилок

Щоб визначити тип помилки Push Service, найкраще подивитися **HTTP status code**. Повідомлення про помилки різняться залежно від Push Service, і деякі з них є більш корисними, ніж інші.

### 📊 Коди стану

У цьому прикладі він перевіряє коди стану **`404`** і **`410`**:

- **`404`** - "Not Found" (Не знайдено)
- **`410`** - "Gone" (Пішов)

**Що це означає:** Термін дії підписки закінчився або вона більше не дійсна. У цих сценаріях нам необхідно **видалити підписки** з нашої бази даних.

### ⚠️ Інші помилки

У разі будь-якої іншої помилки ми просто **`throw`**, що призведе до exception в **`TriggerPushMessageAsync()`**.

### 📚 Додаткова інформація

Ми розглянемо деякі інші коди стану в наступному розділі, коли будемо більш детально розглядати **Web Push Protocol**.

> 🦊 **Debug Tip:** Якщо на цьому етапі у вас виникнуть проблеми, варто переглянути **журнали помилок Firefox**, а не Chrome. Push Service Mozilla має **набагато більш корисні повідомлення** про помилки в порівнянні з Chrome/FCM.

### 📤 Відповідь сервера

Після проходження підписок Controller автоматично поверне **відповідь у форматі JSON**:

- **✅ Успіх:** `{ "data": { "success": true } }`
- **❌ Помилка:** `{ "error": { "id": "unable-to-send-messages", "message": "..." } }`

Це все обробляється в нашому C# Controller методі вище.


```csharp
// Модель для запиту
public class PushMessageRequest
{
    public object DataToSend { get; set; }
}
```

Далі, що саме роблять для нас ці Web Push бібліотеки?

# 🌐 Протокол Web Push

Ми бачили, як можна використовувати бібліотеку для запуску push-повідомлень, але **що саме роблять ці бібліотеки?**

## 🔍 Що відбувається під капотом?

Вони відправляють **мережеві запити**, гарантуючи, що такі запити мають **правильний формат**. Специфікацією, що визначає цей мережевий запит, є **Web Push Protocol**.

![Web Push Protocol Diagram](https://web.dev/static/articles/push-notifications-web-push-protocol/image/diagram-sending-push-me-f952f3399947e.svg)

*Схема відправки push-повідомлення з вашого сервера на Push Service*

## 📋 Що розглянемо в цьому розділі:

У цьому розділі описується:

1. **🔐 Як сервер може ідентифікувати себе** за допомогою Application Server Keys
2. **📦 Як відправляються зашифровані дані** та пов'язані з ними метадані
3. **🔧 Технічні деталі** протоколу

> ⚠️ **Примітка:** Це складна сторона Web Push, і я не експерт у шифруванні, але давайте розглянемо кожну частину, оскільки **корисно знати, що ці бібліотеки роблять під капотом**.

## 🔑 Application Server Keys

Коли ми підписуємо користувача, ми передаємо **`applicationServerKey`**. Цей ключ передається в Push Service і використовується для перевірки того, що **додаток, який підписався на користувача**, також є **додатком, який запускає push-повідомлення**.

### 🔐 Процес автентифікації

Коли ми запускаємо push-повідомлення, ми відправляємо набір **заголовків**, які дозволяють Push Service **автентифікувати додаток**. (Це визначено специфікацією **VAPID**.)

### 🔄 Що насправді відбувається?

Ось кроки, зроблені для **автентифікації Application Server**:

1. **📝 Підписання:** Application Server підписує деяку **інформацію JSON** своїм **приватним ключем**
2. **📤 Відправка:** Ця підписана інформація відправляється в Push Service у вигляді **заголовка в POST запиті**
3. **🔍 Перевірка:** Push Service використовує збережений **публічний ключ**, отриманий від `pushManager.subscribe()` для перевірки того, що отримана інформація підписана **приватним ключем**, що відноситься до публічного ключа
4. **✅ Доставка:** Якщо підписана інформація дійсна, Push Service **надсилає користувачеві push-повідомлення**

> 💡 **Пам'ятайте:** Публічний ключ — це ключ **`applicationServerKey`**, що передається у виклик підписки.

### 📊 Діаграма процесу

Приклад такого потоку інформації наведено нижче. (Зверніть увагу на легенду внизу ліворуч, що позначає публічний і приватний ключі.)

![VAPID Keys Diagram](https://web.dev/static/articles/push-notifications-web-push-protocol/image/illustration-how-privat-0a010174265d8.svg?hl=ru)

**«Підписана інформація»**, додана в заголовок запиту, являє собою **JSON Web Token**.

## 🎫 JSON Web Token (JWT)

**JSON Web Token** (або скорочено **JWT**) — це спосіб відправлення повідомлення третій стороні, що дозволяє **одержувачу перевірити, хто його відправив**.

### 🔍 Як працює перевірка JWT?

Коли третя сторона отримує повідомлення:

1. **🔑 Отримує публічний ключ** відправника
2. **✅ Використовує його для перевірки підпису** JWT
3. **🎯 Якщо підпис дійсний** → JWT повинен бути підписаний відповідним приватним ключем
4. **✅ Результат:** Повідомлення від очікуваного відправника

### 📚 Бібліотеки для JWT

На [https://jwt.io/](https://jwt.io/) є **безліч бібліотек**, які можуть виконати підписання за вас, і я б **рекомендував вам робити це** там, де ви можете.

> 📖 **Для повноти:** Давайте подивимося, як вручну створити підписаний JWT.

## 🔧 Web Push і підписані JWT

Підписаний **JWT** — це просто **рядок**, хоча його можна розглядати як **три рядки, з'єднані крапками**.

![JWT Structure](https://web.dev/static/articles/push-notifications-web-push-protocol/image/a-illustration-the-strin-21d10f4865585.svg)

### 📋 Структура JWT

**Перший і другий рядки** (інформація JWT і дані JWT) являють собою **фрагменти JSON, закодовані в base64**, що означає, що вони **доступні для публічного читання**.

### 1️⃣ JWT Header (Перший рядок)

**Перший рядок** — це **інформація про сам JWT**, що вказує, який **алгоритм використовувався** для створення підпису.

**Інформація JWT для Web Push** повинна містити наступну інформацію:

```json
{
  "typ": "JWT",
  "alg": "ES256"
}
```

### 2️⃣ JWT Payload (Другий рядок)

**Другий рядок** — це **дані JWT**. Він містить інформацію про:

- **👤 Відправника JWT**
- **🎯 Для кого він призначений**
- **⏰ Як довго він дійсний**

**Для Web Push** дані матимуть такий формат:

```json
{
  "aud": "https://some-push-service.org",
  "exp": "1469618703",
  "sub": "mailto:<EMAIL>"
}
```
### 📊 Пояснення полів JWT

#### 🎯 `aud` (Audience)
**Значення `aud`** — це **«аудиторія»**, тобто для кого призначений JWT. Для Web Push аудиторією є **Push Service**, тому ми встановлюємо її як **origin Push Service**.

#### ⏰ `exp` (Expiration)
**Значення `exp`** — це **закінчення терміну дії JWT**, що не дозволяє зловмисникам **повторно використовувати JWT**, якщо вони його перехоплять.

**Вимоги:**
- Термін дії являє собою **timestamp в секундах**
- **Не повинен перевищувати 24 години**

**У C# термін дії встановлюється за допомогою:**

```csharp
// 12 годин від поточного часу
var expiration = DateTimeOffset.UtcNow.AddHours(12).ToUnixTimeSeconds();
```

> 💡 **Чому 12 годин, а не 24?** Щоб уникнути проблем з **різницею в часі** між Application Server і Push Service.

#### 📧 `sub` (Subject)
**Значення `sub`** повинно бути або:
- **🌐 URL-адресою** (наприклад, `https://example.com`)
- **📧 Адресою електронної пошти** `mailto:` (наприклад, `mailto:<EMAIL>`)

**Навіщо це потрібно?** Якщо Push Service необхідно зв'язатися з відправником, вона може знайти **контактну інформацію в JWT**.

### 🔐 Кодування JWT

Як і **інформація JWT**, **дані JWT** кодуються як **безпечний для URL-адреси рядок base64**.

### 3️⃣ JWT Signature (Третій рядок)

**Третій рядок, підпис**, є результатом:

1. **🔗 Об'єднання** перших двох рядків (інформації JWT і даних JWT) за допомогою **крапки**
2. **📝 Підписання** цього «непідписаного токена»

### 🔒 Процес підпису

Процес підпису вимагає **шифрування «непідписаного токена»** за допомогою **ES256**.

**ES256** — це скорочення від **«ECDSA з використанням кривої P-256 і алгоритму хешування SHA-256»**.

### 💻 Реалізація в C#

Замість JavaScript Web Crypto API, в C# ми можемо використовувати бібліотеку для JWT:

```csharp
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.IdentityModel.Tokens;
using System.Security.Cryptography;

public class VapidJwtGenerator
{
    public static string GenerateVapidJwt(string audience, string subject, ECDsa privateKey)
    {
        // Створюємо JWT header
        var header = new JwtHeader(new SigningCredentials(
            new ECDsaSecurityKey(privateKey),
            SecurityAlgorithms.EcdsaSha256));

        // Створюємо JWT payload
        var payload = new JwtPayload(
            issuer: null,
            audience: audience,
            claims: new[]
            {
                new Claim("sub", subject)
            },
            notBefore: DateTime.UtcNow,
            expires: DateTime.UtcNow.AddHours(12)); // 12 годин

        // Створюємо та підписуємо JWT
        var jwt = new JwtSecurityToken(header, payload);
        var tokenHandler = new JwtSecurityTokenHandler();

        return tokenHandler.WriteToken(jwt);
    }
}

// Приклад використання
public class VapidExample
{
    public static string CreateVapidJwt()
    {
        // Завантажуємо приватний ключ (з конфігурації)
        var privateKeyBytes = Convert.FromBase64String("your-private-key-here");
        using var ecdsa = ECDsa.Create();
        ecdsa.ImportPkcs8PrivateKey(privateKeyBytes, out _);

        // Генеруємо JWT
        var jwt = VapidJwtGenerator.GenerateVapidJwt(
            audience: "https://fcm.googleapis.com", // Push Service URL
            subject: "mailto:<EMAIL>",
            privateKey: ecdsa);

        Console.WriteLine($"VAPID JWT: {jwt}");
        return jwt;
    }
}
```

> 💡 **Примітка:** Бібліотека `Lib.Net.Http.WebPush` автоматично генерує та підписує JWT токени, тому вам **не потрібно робити це вручну**.

### 🔍 Перевірка JWT

**Push Service** може перевірити JWT, використовуючи **публічний ключ Application Server**, щоб:

1. **🔓 Розшифрувати підпис**
2. **✅ Переконатися**, що розшифрований рядок збігається з **«непідписаним токеном»** (тобто першими двома рядками в JWT)

### 📤 HTTP заголовки

#### 🔐 Authorization Header

**Підписаний JWT** (тобто всі три рядки, з'єднані крапками) надсилається до **Web Push Service** у вигляді заголовка **`Authorization`** з доданим **`WebPush`**:

```http
Authorization: WebPush [JWT Info].[JWT Data].[Signature]
```

#### 🔑 Crypto-Key Header

**Web Push Protocol** також стверджує, що **публічний ключ Application Server** повинен бути відправлений в заголовку **`Crypto-Key`** у вигляді **URL-безпечного рядка в кодуванні Base64** з доданим до нього **`p256ecdsa=`**:

```http
Crypto-Key: p256ecdsa=[URL Safe Base64 Public Application Server Key]
```

## 🔒 Шифрування Payload

Далі давайте подивимося, як ми можемо **відправити payload** за допомогою push-повідомлення, щоб, коли наш веб-додаток отримає push-повідомлення, він міг **отримати доступ до отриманих даних**.

### ❓ Чому потрібне шифрування?

**Загальне питання**, яке виникає у будь-кого, хто використовував інші Push Services, полягає в тому, **чому payload Web Push має бути зашифроване?** У нативних додатках push-повідомлення можуть надсилати дані у вигляді **звичайного тексту**.

### 🌐 Переваги Web Push

**Привабливість Web Push** полягає в тому, що, оскільки всі Push Services використовують **один і той же API** (Web Push Protocol), розробникам **не потрібно турбуватися** про те, хто є Push Service.

**Ми можемо:**
- ✅ Зробити запит у потрібному форматі
- ✅ Чекати відправлення push-повідомлення

### 🛡️ Безпека через шифрування

**Зворотним боком** цього є те, що розробники можуть відправляти повідомлення в **ненадійну Push Service**.

**Рішення:** Зашифрувавши payload:
- **🔒 Push Service не може прочитати** відправлені дані
- **🔓 Тільки браузер може розшифрувати** інформацію
- **🛡️ Це захищає дані користувача**

> 📚 **Специфікація:** Шифрування payload визначено в [**Message Encryption Specification**](https://tools.ietf.org/html/draft-ietf-webpush-encryption).

### 🔧 Криптографічні методи

Перш ніж ми розглянемо конкретні кроки щодо **шифрування payload push-повідомлень**, нам слід розглянути деякі **методи**, які будуть використовуватися в процесі шифрування.

> 🙏 **Подяка:** Величезна подяка **Matt Scales** за його чудову статтю про push-шифрування.

## 🔐 ECDH і HKDF

І **ECDH**, і **HKDF** використовуються протягом усього процесу шифрування і пропонують переваги для **шифрування інформації**.

### 🔄 ECDH: Elliptic Curve Diffie-Hellman

**ECDH** — це **обмін ключами Діффі-Хеллмана на основі еліптичної кривої**.

#### 👥 Приклад з Алісою та Бобом

Уявіть, що у вас є дві людини, **Аліса і Боб**, які хочуть поділитися інформацією:

1. **🔑 Генерація ключів:** І Аліса, і Боб мають свої власні **публічні та приватні ключі**
2. **🤝 Обмін:** Аліса і Боб **діляться своїми публічними ключами** один з одним
3. **🔒 Створення секрету:**
   - Аліса використовує **свій приватний ключ + публічний ключ Боба** → створює секретне значення **«X»**
   - Боб використовує **свій приватний ключ + публічний ключ Аліси** → створює те ж саме значення **«X»**
4. **✅ Результат:** **«X»** стає **загальним секретом**, і Алісі та Бобу потрібно було тільки поділитися **публічними ключами**

### 💻 Реалізація в C#

З точки зору коду, більшість мов/платформ постачаються з **бібліотеками**, що спрощують створення цих ключів.

**У C# ми зробимо наступне:**

```csharp
using System.Security.Cryptography;

// Створюємо ECDH ключі
using var ecdh = ECDiffieHellman.Create(ECCurve.NamedCurves.nistP256);

// Генеруємо ключі
var publicKey = ecdh.PublicKey.ExportSubjectPublicKeyInfo();
var privateKey = ecdh.ExportPkcs8PrivateKey();

Console.WriteLine($"Public Key: {Convert.ToBase64String(publicKey)}");
Console.WriteLine($"Private Key: {Convert.ToBase64String(privateKey)}");
```

### 🔑 HKDF: HMAC-based Key Derivation Function

**У Вікіпедії** є короткий опис **HKDF**:

> **HKDF** — це функція отримання ключів на основі HMAC, яка **перетворює будь-який слабкий ключовий матеріал** у **криптографічно стійкий ключовий матеріал**. Його можна використовувати, наприклад, для перетворення загальних секретів, якими обмінювалися Діффі-Хеллман, в ключовий матеріал, придатний для використання в **шифруванні**, **перевірці цілісності** або **автентифікації**.

#### 🛡️ Що робить HKDF?

По суті, **HKDF** буде приймати **вхідні дані**, які не є особливо безпечними, і **робити їх більш безпечними**.

#### 📋 Вимоги специфікації

**Специфікація**, що визначає це шифрування, вимагає:
- **🔐 Використання SHA-256** в якості алгоритму хешування
- **📏 Результуючі ключі** для HKDF при Web Push **не повинні бути довше 256 біт (32 байти)**

#### 💻 Реалізація в C#

**У C# це можна реалізувати так:**

```csharp
using System.Security.Cryptography;

public static class HkdfHelper
{
    // Спрощена HKDF реалізація для Web Push
    public static byte[] Hkdf(byte[] salt, byte[] ikm, byte[] info, int length)
    {
        // Extract phase
        using var extractHmac = new HMACSHA256(salt);
        var prk = extractHmac.ComputeHash(ikm);

        // Expand phase
        using var expandHmac = new HMACSHA256(prk);
        expandHmac.TransformBlock(info, 0, info.Length, null, 0);

        // Додаємо один байт зі значенням 0x01
        var oneByte = new byte[] { 0x01 };
        expandHmac.TransformFinalBlock(oneByte, 0, 1);

        var result = expandHmac.Hash;

        // Повертаємо тільки потрібну довжину
        var output = new byte[length];
        Array.Copy(result, output, Math.Min(length, result.Length));

        return output;
    }
}
```
## 📥 Входи для шифрування

Коли ми хочемо відправити користувачеві push-повідомлення з **payload**, нам потрібні **три входи**:

1. **📦 Саме payload** (дані для відправки)
2. **🔐 Секрет `auth`** з `PushSubscription`
3. **🔑 Ключ `p256dh`** з `PushSubscription`

### 🔍 Нагадування про ключі

Ми бачили значення **`auth`** і **`p256dh`**, отримані з `PushSubscription`, але для швидкого нагадування: для підписки нам будуть потрібні ці значення:

**У C# з бібліотекою `Lib.Net.Http.WebPush`:**

```csharp
// Отримуємо ключі з PushSubscription
var authKey = pushSubscription.GetKey(PushEncryptionKeyName.Auth);
var p256dhKey = pushSubscription.GetKey(PushEncryptionKeyName.P256DH);
```

### 🔐 Пояснення ключів

#### 🤫 Auth Key (Секрет)
**Значення `auth`** слід розглядати як **секрет** і **не передавати за межі** вашого додатка.

#### 🔓 P256DH Key (Публічний ключ)
**Ключ `p256dh`** є **публічним ключем**, його іноді називають **публічним ключем клієнта**. Тут ми будемо називати `p256dh` **публічним ключем підписки**.

**Як це працює:**
- **🌐 Публічний ключ підписки** генерується **браузером**
- **🔒 Браузер зберігає приватний ключ** в секреті
- **🔓 Використовує його для розшифрування** payload

### 📊 Результат шифрування

**Ці три значення:** `auth`, `p256dh` і `payload` необхідні в якості **вхідних даних**, а **результатом процесу шифрування** будуть:

1. **🔒 Зашифровані payload**
2. **🧂 Допоміжне значення (salt)**
3. **🔑 Публічний ключ**, який використовується тільки для шифрування даних


**Наступні кроки:** Тепер ви можете впевнено використовувати Web Push у своїх додатках, знаючи, що під капотом працюють надійні криптографічні механізми!
```csharp
subscriptionPubKeyLength[1] = subscriptionPubKey.length;

const localPublicKeyLength = new Uint8Array(2);
subscriptionPubKeyLength[0] = 0;
subscriptionPubKeyLength[1] = localPublicKey.length;

const contextBuffer = Buffer.concat([
  keyLabel,
  subscriptionPubKeyLength.buffer,
  subscriptionPubKey,
  localPublicKeyLength.buffer,
  localPublicKey,
]);
```

Останній буфер контексту являє собою мітку, кількість байтів у відкритому ключі підписки, за яким слідує сам ключ, потім кількість байтів у локальному відкритому ключі, а потім сам ключ.

За допомогою цього значення контексту ми можемо використовувати його при створенні одноразового номера і ключа шифрування контенту (CEK).

Ключ шифрування контенту і одноразовий номер
Nonce — це значення, яке запобігає атакам повторного відтворення, оскільки його слід використовувати тільки один раз.

Ключ шифрування контенту (CEK) — це ключ, який в кінцевому підсумку буде використовуватися для шифрування нашого корисного навантаження.

Спочатку нам потрібно створити байти даних для nonce і CEK, які представляють собою просто рядок кодування контенту, за яким слідує щойно розрахований контекстний буфер:

```js
const nonceEncBuffer = new Buffer('Content-Encoding: nonce\0', 'utf8');
const nonceInfo = Buffer.concat([nonceEncBuffer, contextBuffer]);

const cekEncBuffer = new Buffer('Content-Encoding: aesgcm\0');
const cekInfo = Buffer.concat([cekEncBuffer, contextBuffer]);
```
Ця інформація передається через HKDF, об'єднуючи сіль і PRK з nonceInfo і cekInfo:

```js
// The nonce should be 12 bytes long
const nonce = hkdf(salt, prk, nonceInfo, 12);

// The CEK should be 16 bytes long
const contentEncryptionKey = hkdf(salt, prk, cekInfo, 16);
```

Це дає нам наш nonce і ключ шифрування контенту.

Виконайте шифрування
Тепер, коли у нас є ключ шифрування контенту, ми можемо зашифрувати корисне навантаження.

Ми створюємо шифр AES128, використовуючи ключ шифрування контенту як ключ, а nonce — це вектор ініціалізації.

У Node це робиться так:
```js
const cipher = crypto.createCipheriv(
  'id-aes128-GCM',
  contentEncryptionKey,
  nonce,
);
```
Перш ніж ми зашифруємо нашу корисну навантаження, нам потрібно визначити, скільки полів ми хочемо додати на початок корисної навантаження. Причина, по якій ми хотіли б додати відступи, полягає в тому, що вони запобігають ризику того, що перехоплювачі зможуть визначити «типи» повідомлень на основі розміру корисної навантаження.

Ви повинні додати два байти заповнення, щоб вказати довжину будь-якого додаткового заповнення.

Наприклад, якщо ви не додали заповнення, у вас буде два байти зі значенням 0, тобто заповнення не існує, після цих двох байтів ви будете читати корисне навантаження. Якщо ви додали 5 байтів заповнення, перші два байти матимуть значення 5, тому споживач потім прочитає ще п'ять байтів, а потім почне читати корисне навантаження.

```js
const padding = new Buffer(2 + paddingLength);
// The buffer must be only zeros, except the length
padding.fill(0);
padding.writeUInt16BE(paddingLength, 0);
```
Потім ми запускаємо заповнення і корисне навантаження через цей шифр.

```js
const result = cipher.update(Buffer.concat(padding, payload));
cipher.final();

// Append the auth tag to the result -
// https://nodejs.org/api/crypto.html#crypto_cipher_getauthtag
const encryptedPayload = Buffer.concat([result, cipher.getAuthTag()]);
```
Тепер у нас є зашифроване корисне навантаження. Ура!

Залишилося тільки визначити, як це корисне навантаження буде відправлено в push-сервіс.

Зашифровані заголовки і тіло корисного навантаження
Щоб відправити це зашифроване корисне навантаження в службу push, нам потрібно визначити кілька різних заголовків в нашому запиті POST.

Заголовок шифрування
Заголовок «Шифрування» повинен містити сіль , яка використовується для шифрування корисних даних.

16-байтова сіль повинна бути безпечно закодована у форматі Base64 і додана в заголовок шифрування, наприклад:

```
Encryption: salt=[URL Safe Base64 Encoded Salt]
```

**Заголовок Crypto-Key**
Ми бачили, що заголовок **Crypto-Key** використовується в розділі «Ключі сервера застосунків» для зберігання відкритого ключа сервера застосунків.

Цей заголовок також використовується для спільного використання локального відкритого ключа, який застосовується для шифрування корисних даних.

Підсумковий заголовок виглядає наступним чином:

```
Crypto-Key: dh=[URL Safe Base64 Encoded Local Public Key String]; p256ecdsa=[URL Safe Base64 Encoded Public Application Server Key]
```

**Тип контенту, довжина та кодування заголовків**
Заголовок **Content-Length** — це кількість байтів у зашифрованому корисному навантаженні. Заголовки **Content-Type** і **Content-Encoding** мають фіксовані значення. Це показано нижче:

```
Content-Length: [Number of Bytes in Encrypted Payload]  
Content-Type: 'application/octet-stream'  
Content-Encoding: 'aesgcm'  
```

Якщо ці заголовки встановлені, нам потрібно надіслати зашифроване корисне навантаження як тіло нашого запиту. Зверніть увагу, що для **Content-Type** встановлено значення `application/octet-stream`. Це пов’язано з тим, що зашифроване корисне навантаження має передаватися у вигляді потоку байтів.

У NodeJS ми б зробили це так:

```js
const pushRequest = https.request(httpsOptions, function(pushResponse) {
  pushRequest.write(encryptedPayload);
  pushRequest.end();
});
```

---

**Ще заголовки?**
Ми розглянули заголовки, які використовуються для ключів JWT/сервера застосунків (тобто для ідентифікації застосунку за допомогою служби push), а також заголовки, які застосовуються для надсилання зашифрованих корисних даних.

Існують додаткові заголовки, які служби push-сповіщень використовують для зміни поведінки надісланих повідомлень. Деякі з них є обов’язковими, інші — необов’язковими.

---

**Заголовок TTL**
*Обов’язковий*

**TTL** (або *time to live*) — це ціле число, яке вказує кількість секунд, протягом яких ваше push-повідомлення буде зберігатися у службі push-повідомлень до його доставки. Після закінчення цього часу повідомлення буде видалено з черги служби push-сповіщень і не буде доставлене.

```
TTL: [Time to live in seconds]
```

Якщо ви встановите нульовий **TTL**, служба push-сповіщень спробує доставити повідомлення негайно, але якщо пристрій не зможе бути досягнутим, ваше повідомлення буде одразу видалене з черги.

Технічно служба push-сповіщень може зменшити **TTL** push-повідомлення, якщо захоче. Ви можете визначити, чи це сталося, перевіривши заголовок **TTL** у відповіді служби push-сповіщень.

Ось переклад українською:

---

**Тема**

Теми — це рядки, які можна використати для заміни повідомлень, що очікують, новим повідомленням, якщо вони мають однакові назви тем.

Це корисно у випадках, коли надсилається кілька повідомлень під час перебування пристрою в офлайні, і ви хочете, щоб користувач побачив лише останнє повідомлення, коли пристрій знову ввімкнеться.

---

**Терміновість**
*Необов’язковий*

Терміновість вказує службі push-сповіщень, наскільки важливе повідомлення для користувача. Це може бути використано службою push-сповіщень для продовження часу роботи батареї пристрою, прокидаючись лише для важливих повідомлень, коли заряд батареї низький.

Значення заголовка визначається так:

```
Urgency: [very-low | low | normal | high]
```

За замовчуванням — **normal**.

---

**Все разом**
Якщо у вас є додаткові запитання щодо того, як усе це працює, ви завжди можете подивитися, як бібліотеки надсилають push-повідомлення на сайті [**web-push-libs.org**](https://github.com/web-push-libs).

Якщо у вас є зашифроване корисне навантаження та наведені вище заголовки, вам просто потрібно зробити POST-запит до **endpoint** із `PushSubscription`.

---

**Що робити з відповіддю на цей POST-запит?**

Після того як ви відправили запит до служби push-сповіщень, необхідно перевірити код стану відповіді, оскільки він підкаже, чи був запит успішним.

| Код стану | Опис                                                                                                                                                                                           |
| --------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **201**   | Створено. Запит на надсилання push-повідомлення отримано й прийнято.                                                                                                                           |
| **429**   | Забагато запитів. Це означає, що ваш сервер досяг ліміту швидкості для служби push. Служба повинна додати заголовок **Retry-After**, який вкаже, через який час можна зробити наступний запит. |
| **400**   | Некоректний запит. Зазвичай означає, що один із заголовків недійсний або неправильно відформатований.                                                                                          |
| **404**   | Не знайдено. Підписка втратила чинність і більше не може бути використана. У такому випадку слід видалити **PushSubscription** і дочекатися повторної підписки користувача.                    |
| **410**   | Видалено. Підписка більше недійсна та повинна бути видалена з сервера застосунків. Це можна відтворити, викликавши `unsubscribe()` для **PushSubscription**.                                   |
| **413**   | Занадто великий розмір корисного навантаження. Мінімальний обов’язковий розмір, який повинна підтримувати служба push-сповіщень, — https://tools.ietf.org/html/draft-ietf-webpush-protocol-10#section-7.2 (4 КБ).                                           |

Додаткову інформацію про коди стану HTTP можна знайти у стандарті [**Web Push (RFC8030)**](https://www.rfc-editor.org/rfc/rfc8030).


Ось переклад українською:

---

# Push- події
На цьому етапі ми вже розглянули підписку користувача та надсилання push-повідомлення. Наступний крок — отримати це push-повідомлення на пристрої користувача та відобразити сповіщення (а також виконати будь-які інші дії, які нам можуть знадобитися).

---

### Подія *push*

Коли повідомлення буде отримано, у вашому Service Worker буде викликана подія `push`.

Код для налаштування слухача подій `push` виглядає майже так само, як і будь-який інший слухач подій у JavaScript:

```javascript
self.addEventListener('push', function(event) {
    if (event.data) {
        console.log('Ця push-подія містить дані: ', event.data.text());
    } else {
        console.log('Ця push-подія не містить даних.');
    }
});
```

Найдивніша частина цього коду для більшості розробників, які мало знайомі з Service Worker’ами — це змінна **self**.
`self` зазвичай використовується у **Web Workers**, до яких належить і Service Worker. Вона вказує на глобальну область видимості, щось на кшталт `window` у веб-сторінці. Але для веб-працівників і сервісних працівників `self` посилається на самого працівника.

Тож `self.addEventListener()` у наведеному прикладі можна розглядати як додавання слухача подій безпосередньо до Service Worker’а.

---

У прикладі події `push` ми перевіряємо, чи є якісь дані, і виводимо щось у консоль.

Є кілька способів прочитати дані з події `push`:

```javascript
// Повертає рядок
event.data.text()

// Розбирає дані як JSON-рядок і повертає об’єкт
event.data.json()

// Повертає Blob
event.data.blob()

// Повертає ArrayBuffer
event.data.arrayBuffer()
```

Більшість розробників використовують `.json()` або `.text()`, залежно від того, у якому форматі дані очікує отримати застосунок.

---

У цьому прикладі ми показали, як додати слухача `push` та отримати доступ до даних, але **тут бракує двох дуже важливих речей**:

1. Показу сповіщення.
2. Використання `event.waitUntil()`.

---

**Очікування виконання (`waitUntil`)**

Одне з важливих понять про Service Worker полягає в тому, що ви майже не контролюєте, коли його код буде виконуватись.
Браузер вирішує, коли його активувати, а коли зупинити.

Єдиний спосіб сказати браузеру: *«Гей, я зараз зайнятий важливими справами»* — це передати обіцянку (Promise) у метод `event.waitUntil()`.
У такому випадку браузер триматиме Service Worker активним, доки переданий вами Promise не буде виконаний.

---

При роботі з `push`-подіями є додаткова вимога: **ви повинні показати сповіщення до того, як переданий у `waitUntil()` Promise буде виконаний**.

---

**Базовий приклад відображення сповіщення:**

```javascript
self.addEventListener('push', function(event) {
    const promiseChain = self.registration.showNotification('Hello, World.');

    event.waitUntil(promiseChain);
});
```

Виклик `self.registration.showNotification()` — це метод, який показує сповіщення користувачу та повертає Promise, що виконується після відображення сповіщення.

Щоб зробити приклад максимально зрозумілим, це Promise зберігається у змінній `promiseChain`, а потім передається в `event.waitUntil()`.

Так, це виглядає трохи багатослівно, але я бачив чимало проблем, спричинених тим, що розробники неправильно розуміли, **що саме треба передавати в `waitUntil()`**, або переривали ланцюжок Promise’ів.

---

**Більш складний приклад**, у якому виконується мережевий запит даних та відстеження `push`-події через аналітику, може виглядати так:


```js
self.addEventListener('push', function(event) {
    const analyticsPromise = pushReceivedTracking();
    const pushInfoPromise = fetch('/api/get-more-data')
    .then(function(response) {
        return response.json();
    })
    .then(function(response) {
        const title = response.data.userName + ' says...';
        const message = response.data.message;

        return self.registration.showNotification(title, {
        body: message
        });
    });

    const promiseChain = Promise.all([
    analyticsPromise,
    pushInfoPromise
    ]);

    event.waitUntil(promiseChain);
});
```

Тут ми викликаємо функцію, яка повертає Promise — `pushReceivedTracking()`.
Для прикладу можна уявити, що вона надсилає мережевий запит до нашого постачальника аналітики.

Ми також виконуємо мережевий запит, отримуємо відповідь і показуємо сповіщення, використовуючи дані з відповіді як заголовок і текст повідомлення.

---

Ми можемо гарантувати, що Service Worker залишатиметься активним, доки обидва завдання не будуть виконані, **об’єднавши ці Promise за допомогою `Promise.all()`**.
Отриманий Promise передається в `event.waitUntil()`, що означає, що браузер чекатиме завершення обох завдань перед тим, як перевірити, чи було показано сповіщення, і завершити роботу Service Worker’а.

---

> **Примітка.** Якщо ви коли-небудь помітите, що ваші ланцюжки Promise стали заплутаними або складними, спробуйте розбити їх на окремі функції — це зменшує складність.
> Також рекомендую блог-пост Філіпа Уолтона про "розплутування" ланцюжків Promise.
> Головний висновок: експериментуйте з тим, як писати та об’єднувати Promise, щоб знайти стиль, який підходить саме вам.

Причина, чому варто приділяти увагу `waitUntil()` і тому, як його використовувати, полягає в тому, що **одна з найпоширеніших проблем** серед розробників — це коли ланцюжок Promise неправильний або "зламаний", і тоді Chrome показує **"типове" (default)** сповіщення.

![](https://web.dev/static/articles/push-notifications-handling-messages/image/an-image-the-default-not-ec0a0feda0315_856.png)


Chrome показуватиме лише повідомлення **«Цей сайт було оновлено у фоновому режимі»**, коли отримано push-повідомлення, а подія `push` у Service Worker не показує сповіщення після завершення Promise, переданого в `event.waitUntil()`.

Головна причина, чому розробники часто з цим стикаються, полягає в тому, що їхній код викликає `self.registration.showNotification()`, але нічого не робить з поверненим Promise.
Час від часу це призводить до відображення **типового (default)** сповіщення.

Наприклад, якщо у наведеному вище прикладі ми приберемо `return self.registration.showNotification()`, ми ризикуємо побачити саме таке стандартне повідомлення.

```js
self.addEventListener('push', function(event) {
    const analyticsPromise = pushReceivedTracking();
    const pushInfoPromise = fetch('/api/get-more-data')
    .then(function(response) {
        return response.json();
    })
    .then(function(response) {
        const title = response.data.userName + ' says...';
        const message = response.data.message;

        self.registration.showNotification(title, {
        body: message
        });
    });

    const promiseChain = Promise.all([
    analyticsPromise,
    pushInfoPromise
    ]);

    event.waitUntil(promiseChain);
});
```

Ви бачите, як легко це пропустити.

Просто пам’ятайте: якщо ви бачите це сповіщення, перевірте свої ланцюжки Promise та `event.waitUntil()`.

У наступному розділі ми розглянемо, що можна зробити для стилізації сповіщень і який контент можна в них відображати.


https://web.dev/articles/push-notifications-display-a-notification?hl=ru ...
