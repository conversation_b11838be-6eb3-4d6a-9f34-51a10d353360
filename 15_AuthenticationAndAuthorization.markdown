# Аутентифікація та авторизація в ASP.NET Core

Аутентифікація та авторизація є ключовими аспектами безпеки веб-додатків. Аутентифікація визначає, хто є користувачем, відповідаючи на питання: "Ким є користувач?". Авторизація визначає, які дії користувач може виконувати, відповідаючи на питання: "Які права має користувач у системі?". ASP.NET Core надає вбудовану підтримку для реалізації цих механізмів, дозволяючи використовувати різні схеми аутентифікації (куки, JWT-токени, OAuth тощо) та гнучкі політики авторизації.

## Вступ до аутентифікації та авторизації

### Аутентифікація
Аутентифікація — це процес перевірки ідентичності користувача на основі наданих даних, таких як логін і пароль, токени або інші облікові дані. У ASP.NET Core за аутентифікацію відповідає middleware `AuthenticationMiddleware`, який вбудовується в конвеєр обробки запитів за допомогою методу `UseAuthentication()`:

```csharp
app.UseAuthentication();
```

Цей метод необхідно викликати до будь-яких middleware, які залежать від аутентифікації, наприклад, `UseAuthorization()`. Для конфігурації аутентифікації використовується метод `AddAuthentication()`, який додає необхідні сервіси до контейнера залежностей:

```csharp
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = CookieAuthenticationDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = CookieAuthenticationDefaults.AuthenticationScheme;
}).AddCookie();
```

Метод `AddAuthentication()` дозволяє вказати схему аутентифікації за замовчуванням і підключити конкретні обробники, такі як куки (`AddCookie()`) або JWT-токени (`AddJwtBearer()`).

### Авторизація
Авторизація визначає, чи має користувач доступ до певного ресурсу або дії. Вона залежить від аутентифікації, оскільки спочатку необхідно ідентифікувати користувача. У ASP.NET Core авторизація реалізується через middleware `AuthorizationMiddleware`, який додається методом `UseAuthorization()`:

```csharp
app.UseAuthorization();
```

Сервіси авторизації реєструються за допомогою методу `AddAuthorization()`:

```csharp
builder.Services.AddAuthorization();
```

Авторизація базується на атрибуті `[Authorize]`, який обмежує доступ до ресурсів, а також на політиках, ролях і claims для детального контролю доступу.

### Основні схеми аутентифікації
ASP.NET Core підтримує кілька схем аутентифікації:
- **Cookies**: Використовує HTTP-куки для зберігання даних про користувача. Схема визначена в `CookieAuthenticationDefaults.AuthenticationScheme`.
- **Bearer (JWT)**: Використовує JSON Web Tokens для аутентифікації. Схема визначена в `JwtBearerDefaults.AuthenticationScheme`. Потребує NuGet-пакет `Microsoft.AspNetCore.Authentication.JwtBearer`.
- **OAuth/OpenID Connect**: Для аутентифікації через зовнішні провайдери (Google, Facebook тощо).
- **Кастомні схеми**: Дозволяють створювати власні обробники аутентифікації.

## Аутентифікація з куки

Аутентифікація на основі куки є традиційним і поширеним підходом у веб-додатках. Вона зберігає дані про користувача в зашифрованих HTTP-куках, які передаються між клієнтом і сервером.

### Налаштування аутентифікації з куки
Приклад мінімального API з аутентифікацією на основі куки:

```csharp
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

var builder = WebApplication.CreateBuilder(args);

// Умовна база даних користувачів
var users = new List<User>
{
    new User("<EMAIL>", "12345"),
    new User("<EMAIL>", "55555")
};

// Налаштування аутентифікації з куки
builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options =>
    {
        options.LoginPath = "/login";
        options.LogoutPath = "/logout";
        options.AccessDeniedPath = "/access-denied";
        options.Cookie.HttpOnly = true; // Захист від доступу через JavaScript
        options.Cookie.SecurePolicy = CookieSecurePolicy.Always; // Лише HTTPS
        options.Cookie.SameSite = SameSiteMode.Strict; // Захист від CSRF
    });
builder.Services.AddAuthorization();

var app = builder.Build();

app.UseAuthentication();
app.UseAuthorization();

// Форма логіну
app.MapGet("/login", async context =>
{
    context.Response.ContentType = "text/html; charset=utf-8";
    string loginForm = @"<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8' />
    <title>ITStep</title>
</head>
<body>
    <h2>Вхід</h2>
    <form method='post'>
        <p>
            <label>Email</label><br />
            <input name='email' />
        </p>
        <p>
            <label>Пароль</label><br />
            <input type='password' name='password' />
        </p>
        <input type='submit' value='Увійти' />
    </form>
</body>
</html>";
    await context.Response.WriteAsync(loginForm);
});

// Обробка логіну
app.MapPost("/login", async (string? returnUrl, HttpContext context) =>
{
    var form = context.Request.Form;
    if (!form.ContainsKey("email") || !form.ContainsKey("password"))
        return Results.BadRequest("Email або пароль не вказані");

    string email = form["email"];
    string password = form["password"];

    var user = users.FirstOrDefault(u => u.Email == email && u.Password == password);
    if (user is null) return Results.Unauthorized();

    var claims = new List<Claim>
    {
        new Claim(ClaimTypes.Name, user.Email)
    };
    var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
    await context.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(claimsIdentity));

    return Results.Redirect(returnUrl ?? "/");
});

// Вихід
app.MapGet("/logout", async (HttpContext context) =>
{
    await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
    return Results.Redirect("/login");
});

// Сторінка відмови в доступі
app.MapGet("/access-denied", () => Results.Text("Доступ заборонено", statusCode: 403));

// Захищений ресурс
app.MapGet("/", [Authorize] (HttpContext context) =>
{
    var user = context.User.Identity?.Name ?? "Анонім";
    return $"Вітаємо, {user}!";
});

app.Run();

record User(string Email, string Password);
```

**Результат аутентифікації з куки**:
*Зображення: Веб-браузер відображає форму логіну на `/login`. Після введення коректних даних користувач перенаправляється на `/`, де відображається привітання. Некоректні дані повертають код 401. Запит до `/` без аутентифікації перенаправляє на `/login`.*

**Пояснення**:
- `AddCookie()` налаштовує схему аутентифікації з куки, встановлюючи шляхи для логіну, виходу та відмови в доступі.
- Налаштування `HttpOnly`, `SecurePolicy` і `SameSite` підвищують безпеку куки, захищаючи від XSS, передачі через незахищені канали та CSRF-атак.
- `SignInAsync` створює аутентифікаційні куки, а `SignOutAsync` їх видаляє.
- Атрибут `[Authorize]` обмежує доступ до кореня (`/`) лише для аутентифікованих користувачів.

### Захист від CSRF
Для захисту від CSRF-атак у формах логіну можна додати анти-CSRF-токен, використовуючи ASP.NET Core Antiforgery:

```csharp
builder.Services.AddAntiforgery();
```

Оновлена форма логіну з анти-CSRF-токеном:

```csharp
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Antiforgery;
using System.Security.Claims;

var builder = WebApplication.CreateBuilder(args);

// Умовна база даних користувачів
var users = new List<User>
{
    new User("<EMAIL>", "12345"),
    new User("<EMAIL>", "55555")
};

// Налаштування аутентифікації та анти-CSRF
builder.Services.AddAntiforgery();
builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options =>
    {
        options.LoginPath = "/login";
        options.LogoutPath = "/logout";
        options.AccessDeniedPath = "/access-denied";
        options.Cookie.HttpOnly = true;
        options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
        options.Cookie.SameSite = SameSiteMode.Strict;
    });
builder.Services.AddAuthorization();

var app = builder.Build();

app.UseAuthentication();
app.UseAuthorization();

// Форма логіну з анти-CSRF
app.MapGet("/login", async (HttpContext context, IAntiforgery antiforgery) =>
{
    var token = antiforgery.GetAndStoreTokens(context).RequestToken;
    context.Response.ContentType = "text/html; charset=utf-8";
    string loginForm = $@"<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8' />
    <title>ITStep</title>
</head>
<body>
    <h2>Вхід</h2>
    <form method='post'>
        <input type='hidden' name='{antiforgery.GetAndStoreTokens(context).FormFieldName}' value='{token}' />
        <p>
            <label>Email</label><br />
            <input name='email' />
        </p>
        <p>
            <label>Пароль</label><br />
            <input type='password' name='password' />
        </p>
        <input type='submit' value='Увійти' />
    </form>
</body>
</html>";
    await context.Response.WriteAsync(loginForm);
});

// Обробка логіну з перевіркою анти-CSRF
app.MapPost("/login", async (HttpContext context, IAntiforgery antiforgery) =>
{
    await antiforgery.ValidateRequestAsync(context);

    var form = context.Request.Form;
    if (!form.ContainsKey("email") || !form.ContainsKey("password"))
        return Results.BadRequest("Email або пароль не вказані");

    string email = form["email"];
    string password = form["password"];

    var user = users.FirstOrDefault(u => u.Email == email && u.Password == password);
    if (user is null) return Results.Unauthorized();

    var claims = new List<Claim>
    {
        new Claim(ClaimTypes.Name, user.Email)
    };
    var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
    await context.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(claimsIdentity));

    return Results.Redirect(form.ContainsKey("returnUrl") ? form["returnUrl"] : "/");
});

// Інші маршрути
app.MapGet("/logout", async (HttpContext context) =>
{
    await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
    return Results.Redirect("/login");
});

app.MapGet("/access-denied", () => Results.Text("Доступ заборонено", statusCode: 403));

app.MapGet("/", [Authorize] (HttpContext context) =>
{
    var user = context.User.Identity?.Name ?? "Анонім";
    return $"Вітаємо, {user}!";
});

app.Run();

record User(string Email, string Password);
```

**Результат із анти-CSRF**:
*Зображення: Форма логіну містить приховане поле з анти-CSRF-токеном. Некоректний токен повертає помилку валідації. Успішний логін перенаправляє на захищений ресурс.*

**Пояснення**:
- `AddAntiforgery()` додає сервіс для генерації та перевірки анти-CSRF-токенів.
- Токен додається до форми як приховане поле та перевіряється на сервері за допомогою `ValidateRequestAsync`.

# Детальний огляд аутентифікації в ASP.NET Core

Аутентифікація в ASP.NET Core — це процес ідентифікації користувача, який надсилає запит до програми. Вона відповідає на питання: "Хто цей користувач?". У цьому розділі ми детально розглянемо, як працює аутентифікація, які компоненти залучені, що відбувається "під капотом", і як налаштувати аутентифікацію з використанням куки та JWT-токенів.

## Основи аутентифікації

Аутентифікація в ASP.NET Core інтегрована в конвеєр обробки HTTP-запитів через middleware (`AuthenticationMiddleware`). Цей компонент аналізує запит, визначає, чи містить він облікові дані (наприклад, куки, JWT-токени чи заголовки), і встановлює ідентичність користувача у вигляді об’єкта `ClaimsPrincipal`, який зберігається в `HttpContext.User`.

### Ключові компоненти
1. **AuthenticationMiddleware**:
   - Відповідає за обробку аутентифікаційних даних у запиті.
   - Викликається методом `app.UseAuthentication()` у конвеєрі обробки.
   - Передає запит до зареєстрованих обробників аутентифікації (handlers) для перевірки облікових даних.

2. **IAuthenticationService**:
   - Інтерфейс, який визначає методи для виконання аутентифікації, наприклад, `AuthenticateAsync`, `SignInAsync`, `SignOutAsync`.
   - Реалізується обробниками аутентифікації для конкретних схем (наприклад, `CookieAuthenticationHandler` або `JwtBearerHandler`).

3. **ClaimsPrincipal**:
   - Представляє користувача в програмі.
   - Містить одну або кілька ідентичностей (`ClaimsIdentity`), які описують користувача через набір claims (заяв).
   - Зберігається в `HttpContext.User` після успішної аутентифікації.

4. **ClaimsIdentity**:
   - Містить набір claims, які описують користувача (наприклад, ім’я, email, роль).
   - Визначає тип аутентифікації (наприклад, "Cookies" або "Bearer") та статус аутентифікованості (`IsAuthenticated`).

5. **AuthenticationScheme**:
   - Визначає схему аутентифікації (наприклад, "Cookies" або "Bearer").
   - Кожна схема пов’язана з конкретним обробником, який обробляє запити для цієї схеми.

### Потік аутентифікації
1. **Надходження запиту**:
   - Клієнт надсилає HTTP-запит, який може містити куки, заголовок `Authorization: Bearer <token>` або інші облікові дані.
   - Запит потрапляє до `AuthenticationMiddleware`.

2. **Виклик IAuthenticationService**:
   - Middleware викликає метод `AuthenticateAsync` для кожної зареєстрованої схеми аутентифікації.
   - Обробник схеми (наприклад, `CookieAuthenticationHandler`) аналізує дані запиту:
     - Для куки: перевіряє наявність зашифрованої куки `.AspNetCore.Cookies`.
     - Для JWT: перевіряє заголовок `Authorization`, декодує токен і валідує його (емітент, аудиторія, час дії, підпис).

3. **Створення ClaimsPrincipal**:
   - Якщо аутентифікація успішна, обробник створює `ClaimsPrincipal` із набором `ClaimsIdentity`, що містить claims (наприклад, `ClaimTypes.Name`, `ClaimTypes.Role`).
   - Цей об’єкт встановлюється в `HttpContext.User`.

4. **Передача в конвеєр**:
   - Якщо аутентифікація пройшла, запит передається далі (наприклад, до `AuthorizationMiddleware`).
   - Якщо аутентифікація не вдалася, повертається HTTP-код 401 (Unauthorized) або перенаправлення на сторінку логіну (для куки).

**Зображення**: *Діаграма потоку аутентифікації: клієнт надсилає запит -> AuthenticationMiddleware викликає IAuthenticationService -> обробник схеми перевіряє дані -> створюється ClaimsPrincipal -> HttpContext.User оновлюється.*

## Налаштування аутентифікації

Аутентифікація налаштовується за допомогою методу `AddAuthentication()` у контейнері сервісів (`IServiceCollection`) та `UseAuthentication()` у конвеєрі обробки.

### Реєстрація сервісів
Метод `AddAuthentication()` додає сервіси аутентифікації та визначає схему за замовчуванням:

```csharp
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = CookieAuthenticationDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = CookieAuthenticationDefaults.AuthenticationScheme;
}).AddCookie();
```

- **DefaultAuthenticateScheme**: Схема, яка використовується для аутентифікації за замовчуванням.
- **DefaultChallengeScheme**: Схема, яка викликається, якщо користувач не аутентифікований і потрібне перенаправлення (наприклад, на сторінку логіну).

### Додавання middleware
Метод `UseAuthentication()` вбудовує `AuthenticationMiddleware` у конвеєр:

```csharp
app.UseAuthentication();
```

Цей middleware має бути розміщений перед `UseAuthorization()` і будь-якими іншими middleware, які залежать від аутентифікації.

## Аутентифікація на основі куки

### Механізм роботи
Аутентифікація на основі куки зберігає зашифровані дані користувача в HTTP-куках, які передаються між клієнтом і сервером. Основні етапи:

1. **Логін**:
   - Користувач надсилає логін і пароль через POST-запит до кінцевої точки (наприклад, `/login`).
   - Сервер перевіряє облікові дані, створює `ClaimsIdentity` із claims (наприклад, `ClaimTypes.Name`) і викликає `SignInAsync` для створення куки.

2. **Створення куки**:
   - `CookieAuthenticationHandler` шифрує `ClaimsPrincipal` за допомогою ключа шифрування (за замовчуванням використовується `DataProtectionProvider`).
   - Куки (зазвичай `.AspNetCore.Cookies`) відправляються клієнту з параметрами безпеки (`HttpOnly`, `Secure`, `SameSite`).

3. **Обробка наступних запитів**:
   - Клієнт автоматично додає куки до кожного запиту.
   - `CookieAuthenticationHandler` розшифровує куки, відновлює `ClaimsPrincipal` і встановлює його в `HttpContext.User`.

4. **Вихід**:
   - Виклик `SignOutAsync` видаляє куки, роблячи користувача неаутентифікованим.

### Приклад із куки
```csharp
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

var builder = WebApplication.CreateBuilder(args);

// Умовна база даних користувачів
var users = new List<User>
{
    new User("<EMAIL>", "12345"),
    new User("<EMAIL>", "55555")
};

// Налаштування аутентифікації з куки
builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options =>
    {
        options.LoginPath = "/login";
        options.LogoutPath = "/logout";
        options.AccessDeniedPath = "/access-denied";
        options.Cookie.HttpOnly = true;
        options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
        options.Cookie.SameSite = SameSiteMode.Strict;
        options.ExpireTimeSpan = TimeSpan.FromMinutes(30); // Час життя куки
        options.SlidingExpiration = true; // Оновлення часу життя при активності
    });
builder.Services.AddAuthorization();

var app = builder.Build();

app.UseAuthentication();
app.UseAuthorization();

// Форма логіну
app.MapGet("/login", async context =>
{
    context.Response.ContentType = "text/html; charset=utf-8";
    string loginForm = @"<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8' />
    <title>ITStep</title>
</head>
<body>
    <h2>Вхід</h2>
    <form method='post'>
        <p>
            <label>Email</label><br />
            <input name='email' />
        </p>
        <p>
            <label>Пароль</label><br />
            <input type='password' name='password' />
        </p>
        <input type='submit' value='Увійти' />
    </form>
</body>
</html>";
    await context.Response.WriteAsync(loginForm);
});

// Обробка логіну
app.MapPost("/login", async (string? returnUrl, HttpContext context) =>
{
    var form = context.Request.Form;
    if (!form.ContainsKey("email") || !form.ContainsKey("password"))
        return Results.BadRequest("Email або пароль не вказані");

    string email = form["email"];
    string password = form["password"];

    var user = users.FirstOrDefault(u => u.Email == email && u.Password == password);
    if (user is null) return Results.Unauthorized();

    var claims = new List<Claim>
    {
        new Claim(ClaimTypes.Name, user.Email),
        new Claim(ClaimTypes.Role, "User")
    };
    var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
    var claimsPrincipal = new ClaimsPrincipal(claimsIdentity);
    await context.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, claimsPrincipal);

    return Results.Redirect(returnUrl ?? "/");
});

// Вихід
app.MapGet("/logout", async (HttpContext context) =>
{
    await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
    return Results.Redirect("/login");
});

// Сторінка відмови в доступі
app.MapGet("/access-denied", () => Results.Text("Доступ заборонено", statusCode: 403));

// Захищений ресурс
app.MapGet("/", [Authorize] (HttpContext context) =>
{
    var user = context.User.Identity?.Name ?? "Анонім";
    return $"Вітаємо, {user}!";
});

app.Run();

record User(string Email, string Password);
```

**Результат**:
*Зображення: Форма логіну на `/login`. Після введення коректних даних створюється кука `.AspNetCore.Cookies`, і користувач перенаправляється на `/`. Запит без куки повертає 401 або перенаправлення на `/login`.*

**Пояснення "під капотом"**:
- **Шифрування куки**: `DataProtectionProvider` використовує ключі шифрування, які зберігаються в системі (наприклад, у файлах або реєстрі). Це забезпечує захист даних у куках.
- **SlidingExpiration**: Якщо увімкнено, час життя куки оновлюється при кожному запиті, якщо половина терміну дії вже минула.
- **HttpContext.User**: Після виклику `SignInAsync`, `ClaimsPrincipal` серіалізується в куки, а при наступних запитах розшифровується і відновлюється в `HttpContext.User`.

## Аутентифікація на основі JWT

### Механізм роботи
JWT (JSON Web Token) — це компактний, самодостатній токен, який містить інформацію про користувача у форматі JSON, зашифровану підписом. Основні етапи:

1. **Генерація токена**:
   - Сервер перевіряє облікові дані користувача.
   - Створюється JWT із заголовком (Header), вмістом (Payload) і підписом (Signature).
   - Токен повертається клієнту (зазвичай у JSON-відповіді).

2. **Передача токена**:
   - Клієнт додає токен до заголовка `Authorization: Bearer <token>` у кожному запиті.
   - `JwtBearerHandler` перевіряє токен на валідність (емітент, аудиторія, час дії, підпис).

3. **Створення ClaimsPrincipal**:
   - Якщо токен валідний, обробник витягує claims із Payload і створює `ClaimsPrincipal`.
   - Цей об’єкт встановлюється в `HttpContext.User`.

4. **Оновлення токена**:
   - JWT зазвичай мають короткий час дії (наприклад, 2 хвилини). Для продовження сесії використовуються refresh-токени, які дозволяють отримати новий JWT без повторного логіну.

### Приклад із JWT
```csharp
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Security.Cryptography;

var builder = WebApplication.CreateBuilder(args);

// Умовна база даних користувачів і токенів оновлення
var users = new List<User>
{
    new User("<EMAIL>", "12345"),
    new User("<EMAIL>", "55555")
};
var refreshTokens = new Dictionary<string, RefreshToken>();

// Налаштування JWT-аутентифікації
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidIssuer = AuthOptions.ISSUER,
            ValidateAudience = true,
            ValidAudience = AuthOptions.AUDIENCE,
            ValidateLifetime = true,
            IssuerSigningKey = AuthOptions.GetSymmetricSecurityKey(),
            ValidateIssuerSigningKey = true,
            ClockSkew = TimeSpan.Zero // Вимкнення допуску на неточність часу
        };
    });
builder.Services.AddAuthorization();

var app = builder.Build();

app.UseAuthentication();
app.UseAuthorization();

// Логін із генерацією JWT і refresh-токена
app.MapPost("/login", (UserLogin loginData) =>
{
    var user = users.FirstOrDefault(u => u.Email == loginData.Email && u.Password == loginData.Password);
    if (user is null) return Results.Unauthorized();

    var claims = new List<Claim>
    {
        new Claim(ClaimTypes.Name, user.Email),
        new Claim(ClaimTypes.Role, "User")
    };
    var accessToken = GenerateJwtToken(claims, TimeSpan.FromMinutes(2));
    var refreshToken = GenerateRefreshToken();
    refreshTokens[refreshToken.Token] = new RefreshToken { Token = refreshToken.Token, UserEmail = user.Email, ExpiryDate = DateTime.UtcNow.AddDays(7) };

    return Results.Json(new { access_token = accessToken, refresh_token = refreshToken.Token });
});

// Оновлення токена
app.MapPost("/refresh", (RefreshRequest request) =>
{
    if (!refreshTokens.TryGetValue(request.RefreshToken, out var storedToken) || storedToken.ExpiryDate < DateTime.UtcNow)
        return Results.Unauthorized();

    var user = users.FirstOrDefault(u => u.Email == storedToken.UserEmail);
    if (user is null) return Results.Unauthorized();

    var claims = new List<Claim>
    {
        new Claim(ClaimTypes.Name, user.Email),
        new Claim(ClaimTypes.Role, "User")
    };
    var newAccessToken = GenerateJwtToken(claims, TimeSpan.FromMinutes(2));
    var newRefreshToken = GenerateRefreshToken();
    refreshTokens.Remove(request.RefreshToken);
    refreshTokens[newRefreshToken.Token] = new RefreshToken { Token = newRefreshToken.Token, UserEmail = user.Email, ExpiryDate = DateTime.UtcNow.AddDays(7) };

    return Results.Json(new { access_token = newAccessToken, refresh_token = newRefreshToken.Token });
});

// Захищений ресурс
app.MapGet("/data", [Authorize] () => new { message = "Вітаємо в захищеному ресурсі!" });

app.Run();

string GenerateJwtToken(List<Claim> claims, TimeSpan lifetime)
{
    var jwt = new JwtSecurityToken(
        issuer: AuthOptions.ISSUER,
        audience: AuthOptions.AUDIENCE,
        claims: claims,
        expires: DateTime.UtcNow.Add(lifetime),
        signingCredentials: new SigningCredentials(AuthOptions.GetSymmetricSecurityKey(), SecurityAlgorithms.HmacSha256));
    return new JwtSecurityTokenHandler().WriteToken(jwt);
}

RefreshToken GenerateRefreshToken()
{
    var randomNumber = new byte[32];
    using var rng = RandomNumberGenerator.Create();
    rng.GetBytes(randomNumber);
    return new RefreshToken { Token = Convert.ToBase64String(randomNumber), ExpiryDate = DateTime.UtcNow.AddDays(7) };
}

public class AuthOptions
{
    public const string ISSUER = "ITStepAuthServer";
    public const string AUDIENCE = "ITStepAuthClient";
    private const string KEY = "mysupersecret_secretsecretsecretkey!123";
    public static SymmetricSecurityKey GetSymmetricSecurityKey() => new(Encoding.UTF8.GetBytes(KEY));
}

record User(string Email, string Password);
record UserLogin(string Email, string Password);
record RefreshToken(string Token, string UserEmail, DateTime ExpiryDate);
record RefreshRequest(string RefreshToken);
```

**Результат**:
*Зображення: POST-запит до `/login` повертає JSON із `access_token` і `refresh_token`. Запит до `/data` із заголовком `Authorization: Bearer <access_token>` повертає дані. Прострочений токен повертає 401, після чого клієнт оновлює токен через `/refresh`.*

**Пояснення "під капотом"**:
- **Валідація токена**: `JwtBearerHandler` декодує токен, перевіряє підпис за допомогою ключа (`IssuerSigningKey`), валідує емітента (`Issuer`), аудиторію (`Audience`) і час дії (`exp`).
- **Claims із Payload**: Claims із Payload (наприклад, `sub`, `role`) конвертуються в об’єкти `Claim` і додаються до `ClaimsIdentity`.
- **Refresh-токени**: Зберігаються окремо (в пам’яті або базі даних) і перевіряються за терміном дії та відповідністю користувачу.
- **ClockSkew**: Вимкнення (`TimeSpan.Zero`) забезпечує точну перевірку часу дії токена, без допуску на неточність.

## Безпека та рекомендації
1. **Куки**:
   - Увімкніть `HttpOnly`, `Secure` і `SameSite=Strict` для захисту від XSS і CSRF.
   - Використовуйте `SlidingExpiration` для автоматичного продовження сесії.
   - Шифруйте куки за допомогою надійного `DataProtectionProvider`.

2. **JWT**:
   - Використовуйте короткий час дії для `access_token` (наприклад, 5-15 хвилин).
   - Зберігайте `refresh_token` у безпечному сховищі (наприклад, базі даних із захистом).
   - Використовуйте алгоритм `HmacSha256` або сильніший (наприклад, `RS256` для асиметричного шифрування).

3. **Загальні**:
   - Завжди використовуйте HTTPS для захисту даних у транзиті.
   - Впроваджуйте анти-CSRF-токени для POST-запитів.
   - Регулярно оновлюйте ключі шифрування та ротуйте refresh-токени.


## Аутентифікація з JWT-токенами

JSON Web Tokens (JWT) — це стандарт для передачі даних про користувача у зашифрованому вигляді. JWT складається з трьох частин: **Header**, **Payload** і **Signature**, розділених крапками (`.`). Вони використовуються для stateless-аутентифікації, де сервер не зберігає сесію.

### Налаштування JWT-аутентифікації
Для використання JWT необхідно додати NuGet-пакет:

```bash
dotnet add package Microsoft.AspNetCore.Authentication.JwtBearer
```

Приклад мінімального API з JWT-аутентифікацією та токенами оновлення:

```csharp
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// Умовна база даних користувачів і токенів оновлення
var users = new List<User>
{
    new User("<EMAIL>", "12345"),
    new User("<EMAIL>", "55555")
};
var refreshTokens = new Dictionary<string, RefreshToken>();

// Налаштування JWT-аутентифікації
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidIssuer = AuthOptions.ISSUER,
            ValidateAudience = true,
            ValidAudience = AuthOptions.AUDIENCE,
            ValidateLifetime = true,
            IssuerSigningKey = AuthOptions.GetSymmetricSecurityKey(),
            ValidateIssuerSigningKey = true
        };
    });
builder.Services.AddAuthorization();

var app = builder.Build();

app.UseAuthentication();
app.UseAuthorization();

// Логін із генерацією JWT і refresh-токена
app.MapPost("/login", (UserLogin loginData) =>
{
    var user = users.FirstOrDefault(u => u.Email == loginData.Email && u.Password == loginData.Password);
    if (user is null) return Results.Unauthorized();

    var claims = new List<Claim> { new Claim(ClaimTypes.Name, user.Email) };
    var accessToken = GenerateJwtToken(claims, TimeSpan.FromMinutes(2));
    var refreshToken = GenerateRefreshToken();
    refreshTokens[refreshToken.Token] = new RefreshToken { Token = refreshToken.Token, UserEmail = user.Email, ExpiryDate = DateTime.UtcNow.AddDays(7) };

    return Results.Json(new { access_token = accessToken, refresh_token = refreshToken.Token });
});

// Оновлення токена
app.MapPost("/refresh", (RefreshRequest request) =>
{
    if (!refreshTokens.TryGetValue(request.RefreshToken, out var storedToken) || storedToken.ExpiryDate < DateTime.UtcNow)
        return Results.Unauthorized();

    var user = users.FirstOrDefault(u => u.Email == storedToken.UserEmail);
    if (user is null) return Results.Unauthorized();

    var claims = new List<Claim> { new Claim(ClaimTypes.Name, user.Email) };
    var newAccessToken = GenerateJwtToken(claims, TimeSpan.FromMinutes(2));
    var newRefreshToken = GenerateRefreshToken();
    refreshTokens.Remove(request.RefreshToken);
    refreshTokens[newRefreshToken.Token] = new RefreshToken { Token = newRefreshToken.Token, UserEmail = user.Email, ExpiryDate = DateTime.UtcNow.AddDays(7) };

    return Results.Json(new { access_token = newAccessToken, refresh_token = newRefreshToken.Token });
});

// Захищений ресурс
app.MapGet("/data", [Authorize] () => new { message = "Вітаємо в захищеному ресурсі!" });

app.Run();

string GenerateJwtToken(List<Claim> claims, TimeSpan lifetime)
{
    var jwt = new JwtSecurityToken(
        issuer: AuthOptions.ISSUER,
        audience: AuthOptions.AUDIENCE,
        claims: claims,
        expires: DateTime.UtcNow.Add(lifetime),
        signingCredentials: new SigningCredentials(AuthOptions.GetSymmetricSecurityKey(), SecurityAlgorithms.HmacSha256));
    return new JwtSecurityTokenHandler().WriteToken(jwt);
}

RefreshToken GenerateRefreshToken()
{
    var randomNumber = new byte[32];
    using var rng = RandomNumberGenerator.Create();
    rng.GetBytes(randomNumber);
    return new RefreshToken { Token = Convert.ToBase64String(randomNumber), ExpiryDate = DateTime.UtcNow.AddDays(7) };
}

public class AuthOptions
{
    public const string ISSUER = "ITStepAuthServer";
    public const string AUDIENCE = "ITStepAuthClient";
    private const string KEY = "mysupersecret_secretsecretsecretkey!123";
    public static SymmetricSecurityKey GetSymmetricSecurityKey() => new(Encoding.UTF8.GetBytes(KEY));
}

record User(string Email, string Password);
record UserLogin(string Email, string Password);
record RefreshToken(string Token, string UserEmail, DateTime ExpiryDate);
record RefreshRequest(string RefreshToken);
```

**Результат JWT-аутентифікації**:
*Зображення: POST-запит до `/login` повертає JSON із `access_token` і `refresh_token`. Запит до `/data` із заголовком `Authorization: Bearer <access_token>` повертає захищені дані. Некоректний токен повертає код 401. Запит до `/refresh` оновлює токени.*

**Пояснення**:
- `AddJwtBearer()` налаштовує валідацію JWT-токенів, включаючи перевірку емітента, аудиторії, часу життя та ключа підпису.
- Токен оновлення (`refresh_token`) дозволяє отримати новий `access_token` без повторного введення логіна/пароля.
- `GenerateRefreshToken()` створює криптографічно безпечний токен оновлення.
- Токени зберігаються в пам’яті (`refreshTokens`), але в реальному додатку слід використовувати базу даних.

### Клієнт JavaScript для JWT
Приклад клієнта для взаємодії з JWT-аутентифікацією:

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>ITStep</title>
</head>
<body>
    <div id="userInfo" style="display:none;">
        <p>Вітаємо, <span id="userName"></span>!</p>
        <button id="logOut">Вийти</button>
    </div>
    <div id="loginForm">
        <h3>Вхід</h3>
        <p>
            <label>Email</label><br />
            <input type="email" id="email" />
        </p>
        <p>
            <label>Пароль</label><br />
            <input type="password" id="password" />
        </p>
        <button id="submitLogin">Увійти</button>
    </div>
    <p>
        <button id="getData">Отримати дані</button>
    </p>
    <script>
        const tokenKey = "accessToken";
        const refreshTokenKey = "refreshToken";

        document.getElementById("submitLogin").addEventListener("click", async () => {
            const response = await fetch("/login", {
                method: "POST",
                headers: { "Accept": "application/json", "Content-Type": "application/json" },
                body: JSON.stringify({
                    email: document.getElementById("email").value,
                    password: document.getElementById("password").value
                })
            });

            if (response.ok) {
                const data = await response.json();
                document.getElementById("userName").innerText = data.username || document.getElementById("email").value;
                document.getElementById("userInfo").style.display = "block";
                document.getElementById("loginForm").style.display = "none";
                sessionStorage.setItem(tokenKey, data.access_token);
                sessionStorage.setItem(refreshTokenKey, data.refresh_token);
            } else {
                console.error("Помилка: ", response.status);
            }
        });

        document.getElementById("getData").addEventListener("click", async () => {
            let token = sessionStorage.getItem(tokenKey);
            let response = await fetch("/data", {
                method: "GET",
                headers: {
                    "Accept": "application/json",
                    "Authorization": `Bearer ${token}`
                }
            });

            if (response.status === 401) {
                const refreshToken = sessionStorage.getItem(refreshTokenKey);
                const refreshResponse = await fetch("/refresh", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ refreshToken })
                });

                if (refreshResponse.ok) {
                    const refreshData = await refreshResponse.json();
                    sessionStorage.setItem(tokenKey, refreshData.access_token);
                    sessionStorage.setItem(refreshTokenKey, refreshData.refresh_token);
                    token = refreshData.access_token;
                    response = await fetch("/data", {
                        method: "GET",
                        headers: {
                            "Accept": "application/json",
                            "Authorization": `Bearer ${token}`
                        }
                    });
                }
            }

            if (response.ok) {
                const data = await response.json();
                alert(data.message);
            } else {
                console.error("Помилка: ", response.status);
            }
        });

        document.getElementById("logOut").addEventListener("click", () => {
            document.getElementById("userName").innerText = "";
            document.getElementById("userInfo").style.display = "none";
            document.getElementById("loginForm").style.display = "block";
            sessionStorage.removeItem(tokenKey);
            sessionStorage.removeItem(refreshTokenKey);
        });
    </script>
</body>
</html>
```

**Результат клієнта JavaScript**:
*Зображення: Веб-сторінка показує форму логіну. Після входу відображається ім’я користувача і кнопка для отримання даних. Запит до `/data` із токеном повертає повідомлення. При простроченому токені клієнт автоматично оновлює його через `/refresh`.*

## Авторизація на основі ролей

Авторизація на основі ролей дозволяє обмежувати доступ до ресурсів залежно від ролі користувача (наприклад, "admin" або "user").

### Приклад авторизації за ролями
Оновлений приклад із ролями:

```csharp
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

var builder = WebApplication.CreateBuilder(args);

// Умовна база даних користувачів
var adminRole = new Role("admin");
var userRole = new Role("user");
var users = new List<User>
{
    new User("<EMAIL>", "12345", adminRole),
    new User("<EMAIL>", "55555", userRole)
};

builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options =>
    {
        options.LoginPath = "/login";
        options.LogoutPath = "/logout";
        options.AccessDeniedPath = "/access-denied";
        options.Cookie.HttpOnly = true;
        options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
        options.Cookie.SameSite = SameSiteMode.Strict;
    });
builder.Services.AddAuthorization();

var app = builder.Build();

app.UseAuthentication();
app.UseAuthorization();

// Форма логіну
app.MapGet("/login", async context =>
{
    context.Response.ContentType = "text/html; charset=utf-8";
    string loginForm = @"<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8' />
    <title>ITStep</title>
</head>
<body>
    <h2>Вхід</h2>
    <form method='post'>
        <p>
            <label>Email</label><br />
            <input name='email' />
        </p>
        <p>
            <label>Пароль</label><br />
            <input type='password' name='password' />
        </p>
        <input type='submit' value='Увійти' />
    </form>
</body>
</html>";
    await context.Response.WriteAsync(loginForm);
});

// Обробка логіну
app.MapPost("/login", async (string? returnUrl, HttpContext context) =>
{
    var form = context.Request.Form;
    if (!form.ContainsKey("email") || !form.ContainsKey("password"))
        return Results.BadRequest("Email або пароль не вказані");

    string email = form["email"];
    string password = form["password"];

    var user = users.FirstOrDefault(u => u.Email == email && u.Password == password);
    if (user is null) return Results.Unauthorized();

    var claims = new List<Claim>
    {
        new Claim(ClaimsIdentity.DefaultNameClaimType, user.Email),
        new Claim(ClaimsIdentity.DefaultRoleClaimType, user.Role.Name)
    };
    var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
    await context.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(claimsIdentity));

    return Results.Redirect(returnUrl ?? "/");
});

// Вихід
app.MapGet("/logout", async (HttpContext context) =>
{
    await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
    return Results.Redirect("/login");
});

// Сторінка відмови в доступі
app.MapGet("/access-denied", () => Results.Text("Доступ заборонено", statusCode: 403));

// Захищені ресурси
app.MapGet("/admin", [Authorize(Roles = "admin")] () => "Панель адміністратора");
app.MapGet("/", [Authorize(Roles = "admin,user")] (HttpContext context) =>
{
    var login = context.User.FindFirst(ClaimsIdentity.DefaultNameClaimType)?.Value;
    var role = context.User.FindFirst(ClaimsIdentity.DefaultRoleClaimType)?.Value;
    return $"Ім'я: {login}\nРоль: {role}";
});

app.Run();

record User(string Email, string Password, Role Role);
record Role(string Name);
```

**Результат авторизації за ролями**:
*Зображення: Користувач із роллю "user" отримує доступ до `/`, але перенаправляється на `/access-denied` при запиті до `/admin`. Користувач із роллю "admin" має доступ до обох ресурсів.*

**Пояснення**:
- Ролі додаються до `ClaimsIdentity` через `ClaimsIdentity.DefaultRoleClaimType`.
- Атрибут `[Authorize(Roles = "admin")]` обмежує доступ до `/admin` лише для адміністраторів.
- `AccessDeniedPath` перенаправляє користувачів без необхідних прав на сторінку відмови.

## Авторизація на основі Claims

Авторизація на основі claims дозволяє обмежувати доступ на основі будь-яких даних користувача, таких як місто проживання, компанія чи вік.

### Приклад авторизації за claims
Приклад із політиками для міста та компанії:

```csharp
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

var builder = WebApplication.CreateBuilder(args);

// Умовна база даних користувачів
var users = new List<User>
{
    new User("<EMAIL>", "12345", "Лондон", "Microsoft"),
    new User("<EMAIL>", "55555", "Лондон", "Google"),
    new User("<EMAIL>", "11111", "Берлін", "Microsoft")
};

builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options =>
    {
        options.LoginPath = "/login";
        options.AccessDeniedPath = "/access-denied";
        options.Cookie.HttpOnly = true;
        options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
        options.Cookie.SameSite = SameSiteMode.Strict;
    });
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("OnlyForLondon", policy =>
        policy.RequireClaim(ClaimTypes.Locality, "Лондон", "London"));
    options.AddPolicy("OnlyForMicrosoft", policy =>
        policy.RequireClaim("company", "Microsoft"));
});

var app = builder.Build();

app.UseAuthentication();
app.UseAuthorization();

// Форма логіну
app.MapGet("/login", async context =>
{
    context.Response.ContentType = "text/html; charset=utf-8";
    string loginForm = @"<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8' />
    <title>ITStep</title>
</head>
<body>
    <h2>Вхід</h2>
    <form method='post'>
        <p>
            <label>Email</label><br />
            <input name='email' />
        </p>
        <p>
            <label>Пароль</label><br />
            <input type='password' name='password' />
        </p>
        <input type='submit' value='Увійти' />
    </form>
</body>
</html>";
    await context.Response.WriteAsync(loginForm);
});

// Обробка логіну
app.MapPost("/login", async (string? returnUrl, HttpContext context) =>
{
    var form = context.Request.Form;
    if (!form.ContainsKey("email") || !form.ContainsKey("password"))
        return Results.BadRequest("Email або пароль не вказані");

    string email = form["email"];
    string password = form["password"];

    var user = users.FirstOrDefault(u => u.Email == email && u.Password == password);
    if (user is null) return Results.Unauthorized();

    var claims = new List<Claim>
    {
        new Claim(ClaimTypes.Name, user.Email),
        new Claim(ClaimTypes.Locality, user.City),
        new Claim("company", user.Company)
    };
    var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
    await context.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(claimsIdentity));

    return Results.Redirect(returnUrl ?? "/");
});

// Вихід
app.MapGet("/logout", async (HttpContext context) =>
{
    await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
    return Results.Redirect("/login");
});

// Сторінка відмови в доступі
app.MapGet("/access-denied", () => Results.Text("Доступ заборонено", statusCode: 403));

// Захищені ресурси
app.MapGet("/london", [Authorize(Policy = "OnlyForLondon")] () => "Ви живете в Лондоні!");
app.MapGet("/microsoft", [Authorize(Policy = "OnlyForMicrosoft")] () => "Ви працюєте в Microsoft!");
app.MapGet("/", [Authorize] (HttpContext context) =>
{
    var login = context.User.FindFirst(ClaimTypes.Name)?.Value;
    var city = context.User.FindFirst(ClaimTypes.Locality)?.Value;
    var company = context.User.FindFirst("company")?.Value;
    return $"Ім'я: {login}\nМісто: {city}\nКомпанія: {company}";
});

app.Run();

record User(string Email, string Password, string City, string Company);
```

**Результат авторизації за claims**:
*Зображення: Користувач із Лондона має доступ до `/london`, але не до `/microsoft`, якщо працює в Google. Користувач із Microsoft має доступ до `/microsoft`.*

**Пояснення**:
- Політики `OnlyForLondon` і `OnlyForMicrosoft` перевіряють наявність claims із відповідними значеннями.
- Claims додаються під час логіну через `ClaimsIdentity`.

## Кастомні обмеження авторизації

Для складніших сценаріїв, наприклад, обмеження за віком, можна створювати власні обмеження авторизації.

### Приклад обмеження за віком
Приклад із кастомним обмеженням за віком:

```csharp
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

var builder = WebApplication.CreateBuilder(args);

// Умовна база даних користувачів
var users = new List<User>
{
    new User("<EMAIL>", "12345", 1984),
    new User("<EMAIL>", "55555", 2006)
};

builder.Services.AddTransient<IAuthorizationHandler, AgeHandler>();
builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options =>
    {
        options.LoginPath = "/login";
        options.AccessDeniedPath = "/access-denied";
        options.Cookie.HttpOnly = true;
        options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
        options.Cookie.SameSite = SameSiteMode.Strict;
    });
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("AgeLimit", policy => policy.Requirements.Add(new AgeRequirement(18)));
});

var app = builder.Build();

app.UseAuthentication();
app.UseAuthorization();

// Форма логіну
app.MapGet("/login", async context =>
{
    context.Response.ContentType = "text/html; charset=utf-8";
    string loginForm = @"<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8' />
    <title>ITStep</title>
</head>
<body>
    <h2>Вхід</h2>
    <form method='post'>
        <p>
            <label>Email</label><br />
            <input name='email' />
        </p>
        <p>
            <label>Пароль</label><br />
            <input type='password' name='password' />
        </p>
        <input type='submit' value='Увійти' />
    </form>
</body>
</html>";
    await context.Response.WriteAsync(loginForm);
});

// Обробка логіну
app.MapPost("/login", async (string? returnUrl, HttpContext context) =>
{
    var form = context.Request.Form;
    if (!form.ContainsKey("email") || !form.ContainsKey("password"))
        return Results.BadRequest("Email або пароль не вказані");

    string email = form["email"];
    string password = form["password"];

    var user = users.FirstOrDefault(u => u.Email == email && u.Password == password);
    if (user is null) return Results.Unauthorized();

    var claims = new List<Claim>
    {
        new Claim(ClaimTypes.Name, user.Email),
        new Claim(ClaimTypes.DateOfBirth, user.Year.ToString())
    };
    var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
    await context.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(claimsIdentity));

    return Results.Redirect(returnUrl ?? "/");
});

// Вихід
app.MapGet("/logout", async (HttpContext context) =>
{
    await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
    return Results.Redirect("/login");
});

// Сторінка відмови в доступі
app.MapGet("/access-denied", () => Results.Text("Доступ заборонено", statusCode: 403));

// Захищені ресурси
app.MapGet("/age", [Authorize(Policy = "AgeLimit")] () => "Віковий ліміт пройдено!");
app.MapGet("/", [Authorize] (HttpContext context) =>
{
    var login = context.User.FindFirst(ClaimTypes.Name)?.Value;
    var year = context.User.FindFirst(ClaimTypes.DateOfBirth)?.Value;
    return $"Ім'я: {login}\nРік народження: {year}";
});

app.Run();

// Модель даних
record User(string Email, string Password, int Year);

// Обмеження за віком
class AgeRequirement : IAuthorizationRequirement
{
    public int Age { get; }
    public AgeRequirement(int age) => Age = age;
}

// Обробник обмеження
class AgeHandler : AuthorizationHandler<AgeRequirement>
{
    protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, AgeRequirement requirement)
    {
        var yearClaim = context.User.FindFirst(ClaimTypes.DateOfBirth);
        if (yearClaim != null && int.TryParse(yearClaim.Value, out var year))
        {
            if ((DateTime.Now.Year - year) >= requirement.Age)
            {
                context.Succeed(requirement);
            }
        }
        return Task.CompletedTask;
    }
}
```

**Результат кастомного обмеження**:
*Зображення: Користувач 1984 року народження має доступ до `/age`, а користувач 2006 року перенаправляється на `/access-denied`.*

**Пояснення**:
- `AgeRequirement` задає мінімальний вік.
- `AgeHandler` перевіряє claim `DateOfBirth` і обчислює вік користувача.
- Політика `AgeLimit` застосовує це обмеження до ресурсу `/age`.

## Висновок

ASP.NET Core надає потужні інструменти для реалізації аутентифікації та авторизації, включаючи підтримку куки, JWT-токенів, ролей, claims і кастомних обмежень. Використання `Results` API спрощує повернення HTTP-відповідей, а middleware `AuthenticationMiddleware` і `AuthorizationMiddleware` забезпечують гнучку інтеграцію. Для підвищення безпеки рекомендується:
- Використовувати HTTPS і налаштування `HttpOnly`, `Secure` та `SameSite` для куки.
- Впроваджувати анти-CSRF-токени для форм.
- Зберігати refresh-токени в безпечному сховищі та використовувати короткий час життя для JWT.
- Регулярно оновлювати ключі шифрування та перевіряти політики авторизації.

</xaiArtifact>