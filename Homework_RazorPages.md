# Домашнє завдання: Створення веб-додатку "Бібліотека книг"

## Мета завдання
Створити простий вебдодаток для управління бібліотекою книг, використовуючи Razor Pages.

## Технічні вимоги
- ASP.NET Core 8.0
- Razor Pages
- Без бази даних (використовувати статичні дані)

---

## Завдання

### Завдання 1: Створення проекту та Layout
1. Створіть новий проект Razor Pages
2. Створіть базовий Layout з:
   - Заголовком "Бібліотека книг"
   - Простим меню (Головна, Книги)
   - Підвалом

### Завдання 2: Модель книги
Створіть клас `Book`:
```csharp
public class Book
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public string Genre { get; set; } = string.Empty;
    public bool IsAvailable { get; set; } = true;
}
```

### Завдання 3: Головна сторінка
Створіть `Index.cshtml` з:
- Привітальним текстом
- Кількістю книг у бібліотеці

### Завдання 4: Список книг
Створіть сторінку `Books/Index.cshtml`:
- Відображення всіх книг у таблиці
- Статичний список з 3-5 книгами
- Посилання на деталі кожної книги

### Завдання 5: Деталі книги
Створіть сторінку `Books/Details.cshtml`:
- Показ всієї інформації про книгу
- Кнопка "Назад до списку"

### Завдання 6: Додавання книги
Створіть сторінку `Books/Create.cshtml`:
- Форма з полями: Назва, Автор, Жанр
- Базова валідація (Required)
- Збереження в статичний список

---

## Підказки

### Приклад тестових даних:
```csharp
public static List<Book> Books = new()
{
    new Book { Id = 1, Title = "Кобзар", Author = "Тарас Шевченко", Genre = "Поезія" },
    new Book { Id = 2, Title = "Лісова пісня", Author = "Леся Українка", Genre = "Драма" },
    new Book { Id = 3, Title = "Захар Беркут", Author = "Іван Франко", Genre = "Історичний роман" }
};
```

### Корисні tag-хелпери:
- `asp-page="/Books/Index"` - посилання на сторінку
- `asp-route-id="@book.Id"` - передача параметра
- `asp-for="Title"` - прив'язка до моделі

### Структура проекту:
```
MyLibrary/
├── Pages/
│   ├── Shared/_Layout.cshtml
│   ├── Books/
│   │   ├── Index.cshtml
│   │   ├── Details.cshtml
│   │   └── Create.cshtml
│   └── Index.cshtml
└── Models/Book.cs
```

# Домашнє завдання 2: Розширення функціоналу "Бібліотеки книг"

## Мета завдання

Поглибити роботу з Razor Pages, додати редагування та видалення книг, а також впровадити пошук і фільтрацію.

## Технічні вимоги

* ASP.NET Core 8.0
* Razor Pages
* Використання статичного списку (без БД)

---

## Завдання

### Завдання 1: Редагування книги

* Створіть сторінку `Books/Edit.cshtml`
* Додайте форму редагування книги (Title, Author, Genre, IsAvailable)
* Використайте попередньо завантажені дані книги
* Після збереження – повернення до списку

---

### Завдання 2: Видалення книги

* Створіть сторінку `Books/Delete.cshtml`
* Відобразіть інформацію про книгу та кнопку підтвердження "Видалити"
* Реалізуйте видалення книги зі статичного списку
* Додайте посилання "Видалити" в таблиці зі списком книг

---

### Завдання 3: Пошук книг

* На сторінці `Books/Index.cshtml` додайте поле для пошуку (по Title або Author)
* Реалізуйте фільтрацію списку за введеним текстом

---

### Завдання 4: Фільтрація за жанром

* На сторінці `Books/Index.cshtml` додайте випадаючий список жанрів (із книг, що є)
* Додайте можливість показувати тільки книги певного жанру

---

### Завдання 5: Підрахунок доступних книг

* На головній сторінці `Index.cshtml` відобразіть:

  * Загальну кількість книг
  * Кількість доступних книг (`IsAvailable == true`)
  * Кількість недоступних книг

---

## Підказки

* Використовуйте `asp-route-id` для передачі Id книги при редагуванні/видаленні
* Для фільтрації за жанром можна отримати унікальні значення з `Books.Select(b => b.Genre).Distinct()`
* Пошук реалізуйте через LINQ:

  ```csharp
  Books.Where(b => b.Title.Contains(search) || b.Author.Contains(search))
  ```
* Для підрахунку доступних книг:

  ```csharp
  var available = Books.Count(b => b.IsAvailable);
  var unavailable = Books.Count(b => !b.IsAvailable);
  ```
