# Results API в ASP.NET Core

Results API в ASP.NET Core спрощує створення відповідей у мінімальному API, надаючи набір статичних методів у класі `Results` для повернення HTTP-відповідей зі статусними кодами, даними чи перенаправленнями. Цей API дозволяє створювати чіткі, типізовані відповіді, зменшуючи необхідність прямого маніпулювання об’єктом `HttpContext`. У цьому матеріалі ми розглянемо методи Results API, їхнє використання в кінцевих точках і middleware, а також створення власних типів `IResult`.

## Вступ до Results API

Клас `Results` містить методи для створення об’єктів `IResult`, які представляють HTTP-відповіді. Ці методи дозволяють задавати статусні коди, формати даних (JSON, текст, файли), перенаправлення та автентифікаційні дії. Вони особливо корисні в мінімальному API, але також можуть застосовуватися в middleware.

### Основні методи Results API

- **Accepted()**: Повертає код 202 (Запит прийнято до обробки).
- **AcceptedAtRoute()**: Код 202 із маршрутом.
- **BadRequest()**: Код 400 (Некоректний запит).
- **Bytes()**: Відправляє масив байтів.
- **Challenge()**: Викликає автентифікацію (коди 401/403).
- **Conflict()**: Код 409 (Конфлікт).
- **Content()**: Відправляє текстовий вміст.
- **Created()**: Код 201 (Створено).
- **CreatedAtRoute()**: Код 201 із маршрутом.
- **File()**: Відправляє файл.
- **Forbid()**: Код 403 (Заборонено).
- **Json()**: Відправляє JSON.
- **LocalRedirect()**: Локальне перенаправлення (301/302).
- **NoContent()**: Код 204 (Без вмісту).
- **NotFound()**: Код 404 (Не знайдено).
- **Ok()**: Код 200 (Успіх).
- **Problem()**: Повертає `ProblemDetails` для помилок.
- **Redirect()**: Перенаправлення на будь-який URL.
- **RedirectToRoute()**: Перенаправлення на маршрут.
- **SignIn()**: Виконує вхід користувача.
- **SignOut()**: Виконує вихід користувача.
- **StatusCode()**: Повертає заданий статусний код.
- **Stream()**: Відправляє потік даних.
- **Text()**: Відправляє текст.
- **Unauthorized()**: Код 401 (Неавторизовано).
- **UnprocessableEntity()**: Код 422 (Неприпустимий об’єкт).
- **ValidationProblem()**: Код 400 із деталями валідації.

### Базовий приклад використання
Порівняння прямого повернення рядка та використання `Results.Text`:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/hello", () => Results.Text("Привіт, ITStep!"));
app.MapGet("/", () => "Привіт, ITStep!");

app.Run();
```

**Результат базового прикладу**:
*Зображення: Веб-браузер відображає "Привіт, ITStep!" для запитів до `/` і `/hello`. Заголовок `Content-Length` присутній у відповіді для `/hello`*

**Пояснення**:
- Обидва маршрути повертають однаковий текст, але `Results.Text` додає заголовок `Content-Length`.
- `Results.Text` забезпечує більший контроль над форматом відповіді.

### Використання в окремому методі
Повернення `IResult` із методу:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/hello", SendHello);
app.MapGet("/", () => "Привіт, ITStep!");

app.Run();

IResult SendHello() => Results.Text("Привіт, ITStep!");
```

**Результат із окремим методом**:
*Зображення: Веб-браузер відображає "Привіт, ITStep!" для запиту до `/hello`*

**Пояснення**:
- Метод `SendHello` повертає `IResult`, що робить код більш модульним.

### Використання в middleware
Використання `Results` у middleware:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.Run(async context =>
{
    await Results.Text("Привіт, ITStep!").ExecuteAsync(context);
});

app.Run();
```

**Результат у middleware**:
*Зображення: Веб-браузер відображає "Привіт, ITStep!" для будь-якого запиту*

**Пояснення**:
- Метод `ExecuteAsync` дозволяє інтегрувати `IResult` у middleware.

## Відправка тексту та JSON

### Метод `Content` і `Text`

Обидва методи відправляють текстовий вміст, але дозволяють задавати тип вмісту та кодування.

#### Приклад із `Content`
Код із кодуванням Unicode:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/", () => Results.Content("Привіт, ITStep!", "text/plain", System.Text.Encoding.Unicode));

app.Run();
```

**Результат із `Content`**:
*Зображення: Веб-браузер відображає "Привіт, ITStep!" із кодуванням Unicode*

**Пояснення**:
- Якщо `contentType` не задано, використовується `text/plain; charset=utf-8`.

#### Приклад із `Text`
Код із різними кодуваннями:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/chinese", () => Results.Text("你好", "text/plain", System.Text.Encoding.Unicode));
app.MapGet("/", () => Results.Text("Привіт, ITStep!"));

app.Run();
```

**Результат із `Text`**:
*Зображення: Веб-браузер відображає "你好" для `/chinese` та "Привіт, ITStep!" для `/`*

### Метод `Json`

Метод `Results.Json` серіалізує об’єкти в JSON із можливістю налаштування.

#### Приклад із `Json`
Код із відправкою об’єктів:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/person", () => Results.Json(new Person("Боб", 41)));
app.MapGet("/", () => Results.Json(new { name = "Том", age = 37 }));

app.Run();

record Person(string Name, int Age);
```

**Результат із `Json`**:
*Зображення: Веб-браузер відображає JSON `{ "Name": "Боб", "Age": 41 }` для `/person` та `{ "name": "Том", "age": 37 }` для `/`*

#### Налаштування серіалізації
Код із налаштуванням `JsonSerializerOptions`:

```csharp
using System.Text.Json;
using System.Text.Json.Serialization;

var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/sam", () => Results.Json(
    new Person("Сем", 25),
    new JsonSerializerOptions
    {
        PropertyNameCaseInsensitive = false,
        NumberHandling = JsonNumberHandling.WriteAsString
    }));

app.MapGet("/bob", () => Results.Json(
    new Person("Боб", 41),
    new JsonSerializerOptions(JsonSerializerDefaults.Web)));

app.MapGet("/", () => Results.Json(new Person("Том", 37)));

app.Run();

record Person(string Name, int Age);
```

**Результат із налаштуванням серіалізації**:
*Зображення: Веб-браузер відображає JSON із різними форматами для `/sam`, `/bob` і `/`*

**Пояснення**:
- `PropertyNameCaseInsensitive`: Керує регістром назв властивостей.
- `NumberHandling`: Числа як рядки для `/sam`.
- `JsonSerializerDefaults.Web`: Використовує стандартні налаштування веб-серіалізації.

#### JSON для помилок
Код із помилкою:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/error", () => Results.Json(
    new { message = "Несподівана помилка" },
    statusCode: StatusCodes.Status500InternalServerError));

app.MapGet("/", () => "Привіт, ITStep!");

app.Run();
```

**Результат із JSON-помилкою**:
*Зображення: Веб-браузер відображає JSON `{ "message": "Несподівана помилка" }` із кодом 500 для `/error`*

## Перенаправлення

### Метод `LocalRedirect`

Перенаправлення на локальний маршрут:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/old", () => Results.LocalRedirect("/new", permanent: true));
app.MapGet("/new", () => "Новий маршрут");

app.MapGet("/", () => "Привіт, ITStep!");

app.Run();
```

**Результат із `LocalRedirect`**:
*Зображення: Запит до `/old` перенаправляє на `/new` із кодом 301*

**Пояснення**:
- `permanent: true` встановлює код 301 (постійне перенаправлення).
- `preserveMethod: true` зберігає метод запиту (наприклад, POST).

### Метод `Redirect`

Перенаправлення на зовнішній або локальний URL:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/old", () => Results.Redirect("https://itstep.org"));
app.MapGet("/", () => "Привіт, ITStep!");

app.Run();
```

**Результат із `Redirect`**:
*Зображення: Запит до `/old` перенаправляє на `https://itstep.org` із кодом 302*

### Метод `RedirectToRoute`

Перенаправлення на іменований маршрут:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/user/{id}", (int id) => $"Користувач {id}").WithName("user-profile");
app.MapGet("/old-profile/{id}", (int id) => Results.RedirectToRoute("user-profile", new { id }));

app.MapGet("/", () => "Привіт, ITStep!");

app.Run();
```

**Результат із `RedirectToRoute`**:
*Зображення: Запит до `/old-profile/1` перенаправляє на `/user/1`*

## Відправка статусних кодів

### Метод `StatusCode`

Повернення довільного статусного коду:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/about", () => Results.StatusCode(StatusCodes.Status401Unauthorized));
app.MapGet("/", () => "Привіт, ITStep!");

app.Run();
```

**Результат із `StatusCode`**:
*Зображення: Запит до `/about` повертає код 401*

### Метод `NotFound`

Повернення коду 404:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/about", () => Results.NotFound(new { message = "Ресурс не знайдено" }));
app.MapGet("/", () => "Привіт, ITStep!");

app.Run();
```

**Результат із `NotFound`**:
*Зображення: Запит до `/about` повертає JSON `{ "message": "Ресурс не знайдено" }` із кодом 404*

### Метод `Unauthorized`

Повернення коду 401:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/contacts", () => Results.Unauthorized());
app.MapGet("/", () => "Привіт, ITStep!");

app.Run();
```

**Результат із `Unauthorized`**:
*Зображення: Запит до `/contacts` повертає код 401*

### Метод `BadRequest`

Повернення коду 400:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/contacts/{age:int}", (int age) =>
{
    if (age < 18)
        return Results.BadRequest(new { message = "Недостатній вік" });
    return Results.Content("Доступ дозволено");
});

app.MapGet("/", () => "Привіт, ITStep!");

app.Run();
```

**Результат із `BadRequest`**:
*Зображення: Запит до `/contacts/15` повертає JSON `{ "message": "Недостатній вік" }` із кодом 400*

### Метод `Ok`

Повернення коду 200:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/about", () => Results.Ok("Успішний запит"));
app.MapGet("/contacts", () => Results.Ok(new { message = "Успіх!" }));
app.MapGet("/", () => "Привіт, ITStep!");

app.Run();
```

**Результат із `Ok`**:
*Зображення: Запит до `/contacts` повертає JSON `{ "message": "Успіх!" }` із кодом 200*

## Відправка файлів

### Відправка масиву байтів

Відправка файлу як масиву байтів:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/forest", async () =>
{
    string path = "Files/forest.png";
    byte[] fileContent = await File.ReadAllBytesAsync(path);
    string contentType = "image/png";
    string downloadName = "winter_forest.png";
    return Results.File(fileContent, contentType, downloadName);
});

app.MapGet("/", () => "Привіт, ITStep!");

app.Run();
```

**Результат із файлом як масивом байтів**:
*Зображення: Запит до `/forest` повертає зображення `winter_forest.png`*

### Відправка потоку

Відправка файлу як потоку:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/forest", () =>
{
    string path = "Files/forest.png";
    FileStream fileStream = new FileStream(path, FileMode.Open, FileAccess.Read);
    string contentType = "image/png";
    string downloadName = "winter_forest.png";
    return Results.File(fileStream, contentType, downloadName);
});

app.MapGet("/", () => "Привіт, ITStep!");

app.Run();
```

**Результат із файлом як потоком**:
*Зображення: Запит до `/forest` повертає зображення `winter_forest.png`*

### Відправка за шляхом

Відправка файлу за шляхом із папки `wwwroot`:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/forest", () =>
{
    string path = "Files/forest.png";
    string contentType = "image/png";
    string downloadName = "winter_forest.png";
    return Results.File(path, contentType, downloadName);
});

app.MapGet("/", () => "Привіт, ITStep!");

app.Run();
```

**Результат із файлом за шляхом**:
*Зображення: Запит до `/forest` повертає зображення `winter_forest.png` із папки `wwwroot`*

### Зміна кореневої папки

Використання кастомної папки для файлів:

```csharp
var builder = WebApplication.CreateBuilder(new WebApplicationOptions { WebRootPath = "Files" });
var app = builder.Build();

app.MapGet("/river", () =>
{
    string path = "newRiver.jpg";
    string contentType = "image/jpeg";
    string downloadName = "river.jpg";
    return Results.File(path, contentType, downloadName);
});

app.MapGet("/", () => "Привіт, ITStep!");

app.Run();
```

**Результат із кастомною папкою**:
*Зображення: Запит до `/river` повертає зображення `river.jpg` із папки `Files`*

## Власний тип `IResult`

Для специфічних сценаріїв можна створити власний тип `IResult`, реалізуючи інтерфейс `IResult`.

### Приклад із HTML-вмістом
Клас для відправки HTML:

```csharp
public class HtmlResult : IResult
{
    private readonly string _htmlCode;

    public HtmlResult(string htmlCode)
    {
        _htmlCode = htmlCode;
    }

    public async Task ExecuteAsync(HttpContext context)
    {
        context.Response.ContentType = "text/html; charset=utf-8";
        await context.Response.WriteAsync(_htmlCode);
    }
}

public static class ResultsHtmlExtension
{
    public static IResult Html(this IResultExtensions ext, string htmlCode) => new HtmlResult(htmlCode);
}
```

Код програми:

```csharp
var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/", () => Results.Extensions.Html(@"<!DOCTYPE html>
<html>
<head>
    <title>ITStep</title>
    <meta charset='utf-8' />
</head>
<body>
    <h1>Привіт, ITStep!</h1>
</body>
</html>"));

app.Run();
```

**Результат із власним `IResult`**:
*Зображення: Веб-браузер відображає HTML-сторінку з заголовком "Привіт, ITStep!"*

**Пояснення**:
- Клас `HtmlResult` реалізує `IResult` і встановлює тип вмісту `text/html`.
- Метод розширення `Html` спрощує використання `HtmlResult`.

## Практичний сценарій: API для управління користувачами

Приклад API із використанням Results API для створення, отримання та обробки помилок:

```csharp
using System.Text.Json;

var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

var users = new List<User> { new User(1, "Том", 37) };

app.MapGet("/users", () => Results.Json(users));

app.MapGet("/users/{id}", (int id) =>
{
    var user = users.FirstOrDefault(u => u.Id == id);
    return user != null ? Results.Ok(user) : Results.NotFound(new { message = $"Користувач із id {id} не знайдено" });
});

app.MapPost("/users", (User user) =>
{
    if (string.IsNullOrEmpty(user.Name))
        return Results.BadRequest(new { message = "Ім’я не може бути порожнім" });
    
    user.Id = users.Max(u => u.Id) + 1;
    users.Add(user);
    return Results.Created($"/users/{user.Id}", user);
});

app.MapGet("/", () => "Привіт, ITStep!");

app.Run();

record User(int Id, string Name, int Age);
```

**Результат API**:
*Зображення: Запит до `/users` повертає JSON із списком користувачів, `/users/1` повертає користувача, `/users/999` повертає 404, POST до `/users` створює нового користувача з кодом 201*

**Пояснення**:
- `Results.Json` для списку користувачів.
- `Results.Ok` і `Results.NotFound` для отримання користувача.
- `Results.Created` для створення користувача.

## Висновок

Results API в ASP.NET Core забезпечує зручний спосіб створення HTTP-відповідей із різними статусними кодами, даними та перенаправленнями. Методи, такі як `Json`, `Text`, `File` і `Redirect`, спрощують роботу з відповідями, а власні реалізації `IResult` дозволяють розширити функціонал. У порівнянні з прямим маніпулюванням `HttpContext`, Results API підвищує читабельність і типобезпеку коду. У наступних темах можна розглянути інтеграцію Results API з автентифікацією або створення складних сценаріїв API.

</xaiArtifact>